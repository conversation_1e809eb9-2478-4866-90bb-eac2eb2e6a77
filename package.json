{"name": "ap-platform-scripts", "version": "1.0.0", "private": true, "scripts": {"audit:server": "(cd server && npm run audit)", "audit:player": "(cd player && npm run audit)", "audit:admin": "(cd admin && npm run audit)", "audit:worker": "(cd player/cloudflare-worker && npm run audit)", "audit:all": "npm run audit:server && npm run audit:player && npm run audit:admin && npm run audit:worker", "audit": "npm run audit:all", "lint:server": "npm --prefix server run lint", "lint:player": "npm --prefix player run lint", "lint:admin": "npm --prefix admin run lint", "lint:all": "npm run lint:server && npm run lint:player && npm run lint:admin", "lint": "npm run lint:all", "typecheck:server": "npm --prefix server run typecheck", "typecheck:player": "npm --prefix player run typecheck", "typecheck:admin": "npm --prefix admin run typecheck", "typecheck:worker": "npm --prefix player/cloudflare-worker run typecheck", "typecheck:all": "npm run typecheck:server && npm run typecheck:player && npm run typecheck:admin && npm run typecheck:worker", "typecheck": "npm run typecheck:all", "test:server": "npm --prefix server run test", "test:player": "npm --prefix player run test", "test:admin": "npm --prefix admin run test", "test:all": "npm run test:server && npm run test:player && npm run test:admin", "test": "npm run test:all"}}