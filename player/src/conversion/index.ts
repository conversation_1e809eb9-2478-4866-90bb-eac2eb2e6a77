import { getWithExpiry } from "@player/core/core.storage";


interface ILineItem {
	quantity: number;
}

export interface IShopify {
	checkout: {
		line_items: ILineItem[];
	};
}

declare let Shopify: IShopify;

export const initializeConversion = async (): Promise<void> => {
	try {
		const apDataObject = getWithExpiry("apDataObject");
		if (!apDataObject){
			return;
		}


		const accountId = apDataObject.accountId;
		const gpSessionId = apDataObject.gpSessionId;

		let orderItemsCount = 0;
		Shopify.checkout.line_items.map((item: ILineItem) => {
			orderItemsCount = orderItemsCount + item.quantity;
		});

		const bodyFormData = new FormData();

		bodyFormData.append("accountId", accountId);
		bodyFormData.append("userSessionId", gpSessionId);
		bodyFormData.append("orderItemsCount", orderItemsCount.toString());

		apDataObject.collections.map((id: string) => {
			bodyFormData.append("collectionIds[]", id);
		});

		localStorage.removeItem("apDataObject");

		fetch(
			process.env.GP_SERVER_API_ENDPOINT + "/api/metrics/conversion",
			{
				method: "POST",
				body: bodyFormData,
				headers: {
					"x-api-version": "1"
				}
			}
		);

	} catch (error: unknown) {
		console.error(error);
	}
};

document.addEventListener("DOMContentLoaded", initializeConversion);
