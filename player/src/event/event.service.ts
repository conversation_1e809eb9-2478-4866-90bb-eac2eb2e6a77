import {
	appLog,
	LogScope,
	validateBackslash,
	readAppParams,
	SnippetTypeEnum
} from "@player/app";
import { startCTAEngagement } from "@player/core/core.util";

import {
	IEventInput,
	IEventPayload
} from "./event.interface";
import { EventNameEnum } from "./event.enum";

import {
	getEventDimensions,
	getDevice
} from "./event.util";

import { SessionManagerSingleton } from "@player/session";

export const buildEventPayload = (input: IEventInput): IEventPayload => {
	const urlParams = readAppParams();
	const sessionManager = SessionManagerSingleton.getInstance();
	const sessionObj = sessionManager.getSession();

	const sessionId = urlParams?.gpSession || sessionObj.id;
	const fingerPrint = sessionObj?.fingerprint || urlParams?.gpFingerprint;
	const accountId = input.accountId;
	const collectionId = input.collectionId;

	const snippetType = <SnippetTypeEnum>input.snippet?.type || urlParams?.snippetType;

	if (!accountId) {
		throw new Error(`AccountId is undefined | eventName: ${input.eventName}`);
	}

	const eventDimensions = getEventDimensions(collectionId);

	const eventPayload: IEventPayload = {
		eventName: input.eventName,
		appId: "gp-player",
		accountId: accountId,
		user: {
			sessionId,
			...(fingerPrint && { fingerprint: fingerPrint })
		},
		device: getDevice(),
		eventDimensions,
		...(input.appSessionId && { appSessionId: input.appSessionId }),
		...(snippetType && { snippet: { type: snippetType } })
	};

	if (input.isShareMode) {
		eventPayload.isShareMode = input.isShareMode;
	}

	const videoId = input.videoId || input.sliderVideoPlayer?.videoId;
	if (input.sliderVideoPlayer) {
		eventPayload.eventDimensions.videoSecondsLength = input.sliderVideoPlayer.totalSeconds;
		eventPayload.eventDimensions.videoSecondsPosition = input.sliderVideoPlayer.playedSeconds;
		eventPayload.eventDimensions.videoPercentagePlayed = input.sliderVideoPlayer.playedPercent;
		eventPayload.eventDimensions.videoState = input.sliderVideoPlayer.videoState;
		eventPayload.playId = input.sliderVideoPlayer.playId;
	}
	if (videoId) {
		eventPayload.eventDimensions.videoId = videoId;
	}

	if (input.endedMethod) {
		eventPayload.eventDimensions.endedMethod = input.endedMethod;
	}
	if (input.videoPlayStatus) {
		eventPayload.eventDimensions.videoPlayStatus = input.videoPlayStatus;
	}
	if (input.productActionedURL) {
		eventPayload.eventDimensions.productActionedURL = input.productActionedURL;
	}
	if (input.productId) {
		eventPayload.eventDimensions.productId = input.productId;
	}

	return eventPayload;
};


const postEvent = async (eventPayload: IEventPayload): Promise<void> => {
	appLog({
		message: `Sending event: ${eventPayload.eventName}`,
		data: eventPayload,
		scope: LogScope.INFO
	});
	const API_ENDPOINT = process.env.GP_SERVER_API_ENDPOINT;
	if (!API_ENDPOINT) {
		throw new Error("Missing API_ENDPOINT.");
	}
	const url = validateBackslash(API_ENDPOINT) + "api/event/buffer";

	const response = await fetch(url, {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			"x-api-version": "1"
		},
		body: JSON.stringify(eventPayload)
	});


	if (response.ok) {
		if (eventPayload.eventDimensions.collectionId &&
			eventPayload.eventName === EventNameEnum.THUMBNAIL_CTA_PRESS) {
			startCTAEngagement(eventPayload.user.sessionId,
				eventPayload.accountId,
				eventPayload.eventDimensions.collectionId);
		}
	} else {
		throw new Error(`postEvent response is not ok, status=${response.status}`);
	}
};

export const sendAppEvent = async (input: IEventInput, isAppInPreviewMode = false): Promise<unknown> => {
	try {
		if (isAppInPreviewMode) {
			return;
		}
		const eventPayload = buildEventPayload(input);
		await postEvent(eventPayload);
	} catch (error: unknown) {
		appLog({
			message: (error as Error).message,
			trace: (error as Error).stack + "\n message.service|sendAppEvent",
			scope: LogScope.ERROR
		});
		return null;
	}
};
