import React, {
	useEffect,
	useState
} from "react";
import styled from "styled-components/macro";
import { useRecoilValue } from "recoil";
import { IconButton } from "@player/components/button/IconButton";
import { BlurBox } from "@player/components/Box";
import { Flex } from "@player/components/Flex";
import { default as VolumeOffIcon } from "@player/assets/icon-volume-off.svg";
import { default as VolumeOnIcon } from "@player/assets/icon-volume-on.svg";
import { default as PauseIcon } from "@player/assets/icon-pause.svg";
import { default as PlayIcon } from "@player/assets/icon-play.svg";
import { default as CCFillIcon } from "@player/assets/icon-cc-fill.svg";
import { default as CCOutlineIcon } from "@player/assets/icon-cc-outline.svg";
import { default as FullScreenIcon } from "@player/assets/icon-full-screen.svg";
import { default as ExitFullScreenIcon } from "@player/assets/icon-exit-full-screen.svg";
import {
	ProgressBar<PERSON>on,
	ProgressBar,
	render<PERSON><PERSON><PERSON><PERSON><PERSON>,
	ProgressBarWrapper
} from "@player/interactive/Components";
import { isPortraitAtom } from "@player/app/app.state";
import { sliderConfigAtom } from "@player/slider/slider.state";
import { isIOSDevice } from "@player/app/app.util";
import { useProgressBar } from "@player/player/useProgressBar";
import { SnippetTypeEnum } from "@player/app/app.interface";


const PanelContainer = styled(Flex)`
	position: relative;
	align-items: center;
`;

interface Props {
	progressBarPercent: number;
	playingAudio: boolean;
	playingVideo: boolean;
	setPlayingAudio: React.Dispatch<React.SetStateAction<boolean>>;
	setPlayingVideo: React.Dispatch<React.SetStateAction<boolean>>;
	playerContainerRef?: React.RefObject<HTMLDivElement>;
	videoDuration: number;
	seekVideo: (time: number) => void;
	setIsFullScreenIOS: React.Dispatch<React.SetStateAction<boolean>>;
	snippetType?: SnippetTypeEnum;
	showCaptionButton: boolean;
	showCaptions: boolean;
	handleCCClick: () => void;
}

export const PlayerControlsPanel: React.FC<Props> = ({
	progressBarPercent,
	playingAudio,
	playingVideo,
	setPlayingAudio,
	setPlayingVideo,
	playerContainerRef,
	videoDuration,
	seekVideo,
	setIsFullScreenIOS,
	snippetType,
	showCaptionButton,
	showCaptions,
	handleCCClick
}) => {
	const isPortrait = useRecoilValue(isPortraitAtom);
	const [isFullscreen, setIsFullscreen] = useState(false);
	const sliderConfig = useRecoilValue(sliderConfigAtom);
	const {
		displayProgressPercent,
		showThumb,
		handleMouseDown,
		handleMouseMove,
		handleTouchStart,
		handleTouchMove,
		handleTouchEnd,
		handleProgressClick
	} = useProgressBar({ videoDuration, progressBarPercent, seekVideo, playingVideo, setPlayingVideo });

	useEffect(() => {
		if (isIOSDevice()) return;
		const handleFullscreenChange = (): void => {
			if (document.fullscreenElement) {
				setIsFullscreen(true);
			} else {
				setIsFullscreen(false);
			}
		};
		document.addEventListener("fullscreenchange", handleFullscreenChange);
		return () => {
			document.removeEventListener("fullscreenchange", handleFullscreenChange);
		};
	}, [setIsFullscreen]);

	const handleEnterFullScreen = (): void => {
		if (isIOSDevice()) {
			setIsFullScreenIOS(true);
		} else if (playerContainerRef?.current) {
			playerContainerRef.current.requestFullscreen().catch((err) => {
				console.error("Error attempting to enable full-screen mode:", err.message);
			});
		}
	};

	const handleExitFullScreen = (event?: React.MouseEvent<HTMLDivElement>): void => {
		event?.stopPropagation();
		if (isIOSDevice()) {
			// eslint-disable-next-line no-console
			console.warn("iOS does not support exiting full-screen mode programmatically.");
		} else if (document.fullscreenElement) {
			document.exitFullscreen();
		}
	};

	return (
		<PanelContainer onClick={(e): void => {e.stopPropagation();}}>
			<BlurBox radius={"0.75rem"} />

			<IconButton svgIcon = {playingVideo ? PauseIcon : PlayIcon}
				backEnabled={false} isPortrait={isPortrait}
				onClick={(): void => {setPlayingVideo((prev)=>!prev);}}/>

			<ProgressBarWrapper
				onMouseDown={handleMouseDown}
				onMouseMove={handleMouseMove}
				onClick={handleProgressClick}
				onTouchStart={handleTouchStart}
				onTouchMove={handleTouchMove}
				onTouchEnd={handleTouchEnd}
			>
				<ProgressBarCon>
					<ProgressBar
						progressBarPercent={displayProgressPercent}
						showThumb={showThumb}
					/>
				</ProgressBarCon>
			</ProgressBarWrapper>

			<IconButton svgIcon = {playingAudio ? VolumeOnIcon : VolumeOffIcon} backEnabled={false}
				isPortrait={isPortrait}
				onClick={(): void => {setPlayingAudio((prev)=>!prev);}}/>

			{showCaptionButton
				&& <IconButton svgIcon={showCaptions ? CCFillIcon : CCOutlineIcon}
					isPortrait={isPortrait}
					backEnabled={false}
					onClick={(e): void => {
						e.stopPropagation();
						handleCCClick();
					}}
					style={{
						marginRight: "0.3em"
					}}
				/>
			}

			{snippetType === SnippetTypeEnum.PLAYER && playerContainerRef?.current && (
				isFullscreen ? (
					<IconButton svgIcon={ExitFullScreenIcon} isPortrait={isPortrait} backEnabled={false}
						onClick={(e): void => handleExitFullScreen(e)}/>
				) : (
					<IconButton
						svgIcon={FullScreenIcon} backEnabled={false} isPortrait={isPortrait}
						onClick={handleEnterFullScreen}/>
				)
			)}
			{!sliderConfig?.hideVanityBranding && renderVanityBrand()}
		</PanelContainer>
	);
};
