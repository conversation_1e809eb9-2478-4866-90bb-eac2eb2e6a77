import React from "react";
import styled from "styled-components/macro";
import { Flex } from "@player/components/Flex";
import { useRecoilValue } from "recoil";
import { LikeVideo } from "@player/like/LikeVideo";
import { PhoneCall } from "@player/phone/PhoneCall";
import { SendEmail } from "@player/email/SendEmail";
import { ProductSlider } from "@player/product/ProductSlider";
import { FrameContainer } from "@player/interactive/Components";
import {
	IVideoData,
	VideoDisplayModeEnum,
	SnippetTypeEnum
} from "@player/app/app.interface";
import {
	isPortraitAtom,
	isControlsVisibleAtom,
	isPlayerControlsVisibleAtom,
	isAppInPreviewModeAtom
} from "@player/app/app.state";
import { sliderConfigAtom } from "@player/slider/slider.state";
import { PlayerControlsPanel } from "@player/player/PlayerControlsPanel";
import { IconButton } from "@player/components/button/IconButton";
import { default as TimesIcon } from "@player/assets/icon-times.svg";
import { PlayerOnsiteNav } from "@player/player/PlayerOnsiteNav";
import { SwipeDirectionEnum } from "@player/slider/slider.enum";

const ControlsWrapper = styled.div<{ visible: boolean }>`
	opacity: ${(props): number => (props.visible ? 1 : 0)};
	transition: opacity 0.7s ease-in-out;
	height: 100%;
`;

const FrameColumn = styled(Flex)`
	flex-direction: column;
	gap: min(0.6em, 0.6rem);
`;

const PlayerFrameColumn = styled(Flex) <{ visible: boolean }>`
	opacity: ${(props): number => (props.visible ? 1 : 0)};
	transition: opacity 0.7s ease-in-out;
	flex-direction: column;
`;

interface Props {
	videoData: IVideoData;
	progressBarPercent: number;
	playingAudio: boolean;
	playingVideo: boolean;
	setPlayingAudio: React.Dispatch<React.SetStateAction<boolean>>;
	setPlayingVideo: React.Dispatch<React.SetStateAction<boolean>>;
	setShowVideoOverlay: React.Dispatch<React.SetStateAction<boolean>>;
	playerContainerRef?: React.RefObject<HTMLDivElement>;
	videoDuration: number;
	seekVideo: (time: number) => void;
	setIsFullScreenIOS: React.Dispatch<React.SetStateAction<boolean>>;
	handleCloseClick: (e: React.MouseEvent<HTMLDivElement>) => void;
	snippetType?: SnippetTypeEnum;
	handleCTAChevronClick?: (dir: SwipeDirectionEnum) => void;
	showCaptions: boolean;
	handleCCClick: () => void;
}

export const PlayerControls: React.FC<Props> = ({
	videoData,
	progressBarPercent,
	playingAudio,
	playingVideo,
	setPlayingAudio,
	setPlayingVideo,
	setShowVideoOverlay,
	playerContainerRef,
	videoDuration,
	seekVideo,
	setIsFullScreenIOS,
	handleCloseClick,
	snippetType,
	handleCTAChevronClick,
	showCaptions,
	handleCCClick
}) => {
	const isPortrait = useRecoilValue(isPortraitAtom);
	const isControlsVisible = useRecoilValue(isControlsVisibleAtom);
	const isPlayerControlsVisible = useRecoilValue(isPlayerControlsVisibleAtom);
	const sliderConfig = useRecoilValue(sliderConfigAtom);
	const isAppInPreviewMode = useRecoilValue(isAppInPreviewModeAtom);

	const onProductLinkOpen = (): void => {
		setPlayingVideo(false);
		setShowVideoOverlay(true);
	};

	const shouldShowCloseIcon = (): boolean => {
		return !snippetType && !isAppInPreviewMode && !sliderConfig.isShareMode;
	};


	return (
		<ControlsWrapper visible={isControlsVisible}>
			<FrameContainer justifyContent={"space-between"}>
				<FrameColumn>
					{shouldShowCloseIcon() && <IconButton svgIcon={TimesIcon} style={{ alignSelf: "flex-end" }}
						isPortrait={isPortrait} onClick={(e): void => handleCloseClick(e)} />}
					<PlayerOnsiteNav isOnSite={snippetType === SnippetTypeEnum.ONSITE}
						handleCTAChevronClick={handleCTAChevronClick} />
					<SendEmail
						email={videoData.email}
						fullWidth={isPortrait ||
							(!isPortrait && videoData.videoDisplayMode === VideoDisplayModeEnum.PORTRAIT)}
						videoId={videoData._id}
						videoTitle={videoData.title}
						groupEmail={process.env.GROUP_EMAIL}
					/>
					<PhoneCall phone={videoData.phone} videoId={videoData._id} />
					<LikeVideo videoId={videoData._id} />

				</FrameColumn>

				<FrameColumn>
					<ProductSlider videoData={videoData} onProductLinkOpen={onProductLinkOpen} />
					<PlayerFrameColumn visible={isPlayerControlsVisible}>
						<PlayerControlsPanel
							progressBarPercent={progressBarPercent}
							playingAudio={playingAudio}
							playingVideo={playingVideo}
							setPlayingAudio={setPlayingAudio}
							setPlayingVideo={setPlayingVideo}
							playerContainerRef={playerContainerRef}
							videoDuration={videoDuration}
							seekVideo={seekVideo}
							setIsFullScreenIOS={setIsFullScreenIOS}
							snippetType={snippetType}
							showCaptionButton={(videoData.captionData != undefined && videoData.captionData.enabled
								&& videoData.captionData.captionText.length > 0)}
							showCaptions={showCaptions}
							handleCCClick={handleCCClick}
						/>
					</PlayerFrameColumn>
				</FrameColumn>
			</FrameContainer>
		</ControlsWrapper>
	);
};
