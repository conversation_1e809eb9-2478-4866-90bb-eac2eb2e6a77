import React from "react";
import styled from "styled-components/macro";
import { Flex } from "@player/components/Flex";
import { IconButton } from "@player/components/button/IconButton";
import { default as TimesIcon } from "@player/assets/icon-times.svg";

const CloseButtonWrapper = styled(Flex)`
	position: absolute;
	top: -45px;
	right: 0px;
	padding: 0.5rem;
`;

interface Props {
	onClick: (e: React.MouseEvent<HTMLDivElement>) => void;
	isPortrait: boolean | null;
}

export const CloseForm: React.FC<Props> = ({ onClick, isPortrait }) =>
	<CloseButtonWrapper>
		<IconButton svgIcon={TimesIcon} isPortrait={isPortrait} onClick={onClick} style={{ width: "1.8rem" }} />
	</CloseButtonWrapper>;
