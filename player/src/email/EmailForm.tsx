import React from "react";
import { useForm } from "react-hook-form";
import {
	Input,
	TextArea
} from "@player/components/Input";
import { ActionButton } from "@player/components/button/ActionButton";
import styled from "styled-components/macro";
import { Heading } from "@player/components";

const emailPattern = /[a-z0-9._%+-]+@[a-z0-9.-]+.[a-z]{2,20}$/i;

const FormWrapper = styled.form`
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
`;
export interface IEmailForm {
	email: string;
	message: string;
}

interface Props {
	onSubmit: (data: IEmailForm) => void;
}

export const EmailForm: React.FC<Props> = ({ onSubmit }) => {
	const { register, watch, handleSubmit, formState: { errors }, reset } = useForm<IEmailForm>();
	const messageText = watch("message", "");
	const messageCharCount = messageText.length;
	const minMessageLength = 10;

	const submitEmail = handleSubmit((data) => {
		onSubmit(data);
		reset();
	});

	return (
		<FormWrapper onSubmit={submitEmail}>
			<Input
				type="email"
				autoComplete="off"
				hasError={!!errors?.email}
				placeholder={errors.email?.type === "required" ? "Email is required" : "Your email address"}
				{...register("email", {
					required: true,
					pattern: {
						value: emailPattern,
						message: "Invalid email address"
					}
				})}
			/>
			<div style={{ width: "100%" }}>
				<TextArea
					placeholder={errors.message?.type === "required" ? "Message is required" : "Message"}
					hasError={!!errors?.message}
					{...register("message", {
						required: true,
						minLength: {
							value: minMessageLength,
							message: "Message must be at least 10 characters long"
						}
					})}
				/>
				<Heading
					tag="h6"
					style={{
						float: "right",
						color: errors?.message ? "red" : "#aaaaaa"
					}}
				>
					{messageCharCount + (messageCharCount < minMessageLength ? `/${minMessageLength}` : "")}
				</Heading>
			</div>
			<ActionButton variantBtn="secondary" text="Send Email" fullWidth />
		</FormWrapper>
	);
};
