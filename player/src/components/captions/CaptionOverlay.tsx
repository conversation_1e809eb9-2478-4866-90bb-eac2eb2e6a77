import React, {
	useRef,
	useState,
	useMemo,
	useEffect
} from "react";
import { IVideoData } from "@player/app";
import { useTheme } from "styled-components";
import { ITheme } from "@player/theme";

interface Caption {
	timeStamp: number;
	videoData: IVideoData;
	show: boolean;
}

export const CaptionOverlay: React.FC<Caption> = ({ timeStamp, videoData, show = false }) => {

	const xPos = videoData.captionData?.xPos ?? 0;
	const yPos = videoData.captionData?.yPos ?? 1000;
	const textColor = videoData.captionData?.textColor ?? "#FFFFFF";
	const backgroundColor = videoData.captionData?.backgroundColor ?? "#000000";
	const fontSize = videoData.captionData?.fontSize ?? 1;
	const captionText = videoData.captionData?.captionText;

	const showTitle = useMemo(() => videoData.showTitle, [videoData.showTitle]);
	const hasProduct = useMemo(() => videoData.products.length > 0, [videoData.products.length]);

	const divRef = useRef<HTMLDivElement>(null);

	const [position, setPosition] = useState({ x: xPos, y: yPos });
	const [offsetY, setOffsetY] = useState(0);
	const [staticOffsetY, setStaticOffsetY] = useState(0);

	const [height, setHeight] = useState(0);
	const [absWidth, setAbsWidth] = useState(0);

	const textIndex = useRef(0);
	const [prevTimeStamp, setPrevTimeStamp] = useState(0);
	const [text, setText] = useState("");

	const [computedFontSize, setcomputedFontSize] = useState(16);
	const [fontsReady, setFontsReady] = useState(false);

	const theme = useTheme() as ITheme;

	useEffect(() => {
		document.fonts.load(`1rem ${theme.fonts.family}`).then(() => {
			setFontsReady(true);
		});
		return () => setFontsReady(false);
	},
	// eslint-disable-next-line react-hooks/exhaustive-deps
	[]);

	useEffect(() => {
		if (!divRef.current) return;

		const updateFontSize = ():void => {
			if (!divRef.current) return;
			const newSize = parseFloat(getComputedStyle(divRef.current).fontSize);
			setcomputedFontSize(newSize);
		};

		const resizeObserver = new ResizeObserver(updateFontSize);
		resizeObserver.observe(divRef.current);

		updateFontSize();

		return () => resizeObserver.disconnect();
	}, []);

	useEffect(() => {
		if (prevTimeStamp > timeStamp)
			textIndex.current = 0;

		let found = false;
		if (captionText) {
			for (let i = textIndex.current; i < captionText.length; i++) {
				if (timeStamp < captionText[i].startTime)
					break;

				if (timeStamp >= captionText[i].startTime && timeStamp < captionText[i].endTime) {
					found = true;
					setText(captionText[i].text);
					if (i > textIndex.current)
						textIndex.current = i;
					break;
				}
			}
		}

		if (!found)
			setText("");

		setPrevTimeStamp(timeStamp);
	}
	// eslint-disable-next-line react-hooks/exhaustive-deps
	, [timeStamp, captionText]);

	useEffect(() => {
		if (fontsReady && divRef.current && divRef.current.parentElement) {
			//Ensures calculation remains consistent in the event of overflow line-breaks
			const oneLineDiv = divRef.current.cloneNode(true) as HTMLDivElement;

			oneLineDiv.style.whiteSpace = "pre";
			oneLineDiv.style.visibility = "hidden";
			(oneLineDiv.children[0] as HTMLParagraphElement).style.whiteSpace = "unset";

			divRef.current.parentElement.appendChild(oneLineDiv);
			const absRect = oneLineDiv.getBoundingClientRect();
			setAbsWidth(absRect.width);

			divRef.current.parentElement.removeChild(oneLineDiv);
		}
	}, [fontsReady, text, fontSize, computedFontSize, show, videoData.captionData?.enabled]);

	useEffect(() => {
		setPosition(prev => ({ ...prev, x: (xPos ?? 0) - (absWidth / 2) }));
	}, [absWidth, xPos]);

	useEffect(() => {
		if (divRef.current) {
			const rect = divRef.current.getBoundingClientRect();
			setHeight(rect.height);
		}
	}, [absWidth, text, fontSize, computedFontSize, show, videoData.captionData?.enabled]);

	useEffect(() => {
		setPosition(prev => ({ ...prev, y: (yPos ?? 0) - (height / 2) }));
	}, [height, yPos]);

	useEffect(() => {
		//Static Measurements
		const screenPadding = 1;
		const progressBarHeight = 2;
		//Non-Static Measurements
		const progressBarGap = 0.6;
		const soloTitleHeight = 1.17 * 1.2;
		const soloProductHeight = 4.38;
		const productMargin = 1;
		const titleProductGap = 0.65;

		setStaticOffsetY(screenPadding + progressBarHeight);

		if (showTitle && hasProduct) {
			setOffsetY(progressBarGap + soloTitleHeight + soloProductHeight + productMargin + titleProductGap);
		} else if (showTitle) {
			setOffsetY(progressBarGap + soloTitleHeight + productMargin + titleProductGap);
		} else if (hasProduct) {
			setOffsetY(progressBarGap + soloProductHeight + productMargin);
		} else {
			setOffsetY(0);
		}

	}, [showTitle, hasProduct]);

	if (videoData.captionData == undefined || !videoData.captionData.enabled || !show) return null;

	return <div ref={divRef} style={{
		position: "absolute",
		left: `clamp(0%, calc(50% + ${position.x}px), calc(100% - ${absWidth}px))`,
		top: `clamp(0%, calc(50% + ${position.y}px), calc(100% - ${height}px -
			calc(
				${staticOffsetY}rem
				+ min(${offsetY}em, ${offsetY}rem)
			)
		))`,
		width: "fit-content",
		height: "fit-content",
		textAlign: "center",
		display: `${(text != "" && fontsReady) ? "block" : "none"}`,
		padding: "0.5em"
	}}>
		<p style={{
			borderRadius: "0.5em",
			backgroundColor: backgroundColor,
			color: textColor,
			fontSize: `min(${fontSize}em, ${fontSize}rem)`,
			fontFamily: theme.fonts.family,
			padding: "0.5em",
			width: "fit-content",
			margin: "0",
			marginLeft: "auto",
			marginRight: "auto",
			whiteSpace: "pre-line",
			overflowWrap: "anywhere",
			wordBreak: "normal"
		}}>
			{text}
		</p>
	</div>;
};
