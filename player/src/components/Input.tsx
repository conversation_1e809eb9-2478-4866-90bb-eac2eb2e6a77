import styled, {
	css,
	FlattenSimpleInterpolation
} from "styled-components/macro";

export const Input = styled.input<{hasError: boolean}>`
    width: 100%;
    box-sizing: border-box;
    display: block;
    padding: 1rem 1rem 1rem 1rem;
    font-family: ${(props):string => props.theme.fonts.family};
    font-size: 0.85rem;
    border: 0px solid transparent;
    border-radius: 10px;
    background-color: #F8F8F8;
    color: #535353;
    outline: none;
    appearance: none;
    &::placeholder {
        color: #9E9D9D;
        font-size: 0.85rem;
    }
    ${(props): false | FlattenSimpleInterpolation =>
		props.hasError &&
    css`
        &::placeholder {
            color: red;
        }
        border: 1px solid red;
    `}
`;

export const TextArea = styled.textarea<{ hasError: boolean }>`
    width: 100%;
    box-sizing: border-box;
    display: block;
    padding: 1rem;
    font-family: ${(props): string => props.theme.fonts.family};
    font-size: clamp(0.75rem, 2cqh, 1rem);
    border: 0px solid transparent;
    border-radius: 10px;
    background-color: #f8f8f8;
    color: #535353;
    outline: none;
    appearance: none;
    resize: none;
    min-height: 100px;
    &::placeholder {
        color: #9e9d9d;
        font-size: clamp(0.75rem, 2cqh, 1rem);
    }
    ${(props):false | FlattenSimpleInterpolation =>
		props.hasError &&
        css`
            &::placeholder {
                color: red;
            }
            border: 1px solid red;
        `}
`;
