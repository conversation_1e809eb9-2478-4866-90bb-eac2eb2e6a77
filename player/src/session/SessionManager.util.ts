import ObjectID from "bson-objectid";

interface ISession {
	id: string;
	createdAt: string;
	fingerprint?: string;
}

export class SessionManager {
	private sessionCookieKey = "apSession";

	public getSession = (): ISession => {
		try {
			const sessionCookie = this.getCookie(this.sessionCookieKey);
			if (!sessionCookie) {
				return this.createNewSession();
			}

			const parsedSession = JSON.parse(sessionCookie) as ISession;
			if (this.isSessionExpired(parsedSession)) {
				return this.createNewSession();
			}

			return parsedSession;
		} catch (error: unknown) {
			return this.createNewSession();
		}
	};

	private createNewSession = (): ISession => {
		const now = new Date().toISOString();
		const newSession: ISession = {
			id: this.generateUniqueId(),
			createdAt: now
		};
		this.setSessionCookie(newSession);
		return newSession;
	};

	private isSessionExpired = (session: ISession): boolean => {
		const createdAt = new Date(session.createdAt);
		const now = new Date();

		if (createdAt.getUTCDate() !== now.getUTCDate()) {
			return true;
		}

		if (createdAt.getUTCMonth() !== now.getUTCMonth()) {
			return true;
		}

		if (createdAt.getUTCFullYear() !== now.getUTCFullYear()) {
			return true;
		}

		return false;
	};

	private generateUniqueId(): string {
		return ObjectID().toHexString();
	}

	private setSessionCookie = (session: ISession): void => {
		const sessionStr = JSON.stringify(session);
		document.cookie = `${this.sessionCookieKey}=${sessionStr}; path=/;`;
	};

	private getCookie = (name: string): string | null | undefined => {
		const value = "; " + document.cookie;
		const parts = value.split("; " + name + "=");
		if (parts.length === 2) {
			return parts.pop()?.split(";").shift();
		}
		return null;
	};
}
