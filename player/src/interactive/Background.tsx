import React, {
	useMemo,
	useState,
	useRef,
	useCallback,
	useEffect
} from "react";
import styled from "styled-components";
import {
	LoadingSpinner,
	Flex
} from "@player/components";
import { ISliderVideoProgress } from "@player/slider";
import {
	VideoDisplayModeEnum,
	IVideoData
} from "@player/app/app.interface";

const VideoSpinnerCon = styled(Flex)<{ url: string }>`
	position: absolute;
	background-image: url(${({ url }): string => url});
	top: 0;
	left: 0;
	height: 100%;
	width: 100%;
	background-size: cover;
	overflow: hidden;
	background-position: center;
`;

const VideoWrapper = styled(Flex)`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: -1;
  justify-content: center;
  align-items: center;
  background-color: black;
`;

interface HTMLVideoElementWithFullScreen extends HTMLVideoElement {
	webkitEnterFullscreen?: () => void;
}

interface Props {
	videoData: IVideoData;
	playingVideo: boolean;
	playingAudio: boolean;
	handleVideoTimeUpdate: (input: ISliderVideoProgress) => void;
	handleVideoEnded: () => void;
	handleSetVideoSeekHandler?: (seekFunction: (time: number) => void) => void;
	isFullScreenIOS: boolean;
	setIsFullScreenIOS: React.Dispatch<React.SetStateAction<boolean>>;
}

// eslint-disable-next-line max-lines-per-function
export const Background: React.FC<Props> = ({
	videoData,
	playingVideo,
	playingAudio,
	handleVideoTimeUpdate,
	handleVideoEnded,
	handleSetVideoSeekHandler,
	isFullScreenIOS,
	setIsFullScreenIOS
}) => {
	const playerRef = useRef<HTMLVideoElement>(null);
	const [loading, setLoading] = useState<boolean>(true);
	const [hasPlayed, setHasPlayed] = useState<boolean>(false);

	const seekVideo = useCallback((time: number) => {
		if (playerRef.current) {
			playerRef.current.currentTime = time;
		}
	}, []);

	useEffect(() => {
		if (handleSetVideoSeekHandler) {
			handleSetVideoSeekHandler(seekVideo);
		}
	}, [handleSetVideoSeekHandler, seekVideo]);

	useEffect(() => {
		const video = playerRef.current as HTMLVideoElement | undefined;
		if (!video) return;

		const handleWebkitEndFullscreen = (): void => {
			setIsFullScreenIOS(false);
		};

		video.addEventListener("webkitendfullscreen", handleWebkitEndFullscreen);

		return () => {
			video.removeEventListener("webkitendfullscreen", handleWebkitEndFullscreen);
		};
	}, [setIsFullScreenIOS]);


	useEffect(() => {
		const video = playerRef.current as HTMLVideoElementWithFullScreen | undefined;
		if (!video) return;

		if (isFullScreenIOS && video.webkitEnterFullscreen) {
			video.webkitEnterFullscreen();
		}
	}, [isFullScreenIOS]);

	const play = useCallback(() => {
		const video = playerRef.current as HTMLVideoElement | undefined;
		if (video && playingVideo) {
			video
				.play()
				.then(() => {
					setHasPlayed(true);
				})
				.catch((error) =>
					console.error(`Failed to play video ${videoData.videoURL} : `, error)
				);
		}
	}, [playingVideo, videoData.videoURL]);

	useEffect(() => {
		const video = playerRef.current as HTMLVideoElement | undefined;
		if (video) {
			if (playingVideo) {
				play();
			} else {
				video.pause();
			}
		}
	}, [play, playingVideo, playingAudio]);

	useEffect(() => {
		const videoElm = playerRef.current as HTMLVideoElement | undefined;
		if (videoElm) {
			const handleError = (error: unknown):void => {
				console.error(`Failed to play video ${videoData.videoURL} : `, error);
			};
			const handleCanPlay = (): void => play();
			const handleCanPlayThrough = (): void => {
				setLoading(false);
			};

			videoElm.addEventListener("canplay", handleCanPlay);
			videoElm.addEventListener("error", handleError);
			videoElm.addEventListener("canplaythrough", handleCanPlayThrough);

			return () => {
				videoElm.removeEventListener("canplay", handleCanPlay);
				videoElm.removeEventListener("error", handleError);
				videoElm.removeEventListener("canplaythrough", handleCanPlayThrough);
			};
		}
	}, [videoData.videoURL, play]);

	useEffect(() => {
		const videoElm = playerRef.current as HTMLVideoElement | undefined;
		if (videoElm) {
			const getTimeUpdates = (): void => {
				if (videoElm.readyState === HTMLMediaElement.HAVE_ENOUGH_DATA) {
					const { duration } = videoElm;
					const clampedCurrentTime = Math.min(videoElm.currentTime, duration);
					handleVideoTimeUpdate({
						totalSeconds: duration,
						playedSeconds: clampedCurrentTime,
						playedPercent: (clampedCurrentTime / videoElm.duration) * 100
					});
				}
			};
			videoElm.ontimeupdate = getTimeUpdates;
		}
		return () => {
			if (videoElm) {
				videoElm.ontimeupdate = null;
			}
		};
	}, [handleVideoTimeUpdate]);

	useEffect(() => {
		const videoElm = playerRef.current as HTMLVideoElement | undefined;
		if (videoElm) {
			const videoHasEnded = (): void => {
				handleVideoEnded();
				play();
			};
			videoElm.onended = videoHasEnded;
		}
		return () => {
			if (videoElm) {
				videoElm.onended = null;
			}
		};
	}, [handleVideoEnded, play]);

	const getVideoStyle = (videoData: IVideoData): React.CSSProperties =>{
		const { videoDisplayMode, videoWidthPx, videoHeightPx } = videoData;
		const defaultStyle: React.CSSProperties = {
			width: "auto",
			height: "auto",
			aspectRatio: "auto"
		};

		if (![VideoDisplayModeEnum.LANDSCAPE, VideoDisplayModeEnum.PORTRAIT].includes(videoDisplayMode) ||
			typeof videoWidthPx !== "number" || typeof videoHeightPx !== "number" || videoWidthPx <= 0 ||
			videoHeightPx <= 0
		) {
			return defaultStyle;
		}

		const portraitStyle: React.CSSProperties = {
			width: "auto",
			height: "100%",
			aspectRatio: "9/16"
		};
		const landscapeStyle: React.CSSProperties = {
			width: "100%",
			height: "auto",
			aspectRatio: "16/9"
		};

		if (videoDisplayMode === "landscape") {
			if (videoWidthPx < videoHeightPx)
				return portraitStyle;
			return landscapeStyle;
		} else if (videoDisplayMode === "portrait") {
			if (videoWidthPx > videoHeightPx)
				return landscapeStyle;
			return portraitStyle;
		}
		return defaultStyle;
	};

	const content = useMemo(() => {
		const videoStyle = getVideoStyle(videoData);
		return (
			<video
				ref={playerRef}
				src={videoData.videoURL}
				autoPlay
				muted={!playingAudio}
				playsInline
				style={{
					objectFit: "cover",
					maxHeight: "100%",
					maxWidth: "100%",
					...videoStyle
				}}
				preload="metadata"
				title={videoData.title}
				aria-describedby={videoData.description ? `video-description-${videoData._id}` : undefined}
			>
				{videoData.description && (
					<p id={`video-description-${videoData._id}`} hidden>
						{videoData.description}
					</p>
				)}
			</video>
		);
	}, [playingAudio, videoData]);

	return (
		<VideoWrapper>
			{(!hasPlayed || loading) && (
				<VideoSpinnerCon url={videoData.videoPosterURL}>
					{loading && <LoadingSpinner />}
				</VideoSpinnerCon>
			)}
			{content}
		</VideoWrapper>
	);
};
