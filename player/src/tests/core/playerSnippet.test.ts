import { act } from "react-dom/test-utils";
import { waitFor } from "@testing-library/react";
import {
	accountManifestMock,
	cdnUrlMock,
	videoDataCacheMock,
	playerUrlMock
} from "../mocks/data.mock";
import * as RENDER_SNIPPET from "@player/core/renderSnippet";
import { SnippetTypeEnum } from "@player/app";
import {
	EventNameEnum,
	IEventInput
} from "@player/event";
import { processSnippetElements } from "@player/core/core.controller";
import { defaultTheme } from "@player/theme/defaults";
import * as eventService from "@player/event/event.service";
import { SessionManagerSingleton } from "@player/session";

const setupMockDOM = (): void => {
	document.body.innerHTML = `
    <div data-gp-type="player" data-gp-video="v1" data-video-width="600" data-video-height="350"></div>
  `;
};

describe("Core Script - Player Snippet", () => {
	beforeEach(async () => {
		jest.clearAllMocks();
		jest.resetModules();
		jest.spyOn(console, "warn").mockImplementation(jest.fn());
		jest.spyOn(console, "error").mockImplementation(jest.fn());
	});

	afterAll(() => {
		jest.resetAllMocks();
	});

	it("[Player SNIPPET + IMPRESSION ]", async () => {
		setupMockDOM();
		const renderSnippetSpy = jest.spyOn(RENDER_SNIPPET, "renderSnippet");
		const sendAppEventSpy = jest.spyOn(eventService, "sendAppEvent");
		const videoURLSnippetMap = new Map<string, HTMLElement[]>();
		const playerElement: HTMLElement | null = document.querySelector(
			"[data-gp-type=\"player\"]"
		);

		if (!playerElement) {
			throw new Error("Missing Player element in DOM.");
		}

		let accountId;
		const videoId = playerElement.getAttribute("data-gp-video");
		if (!videoId) {
			throw new Error("Missing videoId.");
		}
		const videoUrl = `${cdnUrlMock}data-cache/videos/${videoId}-video.json`;
		videoURLSnippetMap.set(videoUrl, [playerElement]);

		global.fetch = jest.fn().mockResolvedValue({
			json: () => Promise.resolve(videoDataCacheMock)
		});

		await act(async () => {
			const response = await fetch(videoUrl);
			const videoData = await response.json();
			accountId = videoData.accountId;

			processSnippetElements(
				videoUrl,
				videoDataCacheMock,
				videoURLSnippetMap,
				{ endpoint: playerUrlMock, accountManifest: accountManifestMock }
			);
		});

		if (!accountId) {
			throw new Error("Missing accountId in collection data.");
		}

		const sessionId = SessionManagerSingleton.getInstance().getSession().id;
		const appUrl = `${playerUrlMock}?videoId=${videoId}&gpSession=${sessionId}`;
		const renderSnippetInput = {
			accountId,
			collectionId: undefined,
			videoId,
			element: playerElement,
			dataGpType: SnippetTypeEnum.PLAYER,
			appUrl,
			videoData: [videoDataCacheMock.interactiveVideo],
			theme: defaultTheme
		};

		expect(renderSnippetSpy).toHaveBeenCalledWith(renderSnippetInput);

		const eventInput: IEventInput = {
			eventName: EventNameEnum.SNIPPET_IMPRESSION,
			accountId: accountId,
			videoId: videoId,
			snippet: {
				type: SnippetTypeEnum.PLAYER
			}
		};
		expect(sendAppEventSpy).toHaveBeenCalledWith(eventInput);

		await waitFor(() => {
			const playerComponent = playerElement.querySelector(
				"[data-testid=\"player-iframe\"]"
			);
			if (!playerComponent) {
				throw new Error("Missing playerComponent in the DOM.");
			}
			expect(playerComponent).not.toBeNull();
		});
	});
});
