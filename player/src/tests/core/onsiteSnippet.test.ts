import { act } from "react-dom/test-utils";
import { waitFor } from "@testing-library/react";
import {
	accountManifestMock,
	collectionDataCacheMock,
	cdnUrlMock
} from "../mocks/data.mock";
import * as RENDER_SNIPPET from "@player/core/renderSnippet";
import { SnippetTypeEnum } from "@player/app";
import {
	EventNameEnum,
	IEventInput
} from "@player/event";
import { processSnippetElements } from "@player/core/core.controller";
import { defaultTheme } from "@player/theme/defaults";
import * as eventService from "@player/event/event.service";
import { SessionManagerSingleton } from "@player/session";
jest.mock("@player/event/event.service");

const setupMockDOM = (): void => {
	document.body.innerHTML = `
    <div data-gp-type="carousel" data-gp-collection="c1"></div>
    <div data-gp-type="widget" data-gp-collection="c1"></div>
    <div data-gp-type="onsite" data-gp-collection="c1"></div>
    `;
};

describe("Core Script - Snippets", () => {
	beforeEach(async () => {
		jest.clearAllMocks();
		jest.resetModules();
		jest.spyOn(console, "warn").mockImplementation(jest.fn());
		jest.spyOn(console, "error").mockImplementation(jest.fn());
	});

	afterAll(() => {
		jest.resetAllMocks();
	});

	it("[ONSITE SNIPPET + IMPRESSION ]", async () => {
		setupMockDOM();
		const renderSnippetSpy = jest.spyOn(RENDER_SNIPPET, "renderSnippet");

		const manifestUrls = new Map<string, HTMLElement[]>();
		const url = "http://localhost/manifest";
		const onsiteElement: HTMLElement | null = document.querySelector(
			"[data-gp-type=\"onsite\"]"
		);
		if (!onsiteElement) {
			throw new Error("Missing onsite element in DOM.");
		}

		manifestUrls.set(url, [onsiteElement]);
		const PLAYER_ENDPOINT = "http://localhost";

		let accountId;
		const collectionId = onsiteElement.getAttribute("data-gp-collection");
		const collectionUrl = `${cdnUrlMock}data-cache/collections/${collectionId}-collection.json`;

		if (!collectionId) {
			throw new Error("Missing collectionId.");
		}

		global.fetch = jest.fn().mockResolvedValue({
			json: () => Promise.resolve(collectionDataCacheMock)
		});

		await act(async () => {
			const response = await fetch(collectionUrl);
			const collectionData = await response.json();
			accountId = collectionData.accountId;

			processSnippetElements(
				url,
				collectionDataCacheMock,
				manifestUrls,
				{ endpoint: PLAYER_ENDPOINT, accountManifest: accountManifestMock }
			);
		});


		if (!accountId) {
			throw new Error("Missing accountId in collection data.");
		}
		const sessionId = SessionManagerSingleton.getInstance().getSession().id;
		const appUrl = `http://localhost?collectionId=${collectionId}&gpSession=${sessionId}`;

		const renderSnippetInput = {
			accountId,
			collectionId,
			element: onsiteElement,
			dataGpType: SnippetTypeEnum.ONSITE,
			appUrl,
			videoData: collectionDataCacheMock.interactiveVideos,
			theme: defaultTheme
		};

		expect(renderSnippetSpy).toHaveBeenCalledWith(renderSnippetInput);

		const eventInput: IEventInput = {
			eventName: EventNameEnum.SNIPPET_IMPRESSION,
			accountId: accountId,
			collectionId: collectionId,
			snippet: {
				type: SnippetTypeEnum.ONSITE
			}
		};
		expect(eventService.sendAppEvent).toHaveBeenCalledWith(eventInput);

		await waitFor(() => {
			const onsiteComponent = onsiteElement.querySelector(
				"[data-testid=\"onsite-iframe\"]"
			);
			if (!onsiteComponent) {
				throw new Error("Missing widgetComponent in the DOM.");
			}
			expect(onsiteComponent).not.toBeNull();
		});
	});
});
