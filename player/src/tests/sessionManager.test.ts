import { SessionManagerSingleton } from "@player/session";
import { SessionManager } from "@player/session/SessionManager.util";

describe("SessionManager", () => {
	let sessionManager: SessionManager;

	beforeEach(() => {
		document.cookie.split(";").forEach((c) => {
			document.cookie = c
				.replace(/^ +/, "")
				.replace(
					/=.*/,
					"=;expires=" + new Date().toUTCString() + ";path=/"
				);
		});

		sessionManager = SessionManagerSingleton.getInstance();
	});

	it("should always return the same instance", () => {
		const anotherSessionManager = SessionManagerSingleton.getInstance();
		expect(sessionManager).toBe(anotherSessionManager);
	});

	it("should get a valid session", async () => {
		const session = sessionManager.getSession();
		expect(session).toBeDefined();
		expect(session.id).toBeDefined();
		expect(session.createdAt).toBeDefined();
	});
});
