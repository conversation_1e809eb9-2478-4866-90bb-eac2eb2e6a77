import Joi from "joi";
import {
	APIErrorName,
	LocaleAPI
} from "../../interfaces/apiTypes";
import { ISignupPayload } from "../../modules/signup/signup.interfaces";

const emailSchema = Joi.string()
	.lowercase()
	.email({
		tlds: { allow: false }
	})
	.trim();

const passwordSchema = Joi.string().min(8).messages({
	"string.min": APIErrorName.E_PASSWORD_COMPLEXITY
});

export const signUpSchema = {
	data: Joi.object<ISignupPayload>({
		email: Joi.when("inviteToken", {
			is: Joi.exist(),
			then: emailSchema,
			otherwise: emailSchema.required()
		}),
		password: Joi.when("inviteToken", {
			is: Joi.exist(),
			then: passwordSchema,
			otherwise: passwordSchema.required()
		}),
		companyName: Joi.when("inviteToken", {
			is: Joi.exist(),
			then: Joi.string(),
			otherwise: Joi.string().optional()
		}),
		firstName: Joi.when("inviteToken", {
			is: Joi.exist(),
			then: Joi.string(),
			otherwise: Joi.string().optional()
		}),
		lastName: Joi.when("inviteToken", {
			is: Joi.exist(),
			then: Joi.string(),
			otherwise: Joi.string().optional()
		}),
		legalAgreement: Joi.boolean().optional(),
		callbackEndpoint: Joi.when("inviteToken", {
			is: Joi.exist(),
			then: Joi.string(),
			otherwise: Joi.string().required()
		}),
		locale: Joi.string().custom((value, helper) => {
			if (Object.values(LocaleAPI).includes(value)) {
				return true;
			}
			return helper.message({
				custom: "language is not supported."
			});

		}),
		maxCompanies: Joi.number().optional(),
		inviteToken: Joi.string()
	}).messages({
		"any.required": APIErrorName.E_INVALID_INPUT,
		"string.empty": APIErrorName.E_INVALID_INPUT
	})
};
