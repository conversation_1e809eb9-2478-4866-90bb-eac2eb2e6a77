import {
	Request,
	Response,
	NextFunction
} from "express";
import jwt from "jsonwebtoken";
import { IAccountToken } from "../modules/account/account.interfaces";
import { APIErrorName } from "../interfaces/apiTypes";
import { IAccessToken } from "../modules/accessToken/accessToken.interface";
import {
	getSecrets,
	ISecrets
} from "../modules/secrets/secrets.model";
import { APIError } from "../utils/helpers/apiError";
import { AuthenticationModel } from "../modules/authentication/authentication.model";

const isSuper = (req: Request): boolean | undefined => {
	return req.accessToken?.super;
};

const setSuperWritePermission = (req: Request): void => {
	req.superWritePermission = false;

	if (isSuper(req)) {
		req.superWritePermission = true;
	}
};

const decodeAccessToken = (req: Request, res: Response, bearerToken: string): IAccessToken => {
	const decodedBearerToken = jwt.decode(bearerToken);

	if (!decodedBearerToken) {
		throw new APIError(APIErrorName.E_INVALID_AUTHORIZATION, "invalid bearer authorization header");
	}

	const decodedAccessToken: IAccessToken = decodedBearerToken as IAccessToken;

	return decodedAccessToken;
};

export const decodeAccess = async (req: Request, res: Response, next: NextFunction): Promise<Response | void> => {
	try {
		let bearerToken = req.header("Authorization");
		if (!bearerToken) {
			throw new APIError(
				APIErrorName.E_MISSING_AUTHORIZATION,
				"missing bearer authorization header")
				.suppressLog();
		}

		bearerToken = bearerToken.replace("Bearer", "").trim();

		const decodedAccessToken = decodeAccessToken(req, res, bearerToken);

		const authModel = new AuthenticationModel(null);
		const authenticationDocument = await authModel.readOneById(decodedAccessToken.authenticationId);

		const secrets: ISecrets = await getSecrets();
		const gpSecretKey = secrets.hashkey?.key;

		if (!gpSecretKey) {
			throw new APIError(APIErrorName.E_SERVICE_FAILED, "failed to read the hash key");
		}

		const saltedHash = `${gpSecretKey}${authenticationDocument.salt}`;

		let verifiedAccessToken;
		try {
			verifiedAccessToken = jwt.verify(bearerToken, saltedHash);
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_INVALID_AUTHORIZATION, error).suppressLog();
		}

		const accessToken = verifiedAccessToken as IAccessToken;

		req.accessToken = accessToken;

		const accountTokenHeader = req.header("x-account-token");
		if (accountTokenHeader) {
			let verifiedAccountToken;
			try {
				verifiedAccountToken = jwt.verify(accountTokenHeader, saltedHash);
			} catch (error: unknown) {
				throw new APIError(APIErrorName.E_INVALID_AUTHORIZATION, error).suppressLog();
			}

			const accountToken = verifiedAccountToken as IAccountToken;

			if (accountToken.userId !== accessToken.userId) {
				throw new APIError(APIErrorName.E_INVALID_AUTHORIZATION,
					"account token user does not match access token user. " +
					`${accountToken.userId} !== ${authenticationDocument.userId}`);
			}

			req.accountToken = accountToken;
		}

		setSuperWritePermission(req);

		next();
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};
