import {
	Request,
	Response,
	NextFunction
} from "express";
import { APIErrorName } from "../interfaces/apiTypes";
import { APIError } from "../utils/helpers/apiError";

export const requireSuperWritePermission = async (
	req: Request, res: Response, next: NextFunction
): Promise<void | Response> => {
	try {
		if (!req.accessToken) {
			throw new APIError(APIErrorName.E_MISSING_AUTHORIZATION,
				"Missing bearer authorization header while testing for super write permission");
		}

		if (!req.accountToken) {
			throw new APIError(APIErrorName.E_MISSING_AUTHORIZATION,
				"Missing x-account-token header while testing for super write permission");
		}

		/*
		 truth table
		 passwordRequiredToWrite | superWritePermission | result | explanation
		 false                   | false                | true   |
		 		user belongs to account. write permission is not necessary.
		 false                   | true                 | true   |
		 		user belongs to account. write permission is not necessary.
		 true                    | false                | false  |
		 		user does not belong to account. will fail without required write permission.
		 true                    | true                 | true   |
		 		user does not belong to account. will pass with required write permission.
		*/

		if (req.accountToken.passwordRequiredToWrite && !req.superWritePermission) {
			throw new APIError(APIErrorName.E_ACCESS_FORBIDDEN,
				"super write permission required.");
		}

		next();
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};
