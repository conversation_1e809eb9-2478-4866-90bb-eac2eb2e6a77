import {
	Logging,
	MonitoredResource
} from "@google-cloud/logging";

export enum LogScope {
	INFO = "info",
	ERROR = "error"
}

export interface ILog {
	message: string;
	objData?: unknown;
	trace?: string;
	scope: LogScope;
}
interface ILogEntry {
	message: string;
	data: unknown;
	trace?: string;
	scope: LogScope;
	timestamp: string;
}

interface ILogMetadata {
	resource: MonitoredResource;
	severity: "DEFAULT" | "DEBUG" | "INFO" | "NOTICE" | "WARNING" | "ERROR" | "CRITICAL" | "ALERT" | "EMERGENCY";
}

const logging = new Logging();
const logName = "gp-server-log";
const log = logging.log(logName);

const shouldLog = (input: ILog): boolean => {
	if (input.message === "E_IMPRESSION_LIMIT_REACHED: Metric impression limit reached") {
		return false;
	}

	return true;
};

export const gpLog = async (input: ILog): Promise<void> => {
	if (!shouldLog(input)) {
		return;
	}

	const timestamp = new Date().toISOString();

	const logEntry: ILogEntry = {
		message: input.message,
		data: input.objData || "No data is available.",
		scope: input.scope,
		timestamp,
		trace: input.trace
	};

	if (process.env.NODE_ENV !== "production") {
		gpLogLocal(logEntry, input.scope);
		return;
	}

	const metadata: ILogMetadata = {
		resource: {
			type: "cloud_run_revision"
		},
		severity: input.scope === LogScope.ERROR ? "ERROR" : "INFO"
	};

	const entry = log.entry(metadata, logEntry);

	log.write(entry).catch(error => {
		console.error("Failed to write log entry:", error);
	});

};


const gpLogLocal = (logEntry: ILogEntry, scope: LogScope): void => {
	const log: Record<LogScope, () => void> = {
		[LogScope.INFO]: () => {
			console.info("[SERVER][INFO]", logEntry);
		},
		[LogScope.ERROR]: () => {
			console.error("[SERVER][ERROR]", logEntry);
		}
	};
	if (log[scope]) {
		log[scope]();
	} else {
		console.error("Undefined log scope:", scope);
	}
};
