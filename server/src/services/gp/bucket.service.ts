import {
	Storage,
	UploadOptions,
	GetSignedUrlConfig
} from "@google-cloud/storage";
import {
	getSecrets,
	ISecrets
} from "../../modules/secrets/secrets.model";
import mongoose from "mongoose";
import path from "path";
import axios from "axios";
import fs from "fs";
import util from "util";
import {
	validateBackslash,
	getLocalPath,
	LocalPathType,
	createEmptyFile,
	isLastFileChunk
} from "../../utils/helpers/gp.helper";
import {
	gpLog,
	LogScope
} from "../../utils/managers/gpLog.manager";
import { BucketModel } from "../../modules/bucket/bucket.model";

const appendFileAsync = util.promisify(fs.appendFile);
interface StorageBucketInfo {
	storage: Storage;
	bucketName: string;
	cdnHost: string;
	storageHost: string;
}

let gcpStorage: Storage | undefined = undefined;
const getStorageBucket = async (): Promise<StorageBucketInfo> => {
	const secrets: ISecrets = await getSecrets();
	if (!gcpStorage) {
		gcpStorage = new Storage({ apiEndpoint: secrets.storage.host, credentials: secrets.storage.credentials });
	}
	return {
		storage: gcpStorage,
		bucketName: secrets.storage.bucketName,
		cdnHost: validateBackslash(secrets.cdn.host),
		storageHost: validateBackslash(secrets.storage.host)
	};
};

export interface IUploadAsset {
	file: Express.Multer.File;
	outputFilePath: string;
	outputFileName: string;
}

export const uploadAssetToCloud = async (
	input: IUploadAsset,
	cacheControl: string
): Promise<string | undefined> => {
	try {
		const { file, outputFilePath, outputFileName } = input;

		const { storage, bucketName, cdnHost } = await getStorageBucket();
		const bucket = storage.bucket(bucketName);
		const bucketExists: boolean[] = await bucket.exists();
		if (!bucketExists[0]) {
			throw new Error("cant find cloud bucket.");
		}

		const fileParsed = path.parse(outputFileName);
		const ext = fileParsed.ext;
		const name = fileParsed.name;

		const fileObjId = new mongoose.Types.ObjectId().toString();
		const newFilename = `${name}_${fileObjId}${ext}`;
		const fileDestination = outputFilePath + newFilename;
		const uploadOptions: UploadOptions = {
			destination: fileDestination,
			metadata: {
				cacheControl: cacheControl,
				contentType: file.mimetype
			}
		};

		const fileBuffer = file.buffer;
		const blob = bucket.file(fileDestination, uploadOptions);
		// create write stream to write the buffer directly to the output file path
		const blobStream = blob.createWriteStream({
			//  ensures that the file is written in a single request.
			resumable: false
		});

		return new Promise((resolve, reject) => {
			// write buffer to the output file path
			blobStream
				.on("finish", () => {
					resolve(cdnHost + fileDestination);
				})
				.on("error", (err) => {
					gpLog({
						message: "Failed to upload asset to bucket.",
						objData: { error: err?.message },
						trace: "bucket.service | uploadAssetToCloud",
						scope: LogScope.ERROR
					});
					reject(undefined);
				})
				.end(fileBuffer);
		});
	} catch (error: any) {
		gpLog({
			message: "Failed to upload asset to bucket.",
			objData: { error: error?.message },
			trace: "bucket.service | uploadAssetToCloud",
			scope: LogScope.ERROR
		});
		return undefined;
	}
};

export const deleteAssetsFromCloud = async (
	fileDestination: string[]
): Promise<boolean | null> => {
	try {
		const { storage, bucketName } = await getStorageBucket();
		const bucket = storage.bucket(bucketName);

		const bucketExists: boolean[] = await bucket.exists();
		if (!bucketExists[0]) {
			throw new Error("cant find cloud bucket.");
		}

		const deletePromises = fileDestination.map((fileName) => {
			const file = bucket.file(fileName);
			return file.delete({ ignoreNotFound: true });
		});

		await Promise.all(deletePromises);
		return true;
	} catch (error: any) {
		gpLog({
			message: "Failed to delete content from bucket.",
			objData: { error: error?.message },
			trace: "bucket.service | deleteAssetFromCloud",
			scope: LogScope.ERROR
		});
		return null;
	}
};

export interface ISignedURL {
	contentType: string;
	xFilename: string;
	outputFilePath: string;
}

export const readBucketSignedUrl = async (
	input: ISignedURL
): Promise<string | undefined> => {
	try {
		const { contentType, xFilename, outputFilePath } = input;
		const { storage, bucketName } = await getStorageBucket();

		const bucket = storage.bucket(bucketName);
		const bucketExists: boolean[] = await bucket.exists();
		if (!bucketExists[0]) {
			throw new Error("cant find cloud bucket.");
		}

		const suffix = `-${new mongoose.Types.ObjectId().toString()}`;
		const sanitizedFilename = BucketModel.sanitizeFileName(xFilename, suffix);
		const fileDestination = outputFilePath + sanitizedFilename;

		const secrets = await getSecrets();
		if (secrets.storage.isLocal) {
			return fileDestination;
		}

		const getSignedUrlConfig: GetSignedUrlConfig = {
			version: "v4",
			action: "resumable",
			expires: Date.now() + 30 * 60 * 1000,
			contentType: contentType
		};
		const [signedURL] = await storage
			.bucket(bucketName)
			.file(fileDestination)
			.getSignedUrl(getSignedUrlConfig);
		const resumableSession = await axios({
			method: "POST",
			url: signedURL,
			headers: {
				"x-goog-resumable": "start",
				"content-type": contentType
			}
		});

		if (!resumableSession || !resumableSession.headers) {
			throw new Error("resumableSession is undefined");
		}
		return resumableSession.headers["location"];

	} catch (error: any) {
		gpLog({
			message: "Failed to read GCP bucket signed url.",
			objData: { error: error?.message },
			trace: "bucket.service | readBucketSignedUrl",
			scope: LogScope.ERROR
		});
		return undefined;
	}
};

export interface IUploadChunks {
	location: string;
	contentType: string;
	contentLength: number;
	contentRange: string;
	fileChunk: any;
}

export interface IUploadChunksOut {
	url?: string;
	status?: number;
}

export const uploadAssetChunksToCloud = async (
	input: IUploadChunks
): Promise<IUploadChunksOut | undefined> => {
	try {
		const { location, contentType, contentLength, contentRange, fileChunk } =
      input;

		const { bucketName, cdnHost } = await getStorageBucket();

		const secrets = await getSecrets();

		if (secrets.storage.isLocal) {
			const localStoragePath = getLocalPath(LocalPathType.STORAGE);
			if (!localStoragePath) {
				return undefined;
			}
			const filePath = localStoragePath + bucketName + "/" + location;
			await createEmptyFile(filePath);
			const binaryData = Buffer.from(fileChunk, "binary");
			await appendFileAsync(filePath, binaryData);
			if (contentLength % 256 !== 0 || isLastFileChunk(contentRange)) {
				return { url: encodeURI(cdnHost + location) };
			}
			return { status: 308 };

		}
		const binaryData = Buffer.from(fileChunk, "binary");
		const response = await axios({
			method: "PUT",
			url: location,
			headers: {
				"Content-Type": contentType,
				"Content-Length": contentLength,
				"Content-Range": contentRange
			},
			data: binaryData
		});

		if (response.status === 200) {
			const filePath = new URL(location).pathname
				.split("/")
				.slice(2)
				.join("/");
			return { url: cdnHost + filePath };
		} else if (response.status === 308) {
			return { status: 308 };
		}
		throw new Error(
			`calling ${location} results in ${response.statusText}`
		);


	} catch (error: any) {
		if (error.response?.status === 308) {
			return { status: 308 };
		}
		gpLog({
			message: "Failed to upload file to bucket.",
			objData: { error: error?.message },
			trace: "bucket.service | uploadAssetChunksToCloud",
			scope: LogScope.ERROR
		});
		return undefined;
	}
};
