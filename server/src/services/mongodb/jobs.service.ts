import { ClientSession } from "mongoose";
import { APIErrorName } from "../../interfaces/apiTypes";
import {
	Job,
	JobModel
} from "../../modules/job/job.model";
import { APIError } from "../../utils/helpers/apiError";
import {
	gpLog,
	LogScope
} from "../../utils/managers/gpLog.manager";

export const readJob = async (
	query: any,
	session: ClientSession | null
): Promise<Job | null> => {
	try {
		const document: Job | null = await JobModel.findOne(query).session(
			session
		);
		return document;
	} catch (error: any) {
		gpLog({
			message: "Failed to read job.",
			objData: { error: error },
			trace: "jobs.service | readJob",
			scope: LogScope.ERROR
		});
		throw new APIError(
			APIErrorName.E_SERVICE_FAILED,
			`Failed to read job. | ${error.name} | ${error.message}`
		);
	}
};
