import { ReportsAggregateModel } from "./aggregate.model";
import { JobService } from "../../job/job.service";
import { JobContainerModel } from "../../job/container/job.container.model";
import {
	JobsType,
	JobsStatus,
	Job
} from "../../job/job.model";
import { ReportsDailyAggregateDBModel } from "./aggregate.schema";
import { AccountModel } from "../../account/account.model";

jest.mock("../../job/container/job.container.model");
jest.mock("./aggregate.schema");
jest.mock("../../account/account.model");

describe("ReportsAggregateModel", () => {
	const session = null;

	afterEach(() => {
		jest.clearAllMocks();
	});

	describe("createAggregationJob", () => {
		it("should create a job and run the worker", async () => {
			const mockJob = {
				_id: "jobid123",
				tempFilename: "",
				type: JobsType.REPORTS_AGGREGATE,
				status: JobsStatus.CREATED,
				statusMessage: "",
				createdAt: new Date(),
				updatedAt: new Date(),
				aggregateDate: new Date()
			} as unknown as Job;
			const createJobSpy = jest.spyOn(JobService.prototype, "createJob").mockResolvedValue(mockJob);
			const runJobWorkerSpy = jest
				.spyOn(JobContainerModel.prototype, "runJobWorker")
				.mockResolvedValue(undefined);

			const model = new ReportsAggregateModel(session);
			const payload = { date: new Date(), accountId: "abc" };
			const job = await model.createAggregationJob(payload);

			expect(job).toBe(mockJob);
			expect(createJobSpy).toHaveBeenCalled();
			expect(runJobWorkerSpy).toHaveBeenCalledWith("jobid123", 86400);
		});
	});

	describe("aggregateAccounts", () => {
		it("should throw if aggregateDate is missing", async () => {
			const model = new ReportsAggregateModel(session);
			const job = { _id: "id", type: JobsType.REPORTS_AGGREGATE } as unknown as Job;
			await expect(model.aggregateAccounts(job)).rejects.toThrow("Job does not have an aggregateDate set.");
		});

		it("should process a single account", async () => {
			const model = new ReportsAggregateModel(session);
			const job = {
				_id: "id",
				tempFilename: "",
				type: JobsType.REPORTS_AGGREGATE,
				status: JobsStatus.CREATED,
				statusMessage: "",
				createdAt: new Date(),
				updatedAt: new Date(),
				aggregateDate: new Date(),
				accountId: "abc"
			} as unknown as Job;

			const updateJobStatusSpy = jest.spyOn(JobService.prototype, "updateJobStatus").mockResolvedValue(job);
			(AccountModel as any).mockImplementation(() => ({
				readOneById: jest.fn().mockResolvedValue({ _id: "abc" })
			}));
			model["aggregateSingleAccountJob"] = jest.fn().mockResolvedValue(undefined);

			await model.aggregateAccounts(job);

			expect(updateJobStatusSpy)
				.toHaveBeenCalledWith("id", JobsStatus.RUNNING, expect.any(String));
			expect(model["aggregateSingleAccountJob"]).toHaveBeenCalled();
			expect(updateJobStatusSpy)
				.toHaveBeenCalledWith("id", JobsStatus.COMPLETE, expect.any(String));
		});

		it("should process all accounts", async () => {
			const model = new ReportsAggregateModel(session);
			const job = {
				_id: "id",
				tempFilename: "",
				type: JobsType.REPORTS_AGGREGATE,
				status: JobsStatus.CREATED,
				statusMessage: "",
				createdAt: new Date(),
				updatedAt: new Date(),
				aggregateDate: new Date()
			} as unknown as Job;

			const updateJobStatusSpy = jest.spyOn(JobService.prototype, "updateJobStatus").mockResolvedValue(job);
			model["aggregateAllAccountsJob"] = jest.fn().mockResolvedValue(undefined);

			await model.aggregateAccounts(job);

			expect(model["aggregateAllAccountsJob"]).toHaveBeenCalled();
			expect(updateJobStatusSpy)
				.toHaveBeenCalledWith("id", JobsStatus.COMPLETE, expect.any(String));
		});
	});

	describe("readAggregatedRangeFromDB", () => {
		it("should call the DB model and return results", async () => {
			const mockResults = [{ foo: "bar" }];
			(ReportsDailyAggregateDBModel as any).mockImplementation(() => ({
				collection: {
					find: jest.fn().mockReturnValue({
						sort: jest.fn().mockReturnValue({
							toArray: jest.fn().mockResolvedValue(mockResults)
						})
					})
				}
			}));
			const model = new ReportsAggregateModel(session);
			const result = await model.readAggregatedRangeFromDB(
				{ _id: "abc" } as any,
				new Date("2024-01-01"),
				new Date("2024-01-31")
			);
			expect(result).toBe(mockResults);
		});
	});
});
