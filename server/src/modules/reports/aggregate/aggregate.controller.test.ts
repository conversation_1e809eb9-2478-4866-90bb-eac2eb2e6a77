import { ReportsAggregateController } from "./aggregate.controller";
import {
	createServer,
	initExpressRoutes
} from "../../../express";
import supertest from "supertest";
import { ReportsAggregateModel } from "./aggregate.model";
import { ReportsAggregatePostSchema } from "./aggregate.joi";

jest.mock("./aggregate.model");
jest.mock("./aggregate.joi");

const expressApp = createServer();
const controller = new ReportsAggregateController();
controller.use(expressApp, "/api/reports/aggregate");
initExpressRoutes(expressApp);

describe("ReportsAggregateController", () => {
	beforeEach(() => {
		jest.clearAllMocks();
	});

	it("should return 202 and jobId on valid POST", async () => {
		const mockJob = { _id: "aggjobid123" };
		(ReportsAggregatePostSchema.validateAsync as jest.Mock).mockResolvedValue({
			date: new Date(),
			accountId: "abc"
		});
		(ReportsAggregateModel as any).mockImplementation(() => ({
			createAggregationJob: jest.fn().mockResolvedValue(mockJob)
		}));

		const res = await supertest(expressApp)
			.post("/api/reports/aggregate")
			.query({ date: "2025-07-10", accountId: "abc" });

		expect(res.status).toBe(202);
		expect(res.body).toEqual({ jobId: "aggjobid123" });
	});

	it("should return error if validation fails", async () => {
		(ReportsAggregatePostSchema.validateAsync as jest.Mock).mockRejectedValue(new Error("Invalid input"));

		const res = await supertest(expressApp)
			.post("/api/reports/aggregate")
			.query({ date: "bad-date" });

		expect(res.status).toBe(400);
		expect(res.body).toHaveProperty("error");
	});

	it("should return error if model throws", async () => {
		(ReportsAggregatePostSchema.validateAsync as jest.Mock).mockResolvedValue({
			date: new Date(),
			accountId: "abc"
		});
		(ReportsAggregateModel as any).mockImplementation(() => ({
			createAggregationJob: jest.fn().mockRejectedValue(new Error("DB error"))
		}));

		const res = await supertest(expressApp)
			.post("/api/reports/aggregate")
			.query({ date: "2025-07-10", accountId: "abc" });

		expect(res.status).toBe(500);
		expect(res.body).toHaveProperty("error");
	});
});
