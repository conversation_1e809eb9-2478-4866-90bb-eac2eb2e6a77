import mongoose, { Schema } from "mongoose";
import {
	ReportsAggregate,
	ReportsAggregateURLInteraction,
	ReportsAggregateVideo
} from "./aggregate.types";

const ReportsAggregateURLInteractionSchema: Schema = new Schema<ReportsAggregateURLInteraction>({
	url: { type: String, required: true },
	count: { type: Schema.Types.Number, required: true }
});

const ReportsAggregateVideoSchema: Schema = new Schema<ReportsAggregateVideo>({
	videoId: { type: Schema.Types.ObjectId, required: true },
	interactiveVideoId: { type: Schema.Types.ObjectId, required: true },
	plays: { type: Schema.Types.Number, required: true },
	clicks: { type: Schema.Types.Number, required: true },
	emailLeads: { type: Schema.Types.Number, required: true },
	callLeads: { type: Schema.Types.Number, required: true },
	playTimeSeconds: { type: Schema.Types.Number, required: true },
	engagementScore: { type: Schema.Types.Number, required: true },
	likes: { type: Schema.Types.Number, required: true },
	urlInteractions: { type: [ReportsAggregateURLInteractionSchema], default: [] },
	videoLengthSeconds: { type: Schema.Types.Number, required: true },
	gifURL: { type: String, required: true },
	posterURL: { type: String, required: true },
	interactiveVideoTitle: { type: String, required: true }
});

const ReportsAggregateSchema: Schema = new Schema<ReportsAggregate>({
	accountId: { type: Schema.Types.ObjectId, required: true },
	createdAt: { type: Schema.Types.Date, required: true },
	updatedAt: { type: Schema.Types.Date, required: true },
	captureDate: { type: Schema.Types.Date, required: true },
	totalPlays: { type: Schema.Types.Number, required: true, default: 0 },
	totalPlayTime: { type: Schema.Types.Number, required: true, default: 0 },
	totalEmails: { type: Schema.Types.Number, required: true, default: 0 },
	totalCalls: { type: Schema.Types.Number, required: true, default: 0 },
	totalClicks: { type: Schema.Types.Number, required: true, default: 0 },
	userSessionIds: { type: [Schema.Types.ObjectId], required: true, default: [] },
	totalEngagedSessions: { type: Schema.Types.Number, required: true, default: 0 },
	totalImpressions: { type: Schema.Types.Number, required: true, default: 0 },
	videos: { type: [ReportsAggregateVideoSchema], default: [] }
});

export const ReportsDailyAggregateDBModel = mongoose.model<ReportsAggregate>(
	"reports_daily_aggregation",
	ReportsAggregateSchema
);
