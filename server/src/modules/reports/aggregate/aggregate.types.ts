import { ObjectId } from "mongoose";

export interface ReportsAggregatePlayTimeLookup {
	_id: ObjectId;
	totalPlays: number;
	totalPlayTime: number;
}

export interface ReportsAggregateLeadLookup {
	_id: ObjectId;
	totalLeads: number;
}

export interface ReportsAggregateURLInteractionLookup {
	_id: {
		videoId: ObjectId;
		productId: ObjectId;
	};
	totalClicks: number;
}

export interface ReportsAggregateURLInteraction {
	url: string;
	count: number;
}

export interface ReportsAggregateEngagedSession {
	sessionIds: ObjectId[];
	totalSessions: number;
}

export interface ReportsAggregateVideo {
	videoId: ObjectId;
	interactiveVideoId: ObjectId;
	gifURL: string;
	posterURL: string;
	interactiveVideoTitle: string;
	plays: number;
	clicks: number;
	emailLeads: number;
	callLeads: number;
	playTimeSeconds: number;
	engagementScore: number;
	likes: number;
	urlInteractions: ReportsAggregateURLInteraction[];
	videoLengthSeconds: number;
}

export interface ReportsAggregate {
	accountId: ObjectId;
	createdAt: Date;
	updatedAt: Date;
	captureDate: Date;
	totalPlays: number;
	totalPlayTime: number;
	totalEmails: number;
	totalCalls: number;
	totalClicks: number;
	userSessionIds: ObjectId[];
	totalEngagedSessions: number;
	totalImpressions: number;
	videos: ReportsAggregateVideo[];
}

export interface ReportsAggregatePost {
	date: Date;
	since?: boolean;
	accountId?: string;
	log?: boolean;
}
