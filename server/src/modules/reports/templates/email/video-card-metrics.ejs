<% 
    function formatPlaytime(seconds) {
        seconds = Number(seconds) || 0;
        const h = Math.floor(seconds / 3600);
        const m = Math.floor((seconds % 3600) / 60);
        const s = seconds % 60;
        let result = '';
        if (h > 0) result += `${h}h `;
        if (m > 0 || h > 0) result += `${m}m `;
        result += `${s}s`;
        return result.trim();
    }
%>
<table class="metrics-table">
    <tr>
        <td>
            <span class="metric-label">SESSIONS</span>
            <div class="metric-value-row">
                <div class="video-metric-icon">
                    <img alt="logo" class="logo" width="30"
                        src="<%= cdnHost %>reports/impressions-icon-v1.png" />
                </div>
                <div class="video-metric-value"><%= videoMetrics.sessions %></div>
            </div>
        </td>
        <td>
            <div>
                <span class="metric-label">CLICKS</span>
                <div class="metric-value-row">
                    <div class="video-metric-icon">
                        <img width="30" class="logo" src="<%= cdnHost %>reports/clicks-icon-v1.png" />
                    </div>
                    <div class="video-metric-value"><%= videoMetrics.clicks %></div>
                </div>
            </div>
        </td>
        <td>
            <div>
                <span class="metric-label">CALLS</span>
                <div class="metric-value-row">
                    <div class="video-metric-icon">
                        <img width="30" class="logo" src="<%= cdnHost %>reports/phone-icon-v1.png" />
                    </div>
                    <div class="video-metric-value"><%= videoMetrics.calls %></div>
                </div>
            </div>
        </td>
    </tr>
    <tr>
		<td>
            <div>
                <span class="metric-label">EMAILS</span>
                <div class="metric-value-row">
                    <div class="video-metric-icon">
                        <img width="30" class="logo" src="<%= cdnHost %>reports/emails-icon-v1.png" />
                    </div>
                    <div class="video-metric-value"><%= videoMetrics.emails %></div>
                </div>
            </div>
        </td>
        <td>
            <div>
                <span class="metric-label">PLAYS</span>
                <div class="metric-value-row">
                    <div class="video-metric-icon">
                        <img width="30" class="logo" src="<%= cdnHost %>reports/plays-icon-v1.png" />
                    </div>
                    <div class="video-metric-value"><%= videoMetrics.plays %></div>
                </div>
            </div>
        </td>
        <td>
            <span class="metric-label">PLAYTIME</span>
            <div class="metric-value-row">
                <div class="video-metric-icon">
                    <img width="30" class="logo" src="<%= cdnHost %>reports/playtime-icon-v1.png" />
                </div>
                <div class="video-metric-value"><%= formatPlaytime(videoMetrics.playtime) %></div>
            </div>
        </td>
    </tr>
</table>