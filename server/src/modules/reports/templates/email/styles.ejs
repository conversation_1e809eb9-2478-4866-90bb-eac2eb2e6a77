<style>
	body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
        margin: 0;
        background-color: #f5f5f5;
        line-height: 1.4;
    }
	
	.email-report-container {
		padding: 50px;
		background: white;
		box-sizing: border-box;
	}

	.logo-row {
		margin-bottom: 16px;
	}

	td.middle-gap {
		width: 100%;
	}

	.report-period {
		text-align: right;
		color: #000000;
		width: max-content;
	}
	
	.report-period .dates {
		font-size: 14px;
		font-weight: 600;
	}

	.dates {
		color: rgb(57, 51, 208);
	}

	.grey-box {
		box-sizing: border-box;
		background-color: rgb(248, 248, 248);
		border-radius: 12px;
		padding-top: 40px;
		padding-right: 50px;
		padding-bottom: 34px;
		padding-left: 50px;
		margin-bottom: 16px;
		width: 100%;
	}

	.title {
		font-size: 25px;
		font-weight: 600;
		color: #333;
		margin-bottom: 16px;
	}

	p {
		margin: 0 0 16px 0;
	}

	.section-title {
		text-align: center;
		font-size: 20px;
		font-weight: 600;
		color: #333;
		margin-bottom: 16px;
	}

	.center {
		text-align: center;
	}

	.center>* {
		margin: 0 auto;
	}

	.metrics-table {
		width: 100%;
		table-layout: fixed;
		border-collapse: separate;
		border-spacing: 0 16px;
	}

	.metric-card {
		background: rgb(248, 248, 248);
		border-radius: 10px;
		padding: 15px;
		position: relative;
	}

	.metric-header {
		margin-bottom: 8px;
	}

	.metric-label {
		font-size: 13px;
		font-weight: 600;
		text-transform: uppercase;
		letter-spacing: 0.5px;
		color: #666;
	}

	.metric-change {
		font-size: 13px;
		font-weight: 600;
		padding: 2px 6px;
		border-radius: 4px;
		width: max-content;
	}

	.metric-value-row {
		white-space: nowrap;
	}

	.metric-value-row>* {
		vertical-align: middle;
	}

	.metric-icon {
		width: 40px;
		height: 40px;
		border-radius: 50%;
		font-size: 12px;
		color: white;
		display: inline-block;
	}

	.metric-value {
		font-size: 18px;
		font-weight: 700;
		color: #333;
		line-height: 1;
		display: inline;
	}

	.large-metric-icon {
		width: 50px;
		height: 50px;
	}

	.video-gif {
		width: 100%;
		border-radius: 15px;
	}
	.video-metric-icon {
		width: 30px;
		height: 30px;
		display: inline-block;
	}

	.video-metric-value {
		font-size: 15px;
		font-weight: 700;
		color: #333;
		line-height: 1;
		display: inline;
	}

	.ai-insights {
		background: #1a1a1a;
		color: white;
		border-radius: 12px;
		padding: 5%;
		margin-bottom: 16px;
	}

	.ai-insights-header {
		margin-bottom: 10px;
	}

	.ai-insights-header>* {
		vertical-align: middle;
	}

	.ai-insights-icon {
		font-size: 20px;
		width: 30px;
		height: 30px;
		display: inline-block;
	}

	.ai-insights-title {
		font-size: 14px;
		font-weight: normal;
		text-transform: uppercase;
		display: inline;
	}

	.ai-insights-content p {
		margin: 0;
	}

	.button-link {
		color: unset;
		text-decoration: unset;
		display: inline-block;
		width: fit-content;
		margin-bottom: 16px;
	}

	.button {
		margin: 0;
		padding: 20px 40px;
		border-radius: 10px;
		width: fit-content;
		background-color: #1a1a1a;
	}

	.button-text {
		color: #FFFFFF;
		font-size: 20px;
		font-weight: 600;
		white-space: nowrap;
	}

	.footer {
		width: 100%;
	}

	.footer p {
		font-size: 13px;
		color: rgb(183, 183, 183);
		margin: 0;
		margin-bottom: 7px;
	}

	.footer a {
		color: unset;
	}

	.social-icons {
		width: max-content;
	}

	.social-icon {
		width: 30px;
		height: 30px;
		margin-left: 8px;
	}

	@media (max-width: 600px) {
		.button {
			padding: 20px 25px;
		}
		.button-text {
			font-size: 18px;
		}
	}
</style>