<% 
    function formatPlaytime(seconds) {
        seconds = Number(seconds) || 0;
        const h = Math.floor(seconds / 3600);
        const m = Math.floor((seconds % 3600) / 60);
        const s = seconds % 60;
        let result = '';
        if (h > 0) result += `${h}h `;
        if (m > 0 || h > 0) result += `${m}m `;
        result += `${s}s`;
        return result.trim();
    }
%>
<div class="section-title" style="margin: 0;">This <%= range %>, your videos had:</div>

<table class="metrics-table">
    <tr>
        <td style="padding-right: 8px;">
            <div class="metric-card">
                <div class="metric-header">
                    <table>
                        <tr>
                            <td>
                                <div class="metric-label">IMPRESSIONS</div>
                            </td>
                            <td class="middle-gap"></td>
                            <td>
                                <div class="metric-change <%= metrics.impressions.change >= 0 ? 'positive' : 'negative' %>">
                                    <%= metrics.impressions.change >= 0 ? '↑' : '↓' %> <%= Math.abs(metrics.impressions.change) %>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="metric-value-row">
                    <div class="metric-icon impressions">
                        <img alt="logo" class="logo" width="30" src="<%= cdnHost %>reports/impressions-icon-v1.png" />
                    </div>
                    <div class="metric-value"><%= metrics.impressions.value %></div>
                </div>
            </div>
        </td>
        <td style="padding-left: 8px;">
            <div class="metric-card">
                <div class="metric-header">
                    <table>
                        <tr>
                            <td>
                                <div class="metric-label">PLAYS</div>
                            </td>
                            <td class="middle-gap"></td>
                            <td>
                                <div class="metric-change <%= metrics.plays.change >= 0 ? 'positive' : 'negative' %>">
                                    <%= metrics.plays.change >= 0 ? '↑' : '↓' %> <%= Math.abs(metrics.plays.change) %>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="metric-value-row">
                    <div class="metric-icon plays">
                        <img alt="logo" class="logo" width="30" src="<%= cdnHost %>reports/plays-icon-v1.png" />
                    </div>
                    <div class="metric-value"><%= metrics.plays.value %></div>
                </div>
            </div>
        </td>
    </tr>
    <tr>
        <td style="padding-right: 8px;">
            <div class="metric-card">
                <div class="metric-header">
                    <table>
                        <tr>
                            <td>
                                <div class="metric-label">CLICKS</div>
                            </td>
                            <td class="middle-gap"></td>
                            <td>
                                <div class="metric-change <%= metrics.clicks.change >= 0 ? 'positive' : 'negative' %>">
                                    <%= metrics.clicks.change >= 0 ? '↑' : '↓' %> <%= Math.abs(metrics.clicks.change) %>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="metric-value-row">
                    <div class="metric-icon clicks">
                        <img alt="logo" class="logo" width="30" src="<%= cdnHost %>reports/clicks-icon-v1.png" />
                    </div>
                    <div class="metric-value">
                        <%= metrics.clicks.value %>
                    </div>
                </div>
            </div>
        </td>
        <td style="padding-left: 8px;">
            <div class="metric-card">
                <div class="metric-header">
                    <table>
                        <tr>
                            <td>
                                <div class="metric-label">LEADS</div>
                            </td>
                            <td class="middle-gap"></td>
                            <td>
                                <div class="metric-change <%= metrics.emails.change >= 0 ? 'positive' : 'negative' %>">
                                    <%= metrics.emails.change >= 0 ? '↑' : '↓' %> <%= Math.abs(metrics.emails.change) %>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="metric-value-row">
                    <div class="metric-icon leads">
                        <img alt="logo" class="logo" width="30" src="<%= cdnHost %>reports/emails-icon-v1.png" />
                    </div>
                    <div class="metric-value">
                        <%= metrics.leads.value %>
                    </div>
                </div>
            </div>
        </td>
    </tr>
</table>
