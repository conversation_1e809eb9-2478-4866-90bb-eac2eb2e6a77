<% 
    function formatDateRange(fromDate, toDate, withYear = false) {
        if (!fromDate || !toDate) return '';
        const options = withYear ? { month: 'long', day: 'numeric', year: 'numeric' } : { month: 'long', day: 'numeric' };
        const from = new Date(fromDate).toLocaleDateString('en-US', options);
        const to = new Date(toDate).toLocaleDateString('en-US', options);
        return `${from} - ${to}`;
    }
%>

<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<title>Weekly Report</title>
		<%- include('../shared/styles') %>
		<%- include('styles') %>
	</head>

	<body>
		<div class="email-report-container">
			<%- include('header', {
				reportType: 'WEEKLY REPORT',
				reportPeriod: formatDateRange(fromDate, toDate, true)
			}) %>
		
			<% if (videos.length> 0) { %>
				<div class="grey-box">
					<div class="title">Your weekly performance metrics from <span class="dates"><%= formatDateRange(fromDate, toDate, false) %></span> are here!</div>
				</div>

				<%- include('performance-metrics', {
					metrics: metrics,
					range: "week"
				}) %>

				<div class="center">
					<div class="large-metric-icon">
						<img width="30" alt="logo" class="logo" src="<%= cdnHost %>reports/award-icon-v1.png" />
					</div>
				</div>

				<div class="section-title">Your top performing video was:</div>

				<%- include('top-video-card', {
					videoTitle: videos[0].title,
					videoShareLink: videos[0].shareLink,
					videoGifUrl: videos[0].gifUrl,
					videoMetrics: {
						sessions: videos[0].sessions,
						clicks: videos[0].clicks,
						calls: videos[0].calls,
						emails: videos[0].emails,
						plays: videos[0].plays,
						playtime: videos[0].playtime
					}
				}) %>

				<%- include('ai-insights.ejs', {
					insight: insightSummary
				}) %>

				<%- include('summary.ejs', {
					emailSummary: emailSummary,
					downloadReportUrl: downloadReportUrl
				}) %>
			<% } %>

			<%- include('footer', {
				instagramUrl: instagramUrl,
				xUrl: xUrl,
				youtubeUrl: youtubeUrl,
				privacyPolicyUrl: privacyPolicyUrl,
				termsOfServiceUrl: termsOfServiceUrl
			}) %>
		</div>
	</body>
</html>