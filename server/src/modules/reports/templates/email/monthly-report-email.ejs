<% 
    function formatDateRange(fromDate, toDate, withYear = false) {
        if (!fromDate || !toDate) return '';
        const options = withYear ? { month: 'long', day: 'numeric', year: 'numeric' } : { month: 'long', day: 'numeric' };
        const from = new Date(fromDate).toLocaleDateString('en-US', options);
        const to = new Date(toDate).toLocaleDateString('en-US', options);
        return `${from} - ${to}`;
    }
%>

<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<title>Monthly Report</title>
		<%- include('../shared/styles') %>
		<%- include('styles') %>
	</head>

	<body>
		<div class="email-report-container">
			<%- include('header', {
				reportType: 'MONTHLY REPORT',
				reportPeriod: formatDateRange(fromDate, toDate, true)
			}) %>
		
			<% if (videos.length > 0) { %>
				<div class="grey-box">
					<div class="title">Your monthly performance metrics from <span class="dates"><%= formatDateRange(fromDate, toDate, false) %></span> are here!</div>
				</div>

				<%- include('performance-metrics', {
					metrics: metrics,
					range: "month"
				}) %>

				<div class="center">
					<div class="large-metric-icon">
						<img width="30" alt="logo" class="logo" src="<%= cdnHost %>reports/award-icon-v1.png" />
					</div>
				</div>
				<div class="section-title">Your top <%= (videos.length >= 3) ? 3 : "" %> performing videos were:</div>

				<% for(let i = 0; i < Math.min(videos.length, 3); i++) { %>
					<%- include('top-video-card', {
						videoTitle: videos[i].title,
						videoShareLink: videos[i].shareLink,
						videoGifUrl: videos[i].gifUrl,
						videoMetrics: {
							sessions: videos[i].sessions,
							clicks: videos[i].clicks,
							calls: videos[i].calls,
							emails: videos[i].emails,
							plays: videos[i].plays,
							playtime: videos[i].playtime
						}
					}) %>
				<% } %>

				<%- include('ai-insights', {
					insight: insightSummary
				}) %>

				<%- include('summary.ejs', {
					emailSummary: emailSummary,
					downloadReportUrl: downloadReportUrl
				}) %>
			<% } %>

			<%- include('footer', {
				instagramUrl: instagramUrl,
				xUrl: xUrl,
				youtubeUrl: youtubeUrl,
				privacyPolicyUrl: privacyPolicyUrl,
				termsOfServiceUrl: termsOfServiceUrl
			}) %>
		</div>
	</body>
</html>