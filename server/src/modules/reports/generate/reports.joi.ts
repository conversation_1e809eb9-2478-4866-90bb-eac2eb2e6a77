import Jo<PERSON> from "joi";
import { BasePublicJoi } from "../../base/base.joi";
import {
	ReportRange,
	ReportsPost
} from "./reports.types";

function getDefaultDate(): Date {
	const now = new Date();
	now.setUTCHours(0, 0, 0, 0);
	return now;
}

export const ReportsPostSchema = BasePublicJoi.append<ReportsPost>({
	range: Joi.string()
		.uppercase()
		.valid(...Object.values(ReportRange))
		.required(),
	date: Joi.string()
		.optional()
		.custom((value, helpers) => {
			// Accept YYYY, YYYY-MM, or full date
			if (/^\d{4}$/.test(value)) return new Date(`${value}-01-01`);
			if (/^\d{4}-\d{2}$/.test(value)) return new Date(`${value}-01`);
			const date = new Date(value);
			if (isNaN(date.getTime())) return helpers.error("any.invalid");
			return date;
		})
		.default(getDefaultDate),
	accountId: Joi.string().optional(),
	redirectEmail: Joi.string().email().optional()
});
