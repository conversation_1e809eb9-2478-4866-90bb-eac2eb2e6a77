import { ReportsGenerateController } from "./reports.controller";
import {
	createServer,
	initExpressRoutes
} from "../../../express";
import supertest from "supertest";
import { ReportsGenerateModel } from "./reports.model";
import { ReportsPostSchema } from "./reports.joi";

jest.mock("./reports.model");
jest.mock("./reports.joi");

const expressApp = createServer();
const controller = new ReportsGenerateController();
controller.use(expressApp, "/api/reports/generate");
initExpressRoutes(expressApp);

describe("ReportsGenerateController", () => {
	beforeEach(() => {
		jest.clearAllMocks();
	});

	it("should return 202 and jobId on valid POST", async () => {
		const mockJob = { _id: "jobid123" };
		(ReportsPostSchema.validateAsync as jest.Mock).mockResolvedValue({
			range: "WEEKLY",
			date: new Date(),
			accountId: "abc"
		});
		(ReportsGenerateModel as any).mockImplementation(() => ({
			createJob: jest.fn().mockResolvedValue(mockJob)
		}));

		const res = await supertest(expressApp)
			.post("/api/reports/generate")
			.query({ range: "WEEKLY", date: "2024-07-10", accountId: "abc" });

		expect(res.status).toBe(202);
		expect(res.body).toEqual({ jobId: "jobid123" });
	});

	it("should return error if validation fails", async () => {
		(ReportsPostSchema.validateAsync as jest.Mock).mockRejectedValue(new Error("Invalid input"));

		const res = await supertest(expressApp)
			.post("/api/reports/generate")
			.query({ range: "INVALID" });

		expect(res.status).toBe(400);
		expect(res.body).toHaveProperty("error");
	});

	it("should return error if model throws", async () => {
		(ReportsPostSchema.validateAsync as jest.Mock).mockResolvedValue({
			range: "WEEKLY",
			date: new Date(),
			accountId: "abc"
		});
		(ReportsGenerateModel as any).mockImplementation(() => ({
			createJob: jest.fn().mockRejectedValue(new Error("DB error"))
		}));

		const res = await supertest(expressApp)
			.post("/api/reports/generate")
			.query({ range: "WEEKLY", date: "2024-07-10", accountId: "abc" });

		expect(res.status).toBe(500);
		expect(res.body).toHaveProperty("error");
	});
});
