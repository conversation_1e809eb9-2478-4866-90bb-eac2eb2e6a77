export enum ReportRange {
	WEEKLY = "WEEKLY",
	MONTHLY = "MONTHLY"
}

export interface ReportDateRange {
	startDate: Date;
	endDate: Date;
	range: ReportRange
}

export interface ReportDateRanges {
	currentRange: ReportDateRange;
	comparisonRange: ReportDateRange;
}

export interface ReportsPost {
	range: ReportRange;
	date: Date;
	accountId?: string;
	redirectEmail?: string;
}

export interface ReportTemplateData {
	fromDate: string;
	toDate: string;
	month: string;

	cdnHost: string;

	emailSummary: string;

	metrics: {
		impressions: MetricWithChange;
		plays: MetricWithChange;
		clicks: MetricWithChange;
		emails: Metric<PERSON>ithChange;
		leads: Metric<PERSON>ithChange;
		calls: Metric<PERSON><PERSON><PERSON>hange;
		engaged: MetricWithChange;
		playtime: MetricWithChange;
	};

	dailyPlaytimeData: {
		labels: string[];
		values: number[];
	};

	dailyPerformanceData: {
		labels: string[];
		emails: number[];
		calls: number[];
		clicks: number[];
	};

	dailyPlaysData: {
		current: number[];
		previous: number[];
	};

	metricsData: {
		engagementRate: MetricWithChange;
		playRate: MetricWithChange;
		clickthroughRate: MetricWithChange;
		leadCount: MetricWithChange;
		playsPerSession: MetricWithChange;
		avgPlaytime: MetricWithChange;
	};

	videos: VideoInfo[];

	insightSummary: string;

	helpEmail: string;
	downloadReportUrl: string;

	instagramUrl: string;
	xUrl: string;
	youtubeUrl: string;
	privacyPolicyUrl: string;
	termsOfServiceUrl: string;
	appName: string;
}

interface MetricWithChange {
	value: number;
	change: number;
}

interface VideoInfo {
	position: number;
	change: "up" | "down" | "same";
	changeValue: number;
	title: string;
	shareLink: string;
	posterUrl: string;
	gifUrl: string;
	length: number;
	sessions: number;
	plays: number;
	clicks: number;
	emails: number;
	calls: number;
	playtime: string;
	score: number;
}
