import mongoose, { ClientSession } from "mongoose";
import {
	ReportsAggregate,
	ReportsAggregateVideo
} from "../aggregate/aggregate.types";
import {
	JobsStatus,
	JobsType,
	Job
} from "../../job/job.model";
import { JobService } from "../../job/job.service";
import { getSecrets } from "../../secrets/secrets.model";
import { JobContainerModel } from "../../job/container/job.container.model";
import { AccountDBModel } from "../../account/accountDB.model";
import { IAccount } from "../../account/account.interfaces";
import ejs from "ejs";
import path from "path";
import {
	EmailInput,
	SendGridModel
} from "../../sendgrid/sendgrid.model";
import { AccountModel } from "../../account/account.model";
import {
	ReportsPost,
	ReportTemplateData,
	ReportRange,
	ReportDateRange,
	ReportDateRanges
} from "./reports.types";
import { ReportsAggregateModel } from "../aggregate/aggregate.model";
import { validateBackslash } from "../../../utils/helpers/gp.helper";
import { ReportsAIModel } from "../ai/ai.model";
import {
	AIReportSummary,
	ReportMetricsData
} from "../ai/ai.interfaces";
import juice from "juice";
import { GCPStorageProvider } from "../../storage/providers/gcp.storage";
import { StorageCacheControl } from "../../storage/storage.model";
import { StorageService } from "../../storage/storage.service";

export class ReportsGenerateModel {
	private session: ClientSession | null;

	constructor(session: ClientSession | null) {
		this.session = session;
	}

	public async createJob(payload: ReportsPost): Promise<Job> {
		const date = new Date(payload.date);
		date.setUTCHours(0, 0, 0, 0);

		const data: any = {
			type: JobsType.REPORTS_GENERATE,
			status: JobsStatus.CREATED,
			statusMessage: "reports job has been created.",
			reportRange: payload.range,
			reportDate: date
		};
		if (payload.accountId) {
			data.accountId = payload.accountId;
		}
		if (payload.redirectEmail) {
			data.redirectEmail = payload.redirectEmail;
		}

		const jobService = new JobService(this.session);
		const job = await jobService.createJob(data);

		const jobTimeoutSeconds = 3600 * 24;
		const secrets = await getSecrets();
		const jobContainerModel = new JobContainerModel(this.session, secrets.storage.isLocal);
		await jobContainerModel.runJobWorker(job._id.toString(), jobTimeoutSeconds);

		return job;
	}

	public async processAccounts(job: Job): Promise<void> {
		if (!job.reportDate) {
			throw new Error("Reports job does not have a reportDate set.");
		}

		if (!job.reportRange) {
			throw new Error("Reports job does not have an reportRange set.");
		}

		const reportDateRange = this.getReportDateRange(job.reportDate, job.reportRange);
		const reportComparisonRange = this.getComparisonReportDateRange(job.reportDate, job.reportRange);

		const jobService = new JobService(null);
		await jobService.updateJobStatus(job._id.toString(), JobsStatus.RUNNING, "Reports job is running...");

		const reportDateRanges: ReportDateRanges = {
			currentRange: reportDateRange,
			comparisonRange: reportComparisonRange
		};

		if (job.accountId) {
			const accountModel = new AccountModel(this.session);
			const account = await accountModel.readOneById(job.accountId.toString());
			await jobService.updateJobStatus(
				job._id.toString(),
				JobsStatus.RUNNING,
				`Processing account ${account._id}...`
			);
			await this.processAccount(account, reportDateRanges, job.reportRange, job.redirectEmail);
		} else {
			const accountsDBModel = new AccountDBModel(this.session);
			const accountsCursor = accountsDBModel.collection.find<IAccount>({});
			for await (const account of accountsCursor) {
				await jobService.updateJobStatus(
					job._id.toString(),
					JobsStatus.RUNNING,
					`Processing account ${account._id}...`
				);
				await this.processAccount(account, reportDateRanges, job.reportRange, job.redirectEmail);
			}
		}

		await jobService.updateJobStatus(job._id.toString(), JobsStatus.COMPLETE, "Reports completed successfully.");
	}

	private async processAccount(
		account: IAccount,
		reportDateRanges: ReportDateRanges,
		reportRange: ReportRange,
		redirectEmail: string | undefined
	): Promise<void> {
		const reportsAggregateModel = new ReportsAggregateModel(this.session);

		const currentRange = reportDateRanges.currentRange;
		const comparisonRange = reportDateRanges.comparisonRange;

		const currentAggregate = await reportsAggregateModel.readAggregatedRangeFromDB(
			account,
			currentRange.startDate,
			currentRange.endDate
		);

		// if there are no current videos, skip processing
		const videos = currentAggregate.map(agg => agg.videos).flat();
		if (!videos.length) {
			return;
		}

		const comparisonAggregate = await reportsAggregateModel.readAggregatedRangeFromDB(
			account,
			comparisonRange.startDate,
			comparisonRange.endDate
		);

		const reportFilePath = this.createReportFilePath(account, currentRange);

		const templateData: ReportTemplateData = await this.createTemplateData(
			currentAggregate,
			comparisonAggregate,
			currentRange,
			reportFilePath
		);

		await this.createHTMLReport(templateData, reportFilePath, reportRange);

		await this.createEmailReport(templateData, account, currentRange, redirectEmail);
	}

	private getReportDateRange(date: Date, range: ReportRange): ReportDateRange {
		const d = new Date(date);
		let startDate: Date, endDate: Date;

		switch (range) {
			case ReportRange.WEEKLY: {
				const day = d.getUTCDay();
				const lastSunday = new Date(d);
				lastSunday.setUTCDate(d.getUTCDate() - day);
				lastSunday.setUTCHours(23, 59, 59, 999);
				startDate = new Date(lastSunday);
				startDate.setUTCDate(lastSunday.getUTCDate() - 6);
				startDate.setUTCHours(0, 0, 0, 0);
				endDate = lastSunday;
				break;
			}
			case ReportRange.MONTHLY: {
				const prevMonth = new Date(Date.UTC(d.getUTCFullYear(), d.getUTCMonth() - 1, 1));
				startDate = new Date(prevMonth);
				startDate.setUTCHours(0, 0, 0, 0);
				endDate = new Date(Date.UTC(prevMonth.getUTCFullYear(), prevMonth.getUTCMonth() + 1, 0));
				endDate.setUTCHours(23, 59, 59, 999);
				break;
			}
			default:
				throw new Error(`Unknown reportRange: ${range}`);
		}
		return { startDate, endDate, range };
	}

	private getComparisonReportDateRange(date: Date, range: ReportRange): ReportDateRange {
		const d = new Date(date);
		let comparisonDate: Date;

		switch (range) {
			case ReportRange.WEEKLY: {
				comparisonDate = new Date(d);
				comparisonDate.setUTCDate(d.getUTCDate() - 7);
				break;
			}
			case ReportRange.MONTHLY: {
				comparisonDate = new Date(Date.UTC(d.getUTCFullYear(), d.getUTCMonth() - 1, d.getUTCDate()));
				break;
			}
			default:
				throw new Error(`Unknown reportRange: ${range}`);
		}
		return this.getReportDateRange(comparisonDate, range);
	}

	private async createTemplateData(
		current: ReportsAggregate[],
		comparison: ReportsAggregate[],
		dateRange: ReportDateRange,
		reportsFilePath: string
	): Promise<ReportTemplateData> {
		const secrets = await getSecrets();
		const downloadReportUrl = `${validateBackslash(secrets.cdn.host)}${reportsFilePath}`;

		const shareEndpoint = validateBackslash(secrets.appConfig.shareEndpoint);

		const compiledCurrent = this.compileAggregates(current);
		const compiledComparison = this.compileAggregates(comparison);

		compiledCurrent.videos.sort((a, b) => b.engagementScore - a.engagementScore);
		compiledComparison.videos.sort((a, b) => b.engagementScore - a.engagementScore);

		const currentRankMap = new Map<string, number>();
		compiledCurrent.videos.forEach((v, i) => {
			currentRankMap.set(v.videoId.toString(), i);
		});
		const comparisonRankMap = new Map<string, number>();
		compiledComparison.videos.forEach((v, i) => {
			comparisonRankMap.set(v.videoId.toString(), i);
		});

		const videoInfos = compiledCurrent.videos.map((video, idx) => {
			const videoIdStr = video.videoId.toString();
			const prevRank = comparisonRankMap.has(videoIdStr)
				? comparisonRankMap.get(videoIdStr) : null;
			let change: "up" | "down" | "same" = "same";
			let changeValue = 0;
			if (typeof prevRank === "number") {
				changeValue = prevRank - idx;
				if (changeValue > 0) change = "up";
				else if (changeValue < 0) change = "down";
			}
			return {
				position: idx + 1,
				change,
				changeValue: Math.abs(changeValue),
				title: video.interactiveVideoTitle,
				shareLink: `${shareEndpoint}v/${video.interactiveVideoId}`,
				posterUrl: video.posterURL,
				gifUrl: video.gifURL,
				length: video.videoLengthSeconds,
				sessions: 0,
				plays: video.plays,
				clicks: video.clicks,
				emails: video.emailLeads,
				calls: video.callLeads,
				playtime: video.playTimeSeconds.toString(),
				score: Math.round(video.engagementScore)
			};
		});

		const dailyPlaytimeLabels = current.map(agg => {
			const date = agg.captureDate instanceof Date ? agg.captureDate : new Date(agg.captureDate);
			return date.toLocaleDateString("default", { month: "short", day: "numeric" });
		});
		const dailyPlaytimeValues = current.map(agg => {
			const totalSeconds = agg.videos.reduce((sum, v) => sum + (v.playTimeSeconds || 0), 0);
			return +(totalSeconds / 3600).toFixed(2);
		});

		const dailyPerformanceLabels = current.map(agg => {
			const date = agg.captureDate instanceof Date ? agg.captureDate : new Date(agg.captureDate);
			return date.toLocaleDateString("default", { month: "short", day: "numeric" });
		});
		const dailyEmails = current.map(agg => agg.videos.reduce((sum, v) => sum + (v.emailLeads || 0), 0));
		const dailyCalls = current.map(agg => agg.videos.reduce((sum, v) => sum + (v.callLeads || 0), 0));
		const dailyClicks = current.map(agg => agg.videos.reduce((sum, v) => sum + (v.clicks || 0), 0));

		const templateData: ReportTemplateData = {
			appName: secrets.appConfig.appName,
			fromDate: dateRange.startDate.toISOString(),
			toDate: dateRange.endDate.toISOString(),
			month: dateRange.startDate.toLocaleString("default", { month: "long" }),
			cdnHost: validateBackslash(secrets.cdn.host),
			emailSummary: "",
			metrics: {
				impressions: {
					value: compiledCurrent.totalImpressions,
					change: compiledCurrent.totalImpressions - compiledComparison.totalImpressions
				},
				plays: {
					value: compiledCurrent.totalPlays,
					change: compiledCurrent.totalPlays - compiledComparison.totalPlays
				},
				clicks: {
					value: compiledCurrent.totalClicks,
					change: compiledCurrent.totalClicks - compiledComparison.totalClicks
				},
				emails: {
					value: compiledCurrent.totalEmails,
					change: compiledCurrent.totalEmails - compiledComparison.totalEmails
				},
				leads: {
					value: compiledCurrent.totalEmails + compiledCurrent.totalCalls,
					change: (compiledCurrent.totalEmails + compiledCurrent.totalCalls)
						- (compiledComparison.totalEmails + compiledComparison.totalCalls)
				},
				calls: {
					value: compiledCurrent.totalCalls,
					change: compiledCurrent.totalCalls - compiledComparison.totalCalls
				},
				engaged: {
					value: compiledCurrent.totalEngagedSessions,
					change: compiledCurrent.totalEngagedSessions - compiledComparison.totalEngagedSessions
				},
				playtime: {
					value: compiledCurrent.totalPlayTime,
					change: compiledCurrent.totalPlayTime - compiledComparison.totalPlayTime
				}
			},
			dailyPlaytimeData: {
				labels: dailyPlaytimeLabels,
				values: dailyPlaytimeValues
			},
			dailyPerformanceData: {
				labels: dailyPerformanceLabels,
				emails: dailyEmails,
				calls: dailyCalls,
				clicks: dailyClicks
			},
			dailyPlaysData: {
				current: current.map(agg => agg.videos.reduce((sum, v) => sum + (v.plays || 0), 0)),
				previous: comparison.map(agg => agg.videos.reduce((sum, v) => sum + (v.plays || 0), 0))
			},
			metricsData: {
				engagementRate: {
					value: getEngagementRate(compiledCurrent.totalEngagedSessions, compiledCurrent.totalImpressions),
					change:
						getEngagementRate(compiledCurrent.totalEngagedSessions, compiledCurrent.totalImpressions) -
						getEngagementRate(compiledComparison.totalEngagedSessions, compiledComparison.totalImpressions)
				},
				playRate: {
					value: getPlayRate(compiledCurrent.totalPlays, compiledCurrent.totalImpressions),
					change:
						getPlayRate(compiledCurrent.totalPlays, compiledCurrent.totalImpressions) -
						getPlayRate(compiledComparison.totalPlays, compiledComparison.totalImpressions)
				},
				clickthroughRate: {
					value: getClickthroughRate(compiledCurrent.totalClicks, compiledCurrent.totalPlays),
					change:
						getClickthroughRate(compiledCurrent.totalClicks, compiledCurrent.totalPlays) -
						getClickthroughRate(compiledComparison.totalClicks, compiledComparison.totalPlays)
				},
				leadCount: {
					value: compiledCurrent.totalEmails + compiledCurrent.totalCalls,
					change: (compiledCurrent.totalEmails + compiledCurrent.totalCalls)
						- (compiledComparison.totalEmails + compiledComparison.totalCalls)
				},
				playsPerSession: {
					value: getPlaysPerSession(compiledCurrent.totalPlays, compiledCurrent.totalEngagedSessions),
					change:
						getPlaysPerSession(compiledCurrent.totalPlays, compiledCurrent.totalEngagedSessions) -
						getPlaysPerSession(compiledComparison.totalPlays, compiledComparison.totalEngagedSessions)
				},
				avgPlaytime: {
					value: getAvgPlaytime(compiledCurrent.totalPlayTime, compiledCurrent.totalPlays),
					change:
						getAvgPlaytime(compiledCurrent.totalPlayTime, compiledCurrent.totalPlays) -
						getAvgPlaytime(compiledComparison.totalPlayTime, compiledComparison.totalPlays)
				}
			},
			videos: videoInfos,
			insightSummary: "",
			helpEmail: secrets.appConfig.helpEmail ?? "",
			downloadReportUrl: downloadReportUrl,
			instagramUrl: secrets.appConfig.instagramURL ?? "",
			xUrl: secrets.appConfig.xURL ?? "",
			youtubeUrl: secrets.appConfig.youtubeURL ?? "",
			privacyPolicyUrl: secrets.appConfig.privacyPolicyURL ?? "",
			termsOfServiceUrl: secrets.appConfig.termsOfServiceURL ?? ""
		};

		try {
			const aiReportSummary = await this.getAIInsights(templateData);

			templateData.emailSummary = aiReportSummary.emailSummary;
			templateData.insightSummary = aiReportSummary.insightSummary;
		} catch (err: unknown) {
			templateData.emailSummary = "";
			templateData.insightSummary = "";
		}

		return templateData;
	}

	private compileAggregates(aggregates: ReportsAggregate[]): ReportsAggregate {
		if (!aggregates.length) {
			const now = new Date();
			return {
				accountId: new mongoose.Types.ObjectId() as any,
				createdAt: now,
				updatedAt: now,
				captureDate: now,
				totalPlays: 0,
				totalPlayTime: 0,
				totalEmails: 0,
				totalCalls: 0,
				totalClicks: 0,
				userSessionIds: [],
				totalEngagedSessions: 0,
				totalImpressions: 0,
				videos: []
			};
		}

		const accountId = aggregates[0].accountId;
		const createdAt = aggregates[aggregates.length - 1].createdAt;
		const updatedAt = aggregates[aggregates.length - 1].updatedAt;
		const captureDate = aggregates[aggregates.length - 1].captureDate;

		const totalPlays = aggregates.reduce((sum, agg) => sum + (agg.totalPlays || 0), 0);
		const totalPlayTime = aggregates.reduce((sum, agg) => sum + (agg.totalPlayTime || 0), 0);
		const totalEmails = aggregates.reduce((sum, agg) => sum + (agg.totalEmails || 0), 0);
		const totalCalls = aggregates.reduce((sum, agg) => sum + (agg.totalCalls || 0), 0);
		const totalClicks = aggregates.reduce((sum, agg) => sum + (agg.totalClicks || 0), 0);
		const engagedSessionIds = new Set(aggregates.flatMap(obj => obj.userSessionIds.map(id => id.toString())));
		const totalEngagedSessions = engagedSessionIds.size;
		const totalImpressions = aggregates.reduce((sum, agg) => sum + (agg.totalImpressions || 0), 0);

		const videoMap = new Map<string, ReportsAggregateVideo[]>();
		aggregates.forEach(agg => {
			agg.videos.forEach(video => {
				const key = video.videoId.toString();
				if (!videoMap.has(key)) videoMap.set(key, []);
				const arr = videoMap.get(key);
				if (arr) {
					arr.push(video);
				} else {
					videoMap.set(key, [video]);
				}
			});
		});

		const compiledVideos: ReportsAggregateVideo[] = [];
		for (const videos of videoMap.values()) {
			const first = videos[0];
			const plays = videos.reduce((sum, v) => sum + (v.plays || 0), 0);
			const clicks = videos.reduce((sum, v) => sum + (v.clicks || 0), 0);
			const emailLeads = videos.reduce((sum, v) => sum + (v.emailLeads || 0), 0);
			const callLeads = videos.reduce((sum, v) => sum + (v.callLeads || 0), 0);
			const playTimeSeconds = videos.reduce((sum, v) => sum + (v.playTimeSeconds || 0), 0);
			const engagementScore = videos.reduce((sum, v) => sum + (v.engagementScore || 0), 0) / videos.length;
			const likes = videos.reduce((sum, v) => sum + (v.likes || 0), 0);

			const urlMap = new Map<string, number>();
			videos.forEach(v => {
				v.urlInteractions?.forEach(ui => {
					urlMap.set(ui.url, (urlMap.get(ui.url) || 0) + (ui.count || 0));
				});
			});
			const urlInteractions = Array.from(urlMap.entries()).map(([url, count]) => ({ url, count }));

			// Use the maximum videoLengthSeconds found for this video
			const videoLengthSeconds = videos.reduce(
				(max, v) => (v.videoLengthSeconds && v.videoLengthSeconds > max ? v.videoLengthSeconds : max),
				0
			);

			compiledVideos.push({
				videoId: first.videoId,
				interactiveVideoId: first.interactiveVideoId,
				gifURL: first.gifURL,
				posterURL: first.posterURL,
				interactiveVideoTitle: first.interactiveVideoTitle,
				plays,
				clicks,
				emailLeads,
				callLeads,
				playTimeSeconds,
				engagementScore,
				likes,
				urlInteractions,
				videoLengthSeconds
			});
		}

		return {
			accountId,
			createdAt,
			updatedAt,
			captureDate,
			totalPlays,
			totalPlayTime,
			totalEmails,
			totalCalls,
			totalClicks,
			userSessionIds: [],
			totalEngagedSessions: totalEngagedSessions,
			totalImpressions,
			videos: compiledVideos
		};
	}

	private createReportFilePath(
		account: IAccount,
		dateRange: ReportDateRange
	): string {
		const yearStart = dateRange.startDate.getUTCFullYear();
		const monthStart = (dateRange.startDate.getUTCMonth() + 1).toString().padStart(2, "0");
		const dayStart = dateRange.startDate.getUTCDate().toString().padStart(2, "0");
		const yearEnd = dateRange.endDate.getUTCFullYear();
		const monthEnd = (dateRange.endDate.getUTCMonth() + 1).toString().padStart(2, "0");
		const dayEnd = dateRange.endDate.getUTCDate().toString().padStart(2, "0");
		const date = `${yearStart}-${monthStart}-${dayStart}_${yearEnd}-${monthEnd}-${dayEnd}`;
		return `report-data/${account._id}/${dateRange.range}/${date}-report.html`;
	}

	private async createHTMLReport(
		templateData: ReportTemplateData,
		reportFilePath: string,
		range: ReportRange
	): Promise<void> {
		const html = await this.renderHTMLTemplate(templateData, range);

		const secrets = await getSecrets();
		const storageProvider = new GCPStorageProvider(
			"https://storage.googleapis.com",
			secrets.storage.bucketName,
			secrets.storage.host,
			secrets.storage.credentials
		);
		const storageCacheControl: StorageCacheControl = {
			maxAge: 3600,
			public: true
		};
		const storageService = new StorageService(storageProvider);

		await storageService.writeFromBuffer(
			reportFilePath,
			Buffer.from(html),
			storageCacheControl
		);
	}

	private async renderHTMLTemplate(templateData: ReportTemplateData, range: ReportRange): Promise<string> {
		let template = "";
		if (range === ReportRange.WEEKLY) {
			template = "../templates/html/weekly-report.ejs";
		} else if (range === ReportRange.MONTHLY) {
			template = "../templates/html/monthly-report.ejs";
		}

		const templatePath = path.resolve(
			__dirname,
			template
		);

		return await ejs.renderFile(templatePath, templateData);
	}

	private async createEmailReport(
		templateData: ReportTemplateData,
		account: IAccount,
		dateRange: ReportDateRange,
		redirectEmail: string | undefined
	): Promise<void> {
		const secrets = await getSecrets();
		const accountModel = new AccountModel(this.session);
		const ownerUser = await accountModel.getOwnerUser(account);

		const subject = this.generateEmailSubjectLine(
			dateRange.range,
			ownerUser.firstName,
			templateData.metrics.plays.value,
			dateRange.startDate
		);

		const emailHtml = await this.renderEmailTemplate(templateData, dateRange.range);
		const inlinedEmailHtml = juice(emailHtml);

		const emailInput: EmailInput = {
			to: (redirectEmail) ? redirectEmail : ownerUser.email,
			subject,
			html: inlinedEmailHtml,
			fromName: secrets.appConfig.appName
		};

		const sendGridModel = new SendGridModel();
		await sendGridModel.send(emailInput);
	}

	private generateEmailSubjectLine(
		range: ReportRange,
		ownerFirstName: string,
		currentPlays: number,
		reportDate: Date
	): string {
		const safeName = ownerFirstName && ownerFirstName.trim() ? ownerFirstName.trim() : "there";

		if (range === ReportRange.WEEKLY) {
			return `Hey ${safeName}, your videos were played ${currentPlays} times last week!`;
		} else if (range === ReportRange.MONTHLY) {
			const month = reportDate.toLocaleString("default", { month: "long" });
			return `Your ${month} Video Review`;
		}

		return "Your Video Report";
	}

	private async renderEmailTemplate(templateData: ReportTemplateData, range: ReportRange): Promise<string> {
		let template = "";
		if (range === ReportRange.WEEKLY) {
			template = "../templates/email/weekly-report-email.ejs";
		} else if (range === ReportRange.MONTHLY) {
			template = "../templates/email/monthly-report-email.ejs";
		}

		const templatePath = path.resolve(
			__dirname,
			template
		);

		return await ejs.renderFile(templatePath, templateData);
	}

	private async getAIInsights(
		templateData: ReportTemplateData
	): Promise<AIReportSummary> {
		const aiMetricsData: ReportMetricsData = {
			reportPeriod: `${templateData.fromDate} to ${templateData.toDate}`,
			metrics: {
				impressions: {
					value: templateData.metrics.impressions.value,
					change: templateData.metrics.impressions.change
				},
				plays: {
					value: templateData.metrics.plays.value,
					change: templateData.metrics.plays.change
				},
				clicks: {
					value: templateData.metrics.clicks.value,
					change: templateData.metrics.clicks.change
				},
				emails: {
					value: templateData.metrics.emails.value,
					change: templateData.metrics.emails.change
				},
				leads: {
					value: templateData.metrics.leads.value,
					change: templateData.metrics.leads.change
				},
				calls: {
					value: templateData.metrics.calls.value,
					change: templateData.metrics.calls.change
				},
				engaged: {
					value: templateData.metrics.engaged.value,
					change: templateData.metrics.engaged.change
				},
				playtime: {
					value: templateData.metrics.playtime.value.toString(),
					change: templateData.metrics.playtime.change.toString()
				}
			},
			videos: templateData.videos.map(video => ({
				title: video.title,
				length: video.length.toString(),
				sessions: video.sessions,
				plays: video.plays,
				clicks: video.clicks,
				emails: video.emails,
				calls: video.calls,
				playtime: video.playtime,
				score: video.score
			}))
		};

		const reportsAIModel = new ReportsAIModel();
		return await reportsAIModel.getAIInsights(aiMetricsData);
	}
}

// Helper functions for metricsData calculations
function getEngagementRate(totalEngagedSessions: number, totalImpressions: number): number {
	return totalImpressions > 0 ? Math.round((totalEngagedSessions / totalImpressions) * 100) : 0;
}
function getPlayRate(plays: number, totalImpressions: number): number {
	return totalImpressions > 0 ? Math.round((plays / totalImpressions) * 100) : 0;
}
function getClickthroughRate(clicks: number, plays: number): number {
	return plays > 0 ? Math.round((clicks / plays) * 100) : 0;
}
function getPlaysPerSession(plays: number, engagedSessions: number): number {
	return engagedSessions > 0 ? +(plays / engagedSessions).toFixed(2) : 0;
}
function getAvgPlaytime(playtime: number, plays: number): number {
	return plays > 0 ? Math.round(playtime / plays) : 0;
}
