import { LocaleAPI } from "../../../interfaces/apiTypes";
import { ISignupEmailPayload } from "../../signup/signup.interfaces";
import { SignUpModel } from "../../signup/signup.model";
import { ReportsGenerateModel } from "./reports.model";
import { ReportRange } from "./reports.types";
import {
	JobsType,
	JobsStatus,
	Job
} from "../../job/job.model";
import { JobService } from "../../job/job.service";
import { ReportsAggregateModel } from "../aggregate/aggregate.model";
import mongoose from "mongoose";

describe("ReportsGenerateModel", () => {
	let accountId: string;

	beforeAll(async () => {
		const signupEmailPayload: ISignupEmailPayload = {
			email: "<EMAIL>",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "https://domain.tld"
		};

		const signupModel = new SignUpModel(null);
		const signupResult = await signupModel.createFromEmail({
			email: signupEmailPayload.email,
			callbackEndpoint: signupEmailPayload.callbackEndpoint,
			locale: signupEmailPayload.locale
		});

		expect(signupResult).toHaveProperty("authenticationDoc");
		expect(signupResult).toHaveProperty("authenticationDoc.accounts");
		expect(signupResult).toHaveProperty("authenticationDoc.accounts.0");

		const account = signupResult.authenticationDoc.accounts[0];
		accountId = account._id.toString();
	});

	describe("createJob", () => {
		it("should create a weekly reports job", async () => {
			const reportsModel = new ReportsGenerateModel(null);
			const payload = {
				range: ReportRange.WEEKLY,
				date: new Date(),
				accountId: accountId
			};

			const job = await reportsModel.createJob(payload);

			expect(job).toHaveProperty("_id");
			expect(job.type).toBe(JobsType.REPORTS_GENERATE);
			expect(job.status).toBe(JobsStatus.CREATED);
			expect(job.reportRange).toBe(ReportRange.WEEKLY);
			expect(job.accountId?.toString()).toBe(accountId);
			expect(job.reportDate).toBeInstanceOf(Date);
		});

		it("should create a monthly reports job", async () => {
			const reportsModel = new ReportsGenerateModel(null);
			const payload = {
				range: ReportRange.MONTHLY,
				date: new Date()
			};

			const job = await reportsModel.createJob(payload);

			expect(job).toHaveProperty("_id");
			expect(job.type).toBe(JobsType.REPORTS_GENERATE);
			expect(job.status).toBe(JobsStatus.CREATED);
			expect(job.reportRange).toBe(ReportRange.MONTHLY);
			expect(job.accountId).toBeUndefined();
			expect(job.reportDate).toBeInstanceOf(Date);
		});

		it("should set the date with UTC hours at 0", async () => {
			const reportsModel = new ReportsGenerateModel(null);
			const testDate = new Date("2024-07-15T14:30:45.123Z");
			const payload = {
				range: ReportRange.WEEKLY,
				date: testDate,
				accountId: accountId
			};

			const job = await reportsModel.createJob(payload);

			expect(job.reportDate?.getUTCHours()).toBe(0);
			expect(job.reportDate?.getUTCMinutes()).toBe(0);
			expect(job.reportDate?.getUTCSeconds()).toBe(0);
			expect(job.reportDate?.getUTCMilliseconds()).toBe(0);
		});
	});

	describe("processAccounts", () => {
		let mockJob: Job;

		beforeEach(async () => {
			// Create a mock job object instead of using JobService.createJob
			mockJob = {
				_id: new mongoose.Types.ObjectId(),
				type: JobsType.REPORTS_GENERATE,
				status: JobsStatus.CREATED,
				statusMessage: "test job",
				reportRange: ReportRange.WEEKLY,
				reportDate: new Date(),
				accountId: new mongoose.Types.ObjectId(accountId),
				createdAt: new Date(),
				updatedAt: new Date()
			} as unknown as Job;
		});

		it("should throw error when reportRange is missing", async () => {
			const reportsModel = new ReportsGenerateModel(null);
			const jobWithoutRange = { ...mockJob, reportRange: undefined };

			await expect(reportsModel.processAccounts(jobWithoutRange as Job))
				.rejects.toThrow("Reports job does not have an reportRange set.");
		});

		it("should process a specific account when accountId is provided", async () => {
			const reportsModel = new ReportsGenerateModel(null);

			// Mock the aggregate model to return empty data to avoid processing
			const originalReadAggregated = ReportsAggregateModel.prototype.readAggregatedRangeFromDB;
			ReportsAggregateModel.prototype.readAggregatedRangeFromDB = jest.fn().mockResolvedValue([]);

			// Mock JobService methods to avoid database updates
			const originalUpdateJobStatus = JobService.prototype.updateJobStatus;
			JobService.prototype.updateJobStatus = jest.fn().mockResolvedValue(mockJob);

			try {
				await reportsModel.processAccounts(mockJob);

				// Verify job status updates were called
				expect(JobService.prototype.updateJobStatus).toHaveBeenCalledWith(
					mockJob._id.toString(),
					JobsStatus.RUNNING,
					"Reports job is running..."
				);
				expect(JobService.prototype.updateJobStatus).toHaveBeenCalledWith(
					mockJob._id.toString(),
					JobsStatus.COMPLETE,
					"Reports completed successfully."
				);
			} finally {
				// Restore original methods
				ReportsAggregateModel.prototype.readAggregatedRangeFromDB = originalReadAggregated;
				JobService.prototype.updateJobStatus = originalUpdateJobStatus;
			}
		});

		it("should process all accounts when no accountId is provided", async () => {
			const reportsModel = new ReportsGenerateModel(null);
			const jobWithoutAccountId = { ...mockJob, accountId: undefined };

			// Mock the aggregate model to return empty data
			const originalReadAggregated = ReportsAggregateModel.prototype.readAggregatedRangeFromDB;
			ReportsAggregateModel.prototype.readAggregatedRangeFromDB = jest.fn().mockResolvedValue([]);

			// Mock JobService methods to avoid database updates
			const originalUpdateJobStatus = JobService.prototype.updateJobStatus;
			JobService.prototype.updateJobStatus = jest.fn().mockResolvedValue(mockJob);

			try {
				await reportsModel.processAccounts(jobWithoutAccountId as Job);

				// Verify job status updates were called
				expect(JobService.prototype.updateJobStatus).toHaveBeenCalledWith(
					mockJob._id.toString(),
					JobsStatus.RUNNING,
					"Reports job is running..."
				);
				expect(JobService.prototype.updateJobStatus).toHaveBeenCalledWith(
					mockJob._id.toString(),
					JobsStatus.COMPLETE,
					"Reports completed successfully."
				);
			} finally {
				// Restore original methods
				ReportsAggregateModel.prototype.readAggregatedRangeFromDB = originalReadAggregated;
				JobService.prototype.updateJobStatus = originalUpdateJobStatus;
			}
		});
	});

	describe("date range calculations", () => {
		it("should calculate weekly date range correctly", async () => {
			const reportsModel = new ReportsGenerateModel(null);

			// Use reflection to test private method
			const getReportDateRange = (reportsModel as any).getReportDateRange.bind(reportsModel);

			// Test with a specific date (Wednesday, July 10, 2024)
			const testDate = new Date("2024-07-10T12:00:00.000Z");
			const result = getReportDateRange(testDate, ReportRange.WEEKLY);

			expect(result.range).toBe(ReportRange.WEEKLY);

			// Should start on previous Monday (July 8, 2024 00:00:00)
			expect(result.startDate.getUTCDay()).toBe(1);
			expect(result.startDate.getUTCHours()).toBe(0);
			expect(result.startDate.getUTCMinutes()).toBe(0);
			expect(result.startDate.getUTCSeconds()).toBe(0);

			// Should end on Sunday (July 14, 2024 23:59:59)
			expect(result.endDate.getUTCDay()).toBe(0);
			expect(result.endDate.getUTCHours()).toBe(23);
			expect(result.endDate.getUTCMinutes()).toBe(59);
			expect(result.endDate.getUTCSeconds()).toBe(59);
		});

		it("should calculate monthly date range correctly", async () => {
			const reportsModel = new ReportsGenerateModel(null);

			// Use reflection to test private method
			const getReportDateRange = (reportsModel as any).getReportDateRange.bind(reportsModel);

			// Test with a date in July 2024
			const testDate = new Date("2024-07-15T12:00:00.000Z");
			const result = getReportDateRange(testDate, ReportRange.MONTHLY);

			expect(result.range).toBe(ReportRange.MONTHLY);

			// Should be for June 2024 (previous month)
			expect(result.startDate.getUTCFullYear()).toBe(2024);
			expect(result.startDate.getUTCMonth()).toBe(5);
			expect(result.startDate.getUTCDate()).toBe(1);
			expect(result.startDate.getUTCHours()).toBe(0);

			// Should end on last day of June
			expect(result.endDate.getUTCFullYear()).toBe(2024);
			expect(result.endDate.getUTCMonth()).toBe(5);
			expect(result.endDate.getUTCDate()).toBe(30);
			expect(result.endDate.getUTCHours()).toBe(23);
		});

		it("should throw error for unknown report range", async () => {
			const reportsModel = new ReportsGenerateModel(null);

			const getReportDateRange = (reportsModel as any).getReportDateRange.bind(reportsModel);
			const testDate = new Date("2024-07-15T12:00:00.000Z");

			expect(() => getReportDateRange(testDate, "INVALID_RANGE"))
				.toThrow("Unknown reportRange: INVALID_RANGE");
		});
	});

	describe("comparison date range calculations", () => {
		it("should calculate weekly comparison range correctly", async () => {
			const reportsModel = new ReportsGenerateModel(null);

			const getComparisonReportDateRange = (reportsModel as any).getComparisonReportDateRange.bind(reportsModel);

			const testDate = new Date("2024-07-15T12:00:00.000Z");
			const result = getComparisonReportDateRange(testDate, ReportRange.WEEKLY);

			expect(result.range).toBe(ReportRange.WEEKLY);
			// Should be one week earlier
			expect(result.startDate.getTime()).toBeLessThan(testDate.getTime());
		});

		it("should calculate monthly comparison range correctly", async () => {
			const reportsModel = new ReportsGenerateModel(null);

			const getComparisonReportDateRange = (reportsModel as any).getComparisonReportDateRange.bind(reportsModel);

			const testDate = new Date("2024-07-15T12:00:00.000Z");
			const result = getComparisonReportDateRange(testDate, ReportRange.MONTHLY);

			expect(result.range).toBe(ReportRange.MONTHLY);
			// Should be one month earlier
			expect(result.startDate.getUTCMonth()).toBe(4);
		});
	});
});
