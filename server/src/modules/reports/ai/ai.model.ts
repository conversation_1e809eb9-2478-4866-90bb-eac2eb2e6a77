import { VertexModel } from "../../vertex/vertex.model";
import MarkdownIt from "markdown-it";
import {
	ReportMetricsData,
	AIReportSummary
} from "./ai.interfaces";
import { generatePrompt } from "./ai.prompt";
import { VertexParameters } from "../../vertex/vertex.interfaces";
import { getSecrets } from "../../secrets/secrets.model";

export class ReportsAIModel {

	public async getAIInsights(metricsData: ReportMetricsData): Promise<AIReportSummary> {
		const secrets = await getSecrets();
		const prompt = generatePrompt(metricsData);
		const vertex = new VertexModel();
		const md = new MarkdownIt();

		try {
			const vertexParams: VertexParameters = {
				projectId: secrets.appConfig.gcpProjectId,
				region: secrets.appConfig.gcpRegion,
				model: secrets.appConfig.gcpVertexModel,
				temperature: 0.5,
				maxTokens: 7000,
				prompt: prompt
			};

			let response = await vertex.getAIResponse(vertexParams);

			if (response === "No AI response.") throw new Error("No Response from Vertex");

			if (response.startsWith("```json") || response.startsWith("```")) {
				response = response.replace(/```json\s*|\s*```/g, "").trim();
			}

			const summaries: AIReportSummary = JSON.parse(response);

			if ("emailSummary" in summaries && "insightSummary" in summaries) {
				for (const [key, value] of Object.entries(summaries)) {
					const currKey = key as keyof typeof summaries;
					summaries[currKey] = md.render(value);
				}
				return summaries;
			}

			throw new Error("AI failed to meet desired JSON structure");
		} catch (err: any) {
			throw new Error("Failed to receive insights from AI:\n" + err);
		}
	}
}
