import supertest from "supertest";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../../../express";
import {
	LocaleAPI,
	APIErrorName
} from "../../../interfaces/apiTypes";
import TestHelper from "../../../__tests__/mocks/testHelper";
import { ISignupEmailPayload } from "../../signup/signup.interfaces";
import { VideoModel } from "../../video/video.model";
import { JobService } from "../../job/job.service";
import { JobsStatus } from "../../job/job.model";
import { IAccount } from "../../account/account.interfaces";

describe("POST /api/videos/url", () => {
	let expressApp: express.Express;
	let testHelper: TestHelper;
	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	const fileURL = "https://domain.tld/src/__tests__/sample-files/1718848236_Sample_1.mp4";
	const signupEmailPayload: ISignupEmailPayload = {
		email: "<EMAIL>",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld"
	};

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);
		testHelper = new TestHelper(expressApp);
		const signupResponse = await testHelper.signupEmail(signupEmailPayload);
		accessToken = signupResponse.accessToken;
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});

	it("Missing payload data | 400 [E_INVALID_INPUT]", async () => {
		const res = await supertest(expressApp)
			.post("/api/videos/url")
			.set("x-api-version", "3")
			.set("x-account-token", accountToken)
			.set("Authorization", `Bearer ${accessToken}`)
			.send({});
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});


	it("Video limit reached | 403 E_REQUEST_FORBIDDEN ", async () => {
		jest.spyOn(VideoModel.prototype, "countDocuments")
			.mockResolvedValueOnce(account.subscription.maxInteractiveVideoLimit);
		const res = await supertest(expressApp)
			.post("/api/videos/url")
			.set("x-api-version", "3")
			.set("x-account-token", accountToken)
			.set("Authorization", `Bearer ${accessToken}`)
			.send({ fileUrl: fileURL });
		expect(res.statusCode).toBe(403);
		expect(res.body.error).toEqual(APIErrorName.E_REQUEST_FORBIDDEN);
	});

	it("Successfully started video encode job | 200 ", async () => {

		const res = await supertest(expressApp)
			.post("/api/videos/url")
			.set("x-api-version", "3")
			.set("x-account-token", accountToken)
			.set("Authorization", `Bearer ${accessToken}`)
			.send({ fileUrl: fileURL });

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("tempFilename");
		expect(res.body).toHaveProperty("status");
		expect(res.body).toHaveProperty("statusMessage");
		expect(res.body).toHaveProperty("progressPercent");
		expect(res.body).toHaveProperty("createdAt");
		expect(res.body).toHaveProperty("updatedAt");
		expect(res.body.nextStatusCheck).toBeUndefined();
		const jobService = new JobService(null);
		const job = await jobService.readOneByTempFilename(res.body.tempFilename);
		expect(job.tempFilename).toBe(res.body.tempFilename);
		expect(job.status).toBe(JobsStatus.CREATED);
		expect(job.progressPercent).toBe(0);
	});

});
