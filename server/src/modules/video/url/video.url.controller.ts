import express, {
	type Request,
	type Response
} from "express";
import { Controller } from "../../base/base.controller";
import { APIError } from "../../../utils/helpers/apiError";
import { APIErrorName } from "../../../interfaces/apiTypes";
import Jo<PERSON> from "joi";
import {
	VersionJoi,
	AccessTokenJoi
} from "../../base/base.joi";
import { BaseRequest } from "../../base/base.interfaces";
import { AccountModel } from "../../account/account.model";
import { VideoModel } from "../video.model";
import { UserModel } from "../../user/user.model";
import {
	cancelDBTransaction,
	completeDBTransaction,
	startDBTransaction
} from "../../../services/mongodb/transaction.service";
import { CallbackInfo } from "src/modules/job/job.model";


interface VideoURLPayload extends BaseRequest {
	apiVersion: number;
	fileUrl: string;
	callbackUrl?: string;
	callbackData?: string;
}

const VideoEventUploadedPayloadSchema = Joi.object<VideoURLPayload>({
	apiVersion: VersionJoi.required(),
	accessToken: AccessTokenJoi.required(),
	accountToken: Joi.string().required(),
	fileUrl: Joi.string().min(1).required(),
	callbackUrl: Joi.string(),
	callbackData: Joi.string()
});

export class VideoURLController extends Controller {
	constructor() {
		super();
		this.router.post("/", [express.json()], this.post.bind(this));
	}

	private async post(request: Request, response: Response): Promise<Response> {
		let validPayload: VideoURLPayload;
		try {
			validPayload = await VideoEventUploadedPayloadSchema.validateAsync({
				apiVersion: request.headers["x-api-version"],
				accountToken: request.headers["x-account-token"],
				accessToken: request.headers.authorization,
				fileUrl: request.body.fileUrl,
				callbackUrl: request.body.callbackUrl,
				callbackData: request.body.callbackData
			}
			);
		} catch (error: unknown) {
			const apiError = new APIError(APIErrorName.E_INVALID_INPUT, error);
			return apiError.setResponse(response);
		}

		try {
			await this.verifyAPIVersion(validPayload.apiVersion, 1);
			await startDBTransaction(response.locals.session);
			const accessToken = await this.verifyAccessToken(validPayload.accessToken);
			const accountToken = await this.verifyAccountToken(validPayload.accountToken);

			const accountModel: AccountModel = new AccountModel(response.locals.session);
			const account = await accountModel.readOneById(accountToken.account._id);

			const userModel = new UserModel(response.locals.session);
			const user = await userModel.readOneById(accessToken.userId);

			const videoModel: VideoModel = new VideoModel(response.locals.session);
			const fileUrl = validPayload.fileUrl;
			const callbackInfo: CallbackInfo = {
				callbackUrl: validPayload.callbackUrl,
				callbackData: validPayload.callbackData
			};
			const result = await videoModel.startVideoURLEncodeJob(account, user._id.toString(), fileUrl, callbackInfo);

			await completeDBTransaction(response.locals.session);
			return response.status(200).json(result);
		} catch (error: unknown) {
			await cancelDBTransaction(response.locals.session);
			return APIError.fromUnknownError(error).log().setResponse(response);
		}
	}


}
