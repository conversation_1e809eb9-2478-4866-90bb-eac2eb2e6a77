import mongoose from "mongoose";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../../../express";
import {
	LocaleAPI,
	APIErrorName
} from "../../../interfaces/apiTypes";
import TestHelper from "../../../__tests__/mocks/testHelper";
import { ISignupEmailPayload } from "../../signup/signup.interfaces";
import {
	JobsStatus,
	JobsType,
	Job,
	JobModel
} from "../../job/job.model";
import {
	VIDEO_ENCODE_STATUS_MAX_INTERVAL_MS,
	VIDEO_ENCODE_STATUS_MIN_INTERVAL_MS
} from "../job/video.job.model";
import { VideoEncodeModel } from "./video.encode.model";
import { JobService } from "../../job/job.service";
import path from "path";
import fsPromises from "fs/promises";
import os from "os";
import { randomBytes } from "crypto";
import { VideoProfile } from "../../videoProfile/videoProfile.interface";
import { VideoProfileModel } from "../../videoProfile/videoProfile.model";
import { AccountModel } from "../../account/account.model";
import { IAccount } from "../../account/account.interfaces";
import { APIError } from "../../../utils/helpers/apiError";

let expressApp: express.Express;
let testHelper: TestHelper;
let accountId: string;
let job: Job;

const signupEmailPayload: ISignupEmailPayload = {
	email: "<EMAIL>",
	locale: LocaleAPI.EN_US,
	callbackEndpoint: "https://domain.tld"
};

beforeAll(async () => {
	expressApp = createServer();
	initExpressRoutes(expressApp);
	testHelper = new TestHelper(expressApp);
	const { accessToken } = await testHelper.signupEmail(signupEmailPayload);
	const account = await testHelper.getAccount(accessToken);
	accountId = account._id.toString();
	const data = {
		tempFilename: "video.encode.model.filename",
		type: JobsType.ENCODE_VIDEO,
		status: JobsStatus.RUNNING,
		statusMessage: "Encoding video...0%",
		nextStatusCheck: Date.now() + VIDEO_ENCODE_STATUS_MAX_INTERVAL_MS,
		progressPercent: 0,
		accountId: new mongoose.Types.ObjectId(accountId),
		userId: new mongoose.Types.ObjectId(accessToken.userId)
	};
	job = await new JobModel(data).save({ session: null });
});

afterEach(() => {
	jest.restoreAllMocks();
});

describe("White Box Testing | Video Encode Model | handleFfmpegProgress() Method", () => {
	class VideoEncodeModelTest extends VideoEncodeModel {
		public async handleFfmpegProgress(progressPercent: number): Promise<void> {
			return super.handleFfmpegProgress(progressPercent);
		}
	}

	it("Should set nextStatusCheck to VIDEO_ENCODE_STATUS_MIN_INTERVAL_MS on first call (no deltas).", async () => {
		const videoEncodeModel = new VideoEncodeModelTest(job);
		const fixedNow = 100_000;
		jest.spyOn(Date, "now").mockImplementation(() => fixedNow);
		await videoEncodeModel.handleFfmpegProgress(30);

		const jobService = new JobService(null);
		const processedJob = await jobService.readOneByTempFilename(job.tempFilename);
		expect(processedJob).toBeTruthy();
		expect(processedJob.progressPercent).toBe(30);
		expect(processedJob.statusMessage).toBe("Encoding video...30%");
		expect(processedJob.nextStatusCheck).toBe(fixedNow + VIDEO_ENCODE_STATUS_MIN_INTERVAL_MS);
	});

	it("Should compute mean delta on second call and set nextStatusCheck accordingly.", async () => {
		const videoEncodeModel = new VideoEncodeModelTest(job);
		let fixedNow = 200_000;
		jest.spyOn(Date, "now").mockImplementation(() => fixedNow);
		await videoEncodeModel.handleFfmpegProgress(50);

		fixedNow = 202_000;
		jest.spyOn(Date, "now").mockImplementation(() => fixedNow);
		await videoEncodeModel.handleFfmpegProgress(60);
		const expectedMeanDelta = 2000;
		const expectedNextStatusCheck = fixedNow + expectedMeanDelta;

		const jobService = new JobService(null);
		const processedJob = await jobService.readOneByTempFilename(job.tempFilename);
		expect(processedJob).toBeTruthy();
		expect(processedJob?.progressPercent).toBe(60);
		expect(processedJob?.statusMessage).toBe("Encoding video...60%");
		expect(processedJob?.nextStatusCheck).toBe(expectedNextStatusCheck);
	});

	it("Should compute mean delta over multiple calls.", async () => {
		const videoEncodeModel = new VideoEncodeModelTest(job);

		let fixedNow = 300_000;
		jest.spyOn(Date, "now").mockImplementation(() => fixedNow);
		await videoEncodeModel.handleFfmpegProgress(10);

		fixedNow = 302_000;
		jest.spyOn(Date, "now").mockImplementation(() => fixedNow);
		await videoEncodeModel.handleFfmpegProgress(20);

		fixedNow = 305_000;
		jest.spyOn(Date, "now").mockImplementation(() => fixedNow);
		await videoEncodeModel.handleFfmpegProgress(30);
		/*
		  Recorded deltas: [2000, 3000]
		  meanDelta = (2000 + 3000) / 2 = 2500
		  nextStatusCheck = 305_000 + 2500 = 307_500
		*/
		const expectedMeanDelta = 2500;
		const expectedNextStatusCheck = fixedNow + expectedMeanDelta;

		const jobService = new JobService(null);
		const processedJob = await jobService.readOneByTempFilename(job.tempFilename);
		expect(processedJob).toBeTruthy();
		expect(processedJob?.progressPercent).toBe(30);
		expect(processedJob?.statusMessage).toBe("Encoding video...30%");
		expect(processedJob?.nextStatusCheck).toBe(expectedNextStatusCheck);
	});

	it("Should handle very rapid calls, ensuring a minimum clamp is applied.", async () => {
		const videoEncodeModel = new VideoEncodeModelTest(job);

		let fixedNow = 400_000;
		jest.spyOn(Date, "now").mockImplementation(() => fixedNow);
		await videoEncodeModel.handleFfmpegProgress(20);

		fixedNow = 400_200;
		jest.spyOn(Date, "now").mockImplementation(() => fixedNow);
		await videoEncodeModel.handleFfmpegProgress(40);
		const expectedNextStatusCheck = fixedNow + VIDEO_ENCODE_STATUS_MIN_INTERVAL_MS;
		const jobService = new JobService(null);
		const processedJob = await jobService.readOneByTempFilename(job.tempFilename);
		expect(processedJob).toBeTruthy();
		expect(processedJob?.progressPercent).toBe(40);
		expect(processedJob?.statusMessage).toBe("Encoding video...40%");
		expect(processedJob?.nextStatusCheck).toBe(expectedNextStatusCheck);
	});

});

describe("White Box Testing | Video Encode Model | waitForFileWrite() Method.", () => {
	class VideoEncodeModelTest extends VideoEncodeModel {
		public async waitForFileWrite(filePath: string): Promise<void> {
			return super.waitForFileWrite(filePath);
		}
	}
	const tmpDir = os.tmpdir();

	it("Should resolve immediately if file already exists and stable.", async () => {
		const tempFilePath = path.join(tmpDir, `test-file-write-${randomBytes(12).toString("hex")}.tmp`);
		const videoEncodeModel = new VideoEncodeModelTest(job);
		await fsPromises.writeFile(tempFilePath, "some initial data");
		await expect(videoEncodeModel.waitForFileWrite(tempFilePath)).resolves.not.toThrow();
		await fsPromises.unlink(tempFilePath);
	});

	it("Should eventually resolve if file doesn’t exist yet, but appears and stabilizes.", async () => {
		const tempFilePath = path.join(tmpDir, `test-file-write-${randomBytes(12).toString("hex")}.tmp`);
		const videoEncodeModel = new VideoEncodeModelTest(job);
		const waitPromise = videoEncodeModel.waitForFileWrite(tempFilePath);
		const POLL_INTERVAL_LESS_THAN_EXPECTED_MS = 300;
		setTimeout(async () => {
			await fsPromises.writeFile(tempFilePath, "Hello, world!");
		}, POLL_INTERVAL_LESS_THAN_EXPECTED_MS);
		await expect(waitPromise).resolves.not.toThrow();
		await fsPromises.unlink(tempFilePath);
	});

	it("Should reject if file never appears within MAX_WAIT_MS = 10_000 (using fake timers).", async () => {
		jest.useFakeTimers();
		const tempFilePath = path.join(tmpDir, `test-file-write-${randomBytes(12).toString("hex")}.tmp`);
		const videoEncodeModel = new VideoEncodeModelTest(job);
		const waitPromise = videoEncodeModel.waitForFileWrite(tempFilePath);
		jest.advanceTimersByTime(10_001);
		await expect(waitPromise).rejects.toThrow(/did not appear within 10000ms/);
		jest.useRealTimers();
	});

	it("Should detect changes and only resolve after size stops changing.", async () => {
		const tempFilePath = path.join(tmpDir, `test-file-write-${randomBytes(12).toString("hex")}.tmp`);
		await fsPromises.writeFile(tempFilePath, "");

		const videoEncodeModel = new VideoEncodeModelTest(job);
		const waitPromise = videoEncodeModel.waitForFileWrite(tempFilePath);

		setTimeout(async () => {
			await fsPromises.appendFile(tempFilePath, "chunk1");
		}, 200);
		setTimeout(async () => {
			await fsPromises.appendFile(tempFilePath, "chunk2");
		}, 700);
		// After 1200ms, no more writes => should stabilize soon
		// The polling logic needs 2 consecutive checks of stable size
		await expect(waitPromise).resolves.not.toThrow();
		// By the time we get here, the file must have stabilized.
		const finalSize = (await fsPromises.stat(tempFilePath)).size;
		expect(finalSize).toBe("chunk1".length + "chunk2".length);
		await fsPromises.unlink(tempFilePath);
	});

	it("Should reject if file is created but never stabilizes.", async () => {
		jest.useFakeTimers();
		const tempFilePath = path.join(tmpDir, `test-file-write-${randomBytes(12).toString("hex")}.tmp`);
		const videoEncodeModel = new VideoEncodeModelTest(job);
		await fsPromises.writeFile(tempFilePath, "");
		const waitPromise = videoEncodeModel.waitForFileWrite(tempFilePath);
		let i = 0;
		const interval = setInterval(() => {
			i++;
			fsPromises.appendFile(tempFilePath, `chunk${i}`);
		}, 300);
		jest.advanceTimersByTime(10_500);
		clearInterval(interval);
		await expect(waitPromise).rejects.toThrow(/Timeout waiting for file/);
		await fsPromises.unlink(tempFilePath);
		jest.useRealTimers();
	});
});

describe("White Box Testing | Video Encode Model | getVideoProfile() Method.", () => {
	class VideoEncodeModelTest extends VideoEncodeModel {
		public async getVideoProfile(accountId: string): Promise<VideoProfile> {
			return super.getVideoProfile(accountId);
		}
	}

	it("Should fetch the video profile that is referenced in the account", async () => {
		const account = await new AccountModel(null).readOneById(accountId);
		const videoEncodeModel = new VideoEncodeModelTest(job);
		const videoProfile = await videoEncodeModel.getVideoProfile(accountId);
		expect(videoProfile).toBeTruthy();
		expect(videoProfile._id.toString()).toBe(account.videoProfile.toString());
	});

	it("Should fetch the default video profile and update the account that is missing videoProfile property.",
		async () => {
			const accountModel = new AccountModel(null);
			let account:IAccount;
			account = await accountModel.readOneById(accountId);
			expect(account.videoProfile).toBeTruthy();
			account = await accountModel.updateOneById(accountId, { $unset: { videoProfile: "" } });
			expect(account.videoProfile).toBeFalsy();

			const videoEncodeModel = new VideoEncodeModelTest(job);
			const videoProfile = await videoEncodeModel.getVideoProfile(accountId);
			expect(videoProfile).toBeTruthy();
			account = await accountModel.readOneById(accountId);
			expect(account.videoProfile).toBeTruthy();
			expect(videoProfile._id.toString()).toBe(account.videoProfile.toString());
		});

	it("Should throw an error if no video profile is found and no default is available.",
		async () => {
			jest.spyOn(VideoProfileModel.prototype, "readOneById").mockRejectedValueOnce(
				new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "VideoProfile not found.")
			);
			jest.spyOn(VideoProfileModel.prototype, "readDefault").mockRejectedValueOnce(
				new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Default VideoProfile not found.")
			);
			const videoEncodeModel = new VideoEncodeModelTest(job);
			await expect(videoEncodeModel.getVideoProfile(accountId)).rejects
				.toThrow(/Default VideoProfile not found./);
		});
});
