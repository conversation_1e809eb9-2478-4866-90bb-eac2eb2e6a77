import supertest from "supertest";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../../../express";
import {
	LocaleAPI,
	APIErrorName
} from "../../../interfaces/apiTypes";
import TestHelper from "../../../__tests__/mocks/testHelper";
import { ISignupEmailPayload } from "../../../modules/signup/signup.interfaces";
import { IAccount } from "../../../modules/account/account.interfaces";
import {
	JobsStatus,
	JobsType
} from "../../job/job.model";
import { JobService } from "../../job/job.service";
import {
	VideoJobModel,
	VIDEO_ENCODE_STATUS_MAX_INTERVAL_MS
} from "./video.job.model";

describe("GET /api/videos/job/status/:tempFilename", () => {
	let expressApp: express.Express;
	let testHelper: TestHelper;
	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	const filename = "user-video.mp4";
	let tempFilename: string;
	const signupEmailPayload: ISignupEmailPayload = {
		email: "<EMAIL>",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld"
	};

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);
		testHelper = new TestHelper(expressApp);
		const signupResponse = await testHelper.signupEmail(signupEmailPayload);
		accessToken = signupResponse.accessToken;
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);

		const signedURLResult = await supertest(expressApp)
			.post("/api/videos/signed-url")
			.set("x-api-version", "3")
			.set("x-account-token", accountToken)
			.set("Authorization", `Bearer ${accessToken}`)
			.send({ fileName: filename });
		expect(signedURLResult.statusCode).toBe(200);
		expect(signedURLResult.body.tempFilename).toBeTruthy();
		expect(signedURLResult.body.signedURL).toBeTruthy();
		tempFilename = signedURLResult.body.tempFilename;

		const uploadedResult = await supertest(expressApp)
			.post("/api/videos/events/uploaded")
			.set("x-api-version", "3")
			.set("x-account-token", accountToken)
			.set("Authorization", `Bearer ${accessToken}`)
			.send({ tempFilename: tempFilename });

		expect(uploadedResult.statusCode).toBe(200);
		expect(uploadedResult.body.tempFilename).toBe(tempFilename);
		expect(uploadedResult.body.status).toBe(JobsStatus.CREATED);
		expect(uploadedResult.body.progressPercent).toBe(0);
		expect(uploadedResult.body.createdAt).toBeTruthy();
		expect(uploadedResult.body.updatedAt).toBeTruthy();
		expect(uploadedResult.body.nextStatusCheck).toBeUndefined();

	});

	afterEach(() => {
		jest.restoreAllMocks();
	});
	it("successfully returns video job status | 200", async () => {
		const res = await supertest(expressApp)
			.get(`/api/videos/job/status/${tempFilename}`)
			.set("x-api-version", "3")
			.set("x-account-token", accountToken)
			.set("Authorization", `Bearer ${accessToken}`)
			.send();
		expect(res.statusCode).toBe(200);
		expect(res.body.tempFilename).toBe(tempFilename);
		expect(res.body.status).toBe(JobsStatus.CREATED);
		expect(res.body.progressPercent).toBe(0);
		expect(res.body).toHaveProperty("nextStatusCheck");
		expect(res.body).toHaveProperty("createdAt");
		expect(res.body).toHaveProperty("updatedAt");
	});

	it("Missing x-api-version in the payload data for video job status  | 400 [E_INVALID_INPUT]", async () => {
		const res = await supertest(expressApp)
			.get(`/api/videos/job/status/${tempFilename}`)
			.set("x-account-token", accountToken)
			.set("Authorization", `Bearer ${accessToken}`)
			.send();
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("successfully returns video job | 200", async () => {
		const res = await supertest(expressApp)
			.get("/api/videos/job")
			.set("x-api-version", "3")
			.set("x-account-token", accountToken)
			.set("Authorization", `Bearer ${accessToken}`)
			.send();
		expect(res.statusCode).toBe(200);
		expect(res.body.jobs.length).toBe(1);
		expect(res.body.jobs[0].tempFilename).toBe(tempFilename);
		expect(res.body.jobs[0].type).toBe(JobsType.ENCODE_VIDEO);
		expect(res.body.jobs[0].status).toBe(JobsStatus.CREATED);
		expect(res.body.jobs[0].progressPercent).toBe(0);
		expect(res.body.jobs[0].createdAt).toBeTruthy();
		expect(res.body.jobs[0].updatedAt).toBeTruthy();
	});

	it("Missing x-account-token in the payload data for video job  | 400 [E_INVALID_INPUT]", async () => {
		const res = await supertest(expressApp)
			.get("/api/videos/job")
			.set("x-api-version", "3")
			.set("Authorization", `Bearer ${accessToken}`)
			.send();
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("successfully returns updated video job status (job's nextStatusCheck is valid)", async () => {
		const jobService = new JobService(null);
		const processedJob = await jobService.readOneByTempFilename(tempFilename);

		const fixedNow = 100_000;
		jest.spyOn(Date, "now").mockImplementation(() => fixedNow);
		const newNextStatusCheck = fixedNow + VIDEO_ENCODE_STATUS_MAX_INTERVAL_MS;

		await VideoJobModel.updateVideoEncodeStatus(processedJob._id.toString(), {
			status: JobsStatus.RUNNING,
			progressPercent: 10,
			statusMessage: "Encoding video...10%",
			nextStatusCheck: newNextStatusCheck
		});

		const res = await supertest(expressApp)
			.get(`/api/videos/job/status/${tempFilename}`)
			.set("x-api-version", "3")
			.set("x-account-token", accountToken)
			.set("Authorization", `Bearer ${accessToken}`)
			.send();

		expect(res.statusCode).toBe(200);
		expect(res.body.tempFilename).toBe(tempFilename);
		expect(res.body.status).toBe(JobsStatus.RUNNING);
		expect(res.body.progressPercent).toBe(10);
		expect(res.body.statusMessage).toBe("Encoding video...10%");
		expect(res.body.createdAt).toBeTruthy();
		expect(res.body.updatedAt).toBeTruthy();
		expect(res.body.nextStatusCheck).toBe(newNextStatusCheck);
	});

	it("should override nextStatusCheck if job's is behind the minimum threshold", async () => {
		const jobService = new JobService(null);
		const processedJob = await jobService.readOneByTempFilename(tempFilename);

		const fixedNow = 200_000;
		jest.spyOn(Date, "now").mockImplementation(() => fixedNow);

		const tooSoonNextStatusCheck = fixedNow - 10_000;
		await VideoJobModel.updateVideoEncodeStatus(processedJob._id.toString(), {
			status: JobsStatus.RUNNING,
			progressPercent: 20,
			statusMessage: "Encoding video...20%",
			nextStatusCheck: tooSoonNextStatusCheck
		});

		const res = await supertest(expressApp)
			.get(`/api/videos/job/status/${tempFilename}`)
			.set("x-api-version", "3")
			.set("x-account-token", accountToken)
			.set("Authorization", `Bearer ${accessToken}`)
			.send();

		expect(res.statusCode).toBe(200);
		expect(res.body.tempFilename).toBe(tempFilename);
		expect(res.body.status).toBe(JobsStatus.RUNNING);
		expect(res.body.progressPercent).toBe(20);
		expect(res.body.statusMessage).toBe("Encoding video...20%");
		const maximumFutureNextStatusCheck = fixedNow +	VIDEO_ENCODE_STATUS_MAX_INTERVAL_MS;
		expect(res.body.nextStatusCheck).toBe(maximumFutureNextStatusCheck);
	});
});
