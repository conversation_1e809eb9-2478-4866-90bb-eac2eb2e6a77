import supertest from "supertest";
import express from "express";
import {
	createServer,
	initExpressRoutes
} from "../../../express";
import {
	LocaleAPI,
	APIErrorName
} from "../../../interfaces/apiTypes";
import TestHelper from "../../../__tests__/mocks/testHelper";
import { ISignupEmailPayload } from "../../signup/signup.interfaces";
import { MAX_FILE_SIZE_BYTES } from "../../video/video.model";
import { JobService } from "../../job/job.service";
import { JobsStatus } from "../../job/job.model";

describe("POST /api/videos/events/uploaded", () => {
	let expressApp: express.Express;
	let testHelper: TestHelper;
	let accessToken: string;
	let accountToken: string;
	const filename = "67284d2dd362803be52547fa-user-video.mp4";
	const signupEmailPayload: ISignupEmailPayload = {
		email: "<EMAIL>",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld"
	};

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);
		testHelper = new TestHelper(expressApp);
		const signupResponse = await testHelper.signupEmail(signupEmailPayload);
		accessToken = signupResponse.accessToken;
		const account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
		await testHelper.createVideo(account._id.toString());
	});

	it("Missing payload data | 400 [E_INVALID_INPUT]", async () => {
		const res = await supertest(expressApp)
			.post("/api/videos/events/uploaded")
			.set("x-api-version", "3")
			.set("x-account-token", accountToken)
			.set("Authorization", `Bearer ${accessToken}`)
			.send({});
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("File size exceeds limit | 413 E_FILE_TOO_LARGE", async () => {
		const { Storage } = jest.requireMock("@google-cloud/storage");
		const storageInstance = new Storage();
		const bucket = storageInstance.bucket("test-bucket");
		const file = bucket.file(filename);
		const getMetadataSpy = jest.spyOn(file, "getMetadata");
		getMetadataSpy.mockImplementationOnce(async () => [{ size: (MAX_FILE_SIZE_BYTES + 1).toString() }]);

		const res = await supertest(expressApp)
			.post("/api/videos/events/uploaded")
			.set("x-api-version", "3")
			.set("x-account-token", accountToken)
			.set("Authorization", `Bearer ${accessToken}`)
			.send({ tempFilename: filename });

		expect(res.statusCode).toBe(413);
		expect(res.body.error).toEqual(APIErrorName.E_FILE_TOO_LARGE);
	});

	it("Successfully started video encode job | 200 ", async () => {

		const res = await supertest(expressApp)
			.post("/api/videos/events/uploaded")
			.set("x-api-version", "3")
			.set("x-account-token", accountToken)
			.set("Authorization", `Bearer ${accessToken}`)
			.send({ tempFilename: filename });
		expect(res.statusCode).toBe(200);
		expect(res.body.tempFilename).toBe(filename);
		expect(res.body).toHaveProperty("tempFilename");
		expect(res.body).toHaveProperty("status");
		expect(res.body).toHaveProperty("statusMessage");
		expect(res.body).toHaveProperty("progressPercent");
		expect(res.body).toHaveProperty("createdAt");
		expect(res.body).toHaveProperty("updatedAt");
		expect(res.body.nextStatusCheck).toBeUndefined();
		const jobService = new JobService(null);
		const job = await jobService.readOneByTempFilename(filename);
		expect(job.tempFilename).toBe(filename);
		expect(job.status).toBe(JobsStatus.CREATED);
		expect(job.progressPercent).toBe(0);
	});

});
