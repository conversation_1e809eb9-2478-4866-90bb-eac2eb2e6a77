import mongoose, {
	ClientSession,
	FilterQuery
} from "mongoose";
import { APIErrorName } from "../../interfaces/apiTypes";
import { APIError } from "../../utils/helpers/apiError";
import {
	UserCreateOneInput,
	IUser,
	UserUpdateOneInput
} from "./user.interfaces";
import { UserDBModel } from "./userDB.model";

export class UserModel {
	private session: ClientSession | null;

	constructor (session: ClientSession | null) {
		this.session = session;
	}

	public static async createIndexes (): Promise<void> {
		await UserDBModel.collection.createIndexes([
			{ key: { email: 1 }, name: "email_index", unique: true }
		]);
	}

	public static async getIndexes (): Promise<unknown> {
		return await UserDBModel.collection.indexes();
	}

	async readOneByEmail (email: string): Promise<IUser> {
		const filter: FilterQuery<IUser> = {
			email
		};

		const document: IUser | null = await UserDBModel.findOne(filter).session(
			this.session
		);

		if (!document) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "User not found");
		}

		return document;
	}

	async exists(email: string): Promise<boolean> {
		const filter: FilterQuery<IUser> = {
			email
		};

		const document = await UserDBModel.findOne(filter).session(
			this.session
		);

		let ret = true;
		if (!document) {
			ret = false;
		}

		return ret;
	}

	async readOneById (_id: string): Promise<IUser> {
		const filter: FilterQuery<IUser> = {
			_id: new mongoose.Types.ObjectId(_id)
		};

		const userDocument: IUser | null = await UserDBModel.findOne(filter).session(
			this.session
		);

		if (!userDocument) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "User not found");
		}

		return userDocument;
	}

	async readManyByAccountId (accountId: string): Promise<IUser[]> {
		const query = [
			{
				$lookup: {
					from: "authentications",
					localField: "_id",
					foreignField: "userId",
					as: "authentication"
				}
			},
			{
				$match: {
					"authentication.accounts": {
						$elemMatch: {
							_id: new mongoose.Types.ObjectId(accountId)
						}
					}
				}
			},
			{
				$unset: ["authentication"]
			}
		];

		const documents: IUser[] = await UserDBModel.aggregate(query).session(this.session);
		return documents;
	}

	async createOne (createInput: UserCreateOneInput): Promise<IUser> {
		try {
			createInput.email = createInput.email.toLowerCase().trim();

			const options: mongoose.SaveOptions = {
				session: this.session
			};

			const newDocument = await new UserDBModel(createInput).save(options);

			return newDocument;
		} catch (error: unknown) {
			if (error instanceof Error && error.name === "MongoServerError" && (error as any).code === 11000) {
				throw new APIError(APIErrorName.E_SIGN_UP_EXISTING_EMAIL, "Email already exists");
			}

			throw error;
		}
	}

	async updateOneById (updateInput: UserUpdateOneInput): Promise<IUser> {
		try {
			const filter: FilterQuery<IUser> = {
				_id: new mongoose.Types.ObjectId(updateInput._id)
			};
			const options: mongoose.QueryOptions = {
				session: this.session,
				new: true
			};

			const setFields: Record<string, any> = {};

			if (updateInput.email) {
				setFields.email = updateInput.email.toLowerCase().trim();
			}

			if (updateInput.firstName) {
				setFields.firstName = updateInput.firstName;
			}

			if (updateInput.lastName) {
				setFields.lastName = updateInput.lastName;
			}

			if (updateInput.team) {
				setFields.team = updateInput.team;
			}

			if (updateInput.isPasswordSet !== undefined) {
				setFields.isPasswordSet = updateInput.isPasswordSet;
			}

			if (updateInput.postSignupCompleted !== undefined) {
				setFields.postSignupCompleted = updateInput.postSignupCompleted;
			}

			if (updateInput.maxCompanies !== undefined) {
				setFields.maxCompanies = updateInput.maxCompanies;
			}

			const update = Object.keys(setFields).length > 0 ? { $set: setFields } : {};

			const document = await UserDBModel.findOneAndUpdate(filter, update, options);

			if (!document) {
				throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "User not found");
			}

			return document;
		} catch (error: unknown) {
			if (error instanceof Error && error.name === "MongoServerError" && (error as any).code === 11000) {
				throw new APIError(APIErrorName.E_UPDATE_EMAIL_CONFLICT, "Email is already in use by another user");
			}

			throw error;
		}
	}

	public async deleteOneById (userId: string): Promise<void> {
		const filter: FilterQuery<IUser> = {
			_id: new mongoose.Types.ObjectId(userId)
		};

		await UserDBModel.deleteOne(filter).session(this.session);
	}
}
