import {
	Document,
	ObjectId
} from "mongoose";
import { UserTeam } from "./user.enums";

export interface IUser extends Document {
	_id: ObjectId;
	email: string;
	firstName: string;
	lastName: string;
	isOwner: boolean;
	team: UserTeam;
	maxCompanies?: number;
	postSignupCompleted: boolean;
	isPasswordSet: boolean;
}

export interface UserCreateOneInput {
	email: string;
	firstName: string;
	lastName: string;
	postSignupCompleted: boolean;
	isPasswordSet: boolean;
	maxCompanies?: number;
}

export interface UserUpdateOneInput {
	_id: string;
	email?: string;
	firstName?: string;
	lastName?: string;
	team?: UserTeam;
	isPasswordSet?: boolean;
	postSignupCompleted?: boolean;
	maxCompanies?: number;
}

export interface IPatchUserPayload {
	firstName: string;
	lastName: string;
	team: UserTeam;
}
