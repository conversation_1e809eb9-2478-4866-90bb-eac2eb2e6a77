import mongoose, { Schema } from "mongoose";
import { IUser } from "./user.interfaces";

const UserSchema: Schema = new Schema({
	firstName: { type: String, required: false, default: "" },
	lastName: { type: String, required: false, default: "" },
	email: {
		type: String,
		required: true,
		unique: true
	},
	team: { type: String, required: false },
	maxCompanies: { type: Number, required: false, default: 1 },
	postSignupCompleted: { type: Boolean, required: true },
	isPasswordSet: { type: Boolean, required: false },
	createdAt: {
		type: Number,
		default: () => Date.now()
	},
	updatedAt: {
		type: Number,
		default: () => Date.now()
	}
},
{ timestamps: true }
);

export const UserDBModel = mongoose.model<IUser>("Users", UserSchema);
