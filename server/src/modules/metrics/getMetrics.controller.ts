import {
	Request,
	Response
} from "express";
import { APIError } from "../../utils/helpers/apiError";
import mongoose from "mongoose";
import { MetricImpressionModel } from "../metricImpression/metricImpression.model";
import { IMetricImpression } from "../metricImpression/metricImpression.interfaces";
import { IGetMetricPayload } from "./metrics.interface";
import { APIErrorName } from "../../interfaces/apiTypes";
import { countMetricVideoClicks } from "../../services/mongodb/metricVideoClicks.service";
import { MetricVideoPlayTimeModel } from "../metricVideoPlayTime/metricVideoPlayTime.model";

export const getMetricsController = async (req: Request, res: Response): Promise<Response> => {
	try {
		const payload = req.query as unknown as IGetMetricPayload;
		const accountId = req.accountToken?.account._id;

		if (!accountId) {
			throw new APIError(
				APIErrorName.E_MISSING_AUTHORIZATION,
				"Missing accountId in account token"
			);
		}

		const startDate = new Date(payload.startDate);
		const endDate = new Date(payload.endDate);

		const { currentMetrics, previousMetrics } = await fetchCurrentAndPreviousMetrics(accountId, startDate, endDate);

		return res.status(200).send({
			previousMetrics,
			currentMetrics
		});
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

interface IMetrics {
	totalImpressions: number;
	totalPlays: number;
	totalClicks: number;
	totalPlayTimeSeconds: number;
}

const fetchCurrentAndPreviousMetrics = async (
	accountId: string,
	startDate: Date,
	endDate: Date
): Promise<{ currentMetrics: IMetrics; previousMetrics: IMetrics }> => {
	const durationMs = endDate.getTime() - startDate.getTime();
	const prevEndDate = new Date(startDate.getTime() - 1);
	const prevStartDate = new Date(prevEndDate.getTime() - durationMs);

	const [currentMetrics, previousMetrics] = await Promise.all([
		fetchMetrics(accountId, startDate, endDate),
		fetchMetrics(accountId, prevStartDate, prevEndDate)
	]);

	return { currentMetrics, previousMetrics };
};

const getTotalFilteredImpressionsCount = async (accountId: string, startDate: Date, endDate: Date): Promise<number> => {
	const matchQuery: mongoose.FilterQuery<IMetricImpression> = {
		accountId: new mongoose.Types.ObjectId(accountId),
		createdAt: {
			$gte: startDate,
			$lte: endDate
		}
	};

	const metricImpressionModel = new MetricImpressionModel(null);
	const totalImpressions = await metricImpressionModel.countDocuments(matchQuery);
	return totalImpressions;
};

const fetchMetrics = async (accountId: string, startDate: Date, endDate: Date): Promise<IMetrics> => {
	const metricVideoPlayTimeModel = new MetricVideoPlayTimeModel(null);
	const matchQuery = {
		accountId: new mongoose.Types.ObjectId(accountId),
		createdAt: {
			$gte: startDate,
			$lte: endDate
		}
	};

	const totalImpressions = await getTotalFilteredImpressionsCount(accountId, startDate, endDate);
	const totalPlays =
		await metricVideoPlayTimeModel.countTotalPlaysByDateRange(accountId, startDate, endDate);
	const totalClicks = await countMetricVideoClicks(matchQuery, null);
	const totalPlayTimeSeconds =
		await metricVideoPlayTimeModel.sumPlayTimeSecondsByDateRange(accountId, startDate, endDate);

	return {
		totalImpressions,
		totalPlays,
		totalClicks,
		totalPlayTimeSeconds
	};
};
