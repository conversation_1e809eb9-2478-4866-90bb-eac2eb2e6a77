import mongoose, {
	<PERSON><PERSON>Session,
	FilterQuery
} from "mongoose";
import { IAccount } from "../account/account.interfaces";
import { InteractiveCollectionDBModel } from "./interactiveCollectionDB.model";
import { APIError } from "../../utils/helpers/apiError";
import { ModelResponse } from "../modelResponse/modelResponse.model";
import { MetricInteractiveCollectionModel } from "../metricInteractiveCollection/metricInteractiveCollection.model";
import { InteractiveVideoModel } from "../interactiveVideo/interactiveVideo.model";
import { CDN_DIR } from "../../utils/helpers/gp.helper";
import { AccountModel } from "../account/account.model";
import { SubscriptionDefaults } from "../subscription/subscription.interfaces";
import { APIErrorName } from "../../interfaces/apiTypes";
import { IShoppableVideo } from "../interactiveVideo/interactiveVideo.interface";
import { IShoppableCollection } from "./interactiveCollection.interface";
import { getSecrets } from "../secrets/secrets.model";
import { GCPStorageProvider } from "../storage/providers/gcp.storage";
import { StorageCacheControl } from "../storage/storage.model";
import { StorageService } from "../storage/storage.service";

export interface InteractiveCollectionUpdateOne {
	title?: string;
	shoppableVideos?: mongoose.Types.ObjectId[];
	buttonBackgroundColor?: string;
	buttonBackgroundBlur?: boolean;
	iconTextColor?: string;
	displayFont?: string;
	carouselBorderRadius?: number;
	carouselIsCentered?: boolean;
	carouselMargin?: number;
	carouselGap?: number;
	widgetBorderRadius?: number;
	widgetPosition?: "left" | "right";
	inlineBorderRadius?: number;
}

export interface InteractiveCollectionCreateOne {
	title: string;
	shoppableVideos: string[];
	buttonBackgroundColor?: string;
	buttonBackgroundBlur?: boolean;
	iconTextColor?: string;
	displayFont?: string;
	carouselBorderRadius?: number;
	carouselIsCentered?: boolean;
	carouselMargin?: number;
	carouselGap?: number;
	widgetBorderRadius?: number;
	widgetPosition?: "left" | "right";
	inlineBorderRadius?: number;
}

export class InteractiveCollectionModel {
	private session: ClientSession | null;

	constructor (session: ClientSession | null) {
		this.session = session;
	}

	async countDocuments (filter: FilterQuery<IShoppableCollection>): Promise<number> {
		const count: number = await InteractiveCollectionDBModel.countDocuments(filter).session(
			this.session
		);

		return count;
	}

	async isInteractiveCollectionLimitReached (account: IAccount): Promise<boolean> {
		const filter: FilterQuery<IShoppableCollection> = {
			accountId: account._id
		};

		const documentCount = await this.countDocuments(filter);
		const limit = account?.subscription?.maxInteractiveCollectionLimit ??
			SubscriptionDefaults.maxInteractiveCollectionLimit;

		return documentCount >= limit;
	}

	async createOne (
		createData: InteractiveCollectionCreateOne,
		account: IAccount
	): Promise<ModelResponse<IShoppableCollection>> {
		try {
			if (await this.isInteractiveCollectionLimitReached(account)) {
				return new ModelResponse<IShoppableCollection>(
					null,
					new APIError(APIErrorName.E_REQUEST_FORBIDDEN, "Interactive collection limit reached")
				);
			}

			const options: mongoose.SaveOptions = {
				session: this.session
			};

			const newDocument = await new InteractiveCollectionDBModel({
				...createData,
				accountId: account._id
			}).save(options);

			const metricInteractiveCollectionModel: MetricInteractiveCollectionModel =
			new MetricInteractiveCollectionModel(this.session);

			const metricInteractiveCollectionDocument = await metricInteractiveCollectionModel.createOne(
				{
					accountId: account._id,
					interactiveCollectionId: newDocument._id
				}
			);
			metricInteractiveCollectionDocument.throwIfError();
			await this.updateDataCache(newDocument);

			return new ModelResponse<IShoppableCollection>(newDocument, null);
		} catch (error: unknown) {
			return new ModelResponse<IShoppableCollection>(
				null,
				new APIError(APIErrorName.E_SERVICE_FAILED, (error as Error).message)
			);
		}
	}

	async readOneById (_id: string): Promise<IShoppableCollection> {
		try {
			const filter: FilterQuery<IShoppableCollection> = {
				_id: new mongoose.Types.ObjectId(_id)
			};

			const document = await InteractiveCollectionDBModel.findOne(filter).session(this.session);

			if (!document) {
				throw new APIError(APIErrorName.E_COLLECTION_NOT_FOUND, "Interactive Collection not found");
			}

			return document;
		} catch (error: unknown) {
			if (error instanceof APIError) {
				throw error;
			}
			throw new APIError(APIErrorName.E_SERVICE_FAILED, (error as Error).message);
		}
	}

	async updateOneById (_id: string, updateData: InteractiveCollectionUpdateOne):
	Promise<IShoppableCollection> {
		try {
			const filter: FilterQuery<IShoppableCollection> = {
				_id: new mongoose.Types.ObjectId(_id)
			};
			const options: mongoose.QueryOptions = {
				session: this.session,
				new: true
			};

			const document = await InteractiveCollectionDBModel.findOneAndUpdate(filter, updateData, options);
			if (!document) {
				throw new APIError(APIErrorName.E_COLLECTION_NOT_FOUND, "Interactive collection not found");
			}
			await this.updateDataCache(document);
			return document;
		} catch (error: unknown) {
			if (error instanceof APIError) {
				throw error;
			}
			throw new APIError(APIErrorName.E_SERVICE_FAILED, (error as Error).message);
		}
	}



	async updateOne (interactiveCollection: IShoppableCollection): Promise<IShoppableCollection> {
		try {

			const filter: FilterQuery<IShoppableCollection> = {
				_id: interactiveCollection._id
			};
			const options: mongoose.QueryOptions = {
				session: this.session,
				new: true
			};

			const document = await InteractiveCollectionDBModel
				.findOneAndUpdate(filter, interactiveCollection, options);
			if (!document) {
				throw new APIError(APIErrorName.E_COLLECTION_NOT_FOUND, "Interactive collection not found");
			}

			await this.updateDataCache(document);

			return document;
		} catch (error: unknown) {
			if (error instanceof APIError) {
				throw error;
			}
			throw new APIError(APIErrorName.E_SERVICE_FAILED, (error as Error).message);
		}
	}

	async readManyByVideo(video: IShoppableVideo): Promise<IShoppableCollection[]> {
		const filter: FilterQuery<IShoppableCollection> = {
			accountId: video.accountId,
			shoppableVideos: { $in: [video._id] }
		};

		const documents = await InteractiveCollectionDBModel.find(filter).session(this.session);
		return documents;
	}

	async updateDataCache (interactiveCollection: IShoppableCollection): Promise<void> {
		const sortedVideos = await this.getSortedInteractiveVideos(interactiveCollection);

		const interactiveVideoModel = new InteractiveVideoModel(this.session);
		const redactedSortedVideos = await interactiveVideoModel.constructPublicData(sortedVideos);

		const fileContent = JSON.stringify({
			accountId: interactiveCollection.accountId,
			interactiveVideos: redactedSortedVideos,
			buttonBackgroundColor: interactiveCollection?.buttonBackgroundColor,
			buttonBackgroundBlur: interactiveCollection?.buttonBackgroundBlur,
			iconTextColor: interactiveCollection?.iconTextColor,
			displayFont: interactiveCollection?.displayFont,
			carouselBorderRadius: interactiveCollection?.carouselBorderRadius,
			carouselIsCentered: interactiveCollection?.carouselIsCentered,
			carouselMargin: interactiveCollection?.carouselMargin,
			carouselGap: interactiveCollection?.carouselGap,
			widgetBorderRadius: interactiveCollection?.widgetBorderRadius,
			widgetPosition: interactiveCollection?.widgetPosition,
			inlineBorderRadius: interactiveCollection?.inlineBorderRadius
		});

		const fileDestination = `${CDN_DIR.DATACACHE}collections/` +
		`${interactiveCollection._id.toString()}-collection.json`;

		const secrets = await getSecrets();
		const storageProvider = new GCPStorageProvider(
			"https://storage.googleapis.com",
			secrets.storage.bucketName,
			secrets.storage.host,
			secrets.storage.credentials
		);
		const storageCacheControl: StorageCacheControl = {
			maxAge: 0,
			public: true
		};
		const storageService = new StorageService(storageProvider);

		await storageService.writeFromBuffer(
			fileDestination,
			Buffer.from(fileContent),
			storageCacheControl
		);
	}

	public async updateSessionMetrics (collectionId: string, sessionCount: number, userCVR: number): Promise<void> {
		const filter: FilterQuery<IShoppableCollection> = {
			_id: new mongoose.Types.ObjectId(collectionId)
		};

		const updateData = {
			$set: {
				userSessionCount: sessionCount,
				userCVR: userCVR
			}
		};

		await InteractiveCollectionDBModel.updateOne(filter, updateData).session(this.session);
	}

	private async getSortedInteractiveVideos(interactiveCollection: IShoppableCollection): Promise<IShoppableVideo[]> {
		if (interactiveCollection.shoppableVideos.length === 0) return [];

		const interactiveVideoModel = new InteractiveVideoModel(this.session);
		const relatedVideoDocuments = await interactiveVideoModel.readManyByIds(interactiveCollection.shoppableVideos);

		if (relatedVideoDocuments.length < 1) {
			throw new APIError(APIErrorName.E_INTERNAL_ERROR, "Failed to read interactive videos.");
		}

		const videoMap = new Map(relatedVideoDocuments.map(video => [video._id.toString(), video]));
		return interactiveCollection.shoppableVideos
			.map(videoId => videoMap.get(videoId.toString()))
			.filter(video => video !== undefined) as IShoppableVideo[];
	}

	async redactData(interactiveCollections: IShoppableCollection[]): Promise<IShoppableCollection[]> {
		if (interactiveCollections.length === 0) return [];

		const accountId = interactiveCollections[0].accountId;
		const accountModel: AccountModel = new AccountModel(this.session);
		const accountDocument = await accountModel.readOneById(accountId.toString());

		const shouldRedactConversionMetrics = !accountDocument.subscription.enableConversionMetrics;

		interactiveCollections.forEach(collection => {
			if (shouldRedactConversionMetrics) {
				collection.userCVR = 0;
				collection.userSessionCount = 0;
			}
		});
		return interactiveCollections;

	}

}
