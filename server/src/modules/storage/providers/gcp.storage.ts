import {
	Bucket,
	Storage
} from "@google-cloud/storage";
import { StorageProvider } from "../storage.model";

export class GCPStorageProvider extends StorageProvider {
	private storage: Storage;

	constructor(
		publicHost: string,
		private bucketName: string,
		private apiEndpoint: string,
		private credentials: any
	) {
		super(publicHost);
		this.storage = new Storage({ apiEndpoint: this.apiEndpoint, credentials: this.credentials });
	}

	public async readToBuffer(storagePath: string): Promise<Buffer> {
		const bucket = await this.getBucket();
		const file = bucket.file(storagePath);
		return file.download().then(data => data[0]);
	}

	public async readToFile(storagePath: string, localPath: string): Promise<void> {
		const bucket = await this.getBucket();
		const file = bucket.file(storagePath);
		await file.download({ destination: localPath });
	}

	public async writeFromBuffer(filePath: string, buffer: Buffer, cacheControl: string): Promise<string> {
		const bucket = await this.getBucket();
		const file = bucket.file(filePath);
		await file.save(buffer, {
			metadata: {
				cacheControl: cacheControl
			}
		});
		return file.name;
	}

	public async writeFromStream(
		filePath: string,
		stream: NodeJS.ReadableStream,
		cacheControl: string
	): Promise<string> {
		const bucket = await this.getBucket();
		const file = bucket.file(filePath);
		await new Promise<void>((resolve, reject) => {
			const writeStream = file.createWriteStream({
				metadata: {
					cacheControl: cacheControl
				}
			});
			writeStream.on("error", reject);
			writeStream.on("finish", resolve);
			stream.pipe(writeStream);
		});
		return file.name;
	}

	private async getBucket(): Promise<Bucket> {
		const bucket = this.storage.bucket(this.bucketName);

		const bucketExists: boolean[] = await bucket.exists();
		if (!bucketExists[0]) {
			throw new Error(`Failed to find GCP Storage Bucket: ${this.bucketName}`);
		}
		return bucket;
	}
}
