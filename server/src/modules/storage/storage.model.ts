export interface StorageCacheControl {
	maxAge?: number;
	public?: boolean;
	private?: boolean;
	noCache?: boolean;
	noStore?: boolean;
	mustRevalidate?: boolean;
	immutable?: boolean;
}

export class StorageProvider {
	constructor(public publicHost: string) {}

	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	public async readToBuffer(storagePath: string): Promise<Buffer> {
		throw new Error("Method StorageProvider.readToBuffer not implemented. This is a generic class.");
	}

	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	public async readToFile(storagePath: string, localPath: string): Promise<void> {
		throw new Error("Method StorageProvider.readToFile not implemented. This is a generic class.");
	}

	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	public async writeFromBuffer(filePath: string, buffer: Buffer, cacheControl: string): Promise<string> {
		throw new Error("Method StorageProvider.writeFromBuffer not implemented. This is a generic class.");
	}

	public async writeFromStream(
		// eslint-disable-next-line @typescript-eslint/no-unused-vars
		filePath: string,
		// eslint-disable-next-line @typescript-eslint/no-unused-vars
		stream: NodeJS.ReadableStream,
		// eslint-disable-next-line @typescript-eslint/no-unused-vars
		cacheControl: string
	): Promise<string> {
		throw new Error("Method StorageProvider.writeFromStream not implemented. This is a generic class.");
	}
}
