import * as crypto from "crypto";
import * as fs from "fs";
import * as os from "os";
import * as path from "path";
import { Readable } from "stream";
import {
	StorageCacheControl,
	StorageProvider
} from "./storage.model";

export class StorageService {
	constructor(public provider: StorageProvider) {
	}

	public createRandomFileName(extension: string): string {
		const randomName = `${crypto.randomUUID()}${extension}`;
		return randomName;
	}

	public getUrl(storagePath: string): string {
		const publicHost = this.provider.publicHost.replace(/\/+$/, "");
		const pathName = storagePath.replace(/^\/+/, "");
		return `${publicHost}/${pathName}`;
	}

	public async readToBuffer(storagePath: string): Promise<Buffer> {
		return await this.provider.readToBuffer(storagePath);
	}

	public async readToFile(storagePath: string, localPath: string): Promise<void> {
		await this.provider.readToFile(storagePath, localPath);
	}

	public async writeFromBuffer(
		storagePath: string,
		buffer: Buffer,
		cacheControl: StorageCacheControl
	): Promise<string> {
		const cacheControlString = this.cacheControlToString(cacheControl);
		return await this.provider.writeFromBuffer(storagePath, buffer, cacheControlString);
	}

	public async writeFromFile(
		storagePath: string,
		localPath: string,
		cacheControl: StorageCacheControl
	): Promise<void> {
		const fileStream = fs.createReadStream(localPath);
		const cacheControlString = this.cacheControlToString(cacheControl);
		await this.provider.writeFromStream(storagePath, fileStream, cacheControlString);
	}

	public async writeFromUrl(storagePath: string, url: string, cacheControl: StorageCacheControl): Promise<void> {
		const response = await fetch(url);
		if (!response.body || !response.ok) {
			throw new Error(`Failed to fetch file from URL: ${response.status} ${response.statusText}`);
		}

		const tmpDir = await fs.promises.mkdtemp(path.join(os.tmpdir(), "tmp-"));
		const tmpFile = path.join(tmpDir, path.basename(storagePath));

		try {
			let nodeStream: any;
			if ((response.body as any).pipe) {
				nodeStream = response.body;
			} else if (typeof Readable.fromWeb === "function") {
				nodeStream = Readable.fromWeb(response.body as any);
			} else {
				throw new Error("Unable to convert response.body to Node.js stream");
			}

			await new Promise<void>((resolve, reject) => {
				const fileStream = fs.createWriteStream(tmpFile);
				fileStream.on("error", reject);
				fileStream.on("finish", resolve);
				nodeStream.on("error", reject);
				nodeStream.pipe(fileStream);
			});

			await this.writeFromFile(storagePath, tmpFile, cacheControl);
		} finally {
			await this.silentCleanup(fs.promises.rm(tmpDir, { recursive: true, force: true }));
		}
	}

	private silentCleanup(promise: Promise<unknown>): Promise<void> {
		return promise.catch(() => undefined) as Promise<void>;
	}

	private cacheControlToString(options: StorageCacheControl): string {
		const directives: string[] = [];
		if (options.public) directives.push("public");
		if (options.private) directives.push("private");
		if (options.noCache) directives.push("no-cache");
		if (options.noStore) directives.push("no-store");
		if (options.mustRevalidate) directives.push("must-revalidate");
		if (options.immutable) directives.push("immutable");
		if (typeof options.maxAge === "number") directives.push(`max-age=${options.maxAge}`);
		return directives.join(", ");
	}
}
