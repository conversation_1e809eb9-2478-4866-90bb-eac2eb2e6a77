import { GoogleAuth } from "google-auth-library";
import { getSecrets } from "../secrets/secrets.model";
import axios from "axios";
import { VertexParameters } from "./vertex.interfaces";

export class VertexModel {
	public async getAIResponse(params: VertexParameters): Promise<string> {
		try {
			const secrets = await getSecrets();

			const auth = new GoogleAuth({
				credentials: secrets.vertex.credentials,
				scopes: "https://www.googleapis.com/auth/cloud-platform"
			});

			const client = await auth.getClient();
			const accessToken = await client.getAccessToken();
			const url = (
				`https://${params.region}-aiplatform.googleapis.com/v1/projects/` +
				`${params.projectId}/locations/${params.region}/publishers/google/models/` +
				`${params.model}:generateContent`
			);

			const requestBody = {
				contents: [
					{
						role: "user",
						parts: [
							{ text: params.prompt }
						]
					}
				],
				generationConfig: {
					temperature: params.temperature,
					maxOutputTokens: params.maxTokens
				}
			};

			const response = await axios.post(url, requestBody, {
				headers: {
					Authorization: `Bearer ${accessToken.token}`,
					"Content-Type": "application/json"
				}
			});

			const generatedText = response.data?.candidates?.[0]?.content?.parts?.[0]?.text || "No AI response.";
			return generatedText;

		} catch (error: unknown) {
			return "Failed to get AI response.";
		}
	}
}
