import express, {
	Request,
	Response
} from "express";
import { Controller } from "../base/base.controller";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";
import { CheckInstallService } from "./checkInstall.service";
import { CheckInstallSchema } from "./checkInstall.model";
import { BaseRequest } from "../base/base.interfaces";

export class CheckInstallController extends Controller {
	constructor() {
		super();

		this.router.get(
			"/",
			[express.json()],
			async (request: Request, response: Response) => {
				try {
					const payload = await this.validateGetPayload(request);

					await this.verifyAPIVersion(payload.apiVersion, 1);
					await this.verifyAccessToken(payload.accessToken);
					const accountToken = await this.verifyAccountToken(payload.accountToken);

					const checkInstallService = new CheckInstallService();
					const result = await checkInstallService.checkInstallation(
						accountToken.account._id.toString(),
						request.url
					);

					return response.status(200).json(result);
				} catch (error: unknown) {
					return APIError.fromUnknownError(error).log().setResponse(response);
				}
			}
		);
	}

	private async validateGetPayload(request: Request): Promise<BaseRequest> {
		try {
			return await CheckInstallSchema.validateAsync({
				apiVersion: request.headers["x-api-version"],
				accessToken: request.headers.authorization,
				accountToken: request.headers["x-account-token"]
			});
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, error);
		}
	}
}
