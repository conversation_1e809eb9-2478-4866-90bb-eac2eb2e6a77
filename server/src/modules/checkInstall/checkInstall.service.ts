import {
	parse,
	HTMLElement
} from "node-html-parser";
import { readAccount } from "../../services/mongodb/account.service";
import {
	APIErrorName,
	Permission
} from "../../interfaces/apiTypes";
import {
	getSecrets,
	ISecrets
} from "../secrets/secrets.model";
import { APIError } from "../../utils/helpers/apiError";
import { validateBackslash } from "../../utils/helpers/gp.helper";
import { CheckInstallResponse } from "./checkInstall.model";

export class CheckInstallService {
	public async checkInstallation(accountId: string, requestUrl: string): Promise<CheckInstallResponse> {
		const accountDocument = await readAccount({
			query: {
				_id: accountId
			},
			path: requestUrl,
			permissions: [
				Permission.ALL
			]
		});

		if (!accountDocument || accountDocument === null) {
			throw new APIError(APIErrorName.E_INTERNAL_ERROR,
				"Failed to read the account document with _id " +
				`${accountId} supplied in access token`);
		}

		try {
			new URL(accountDocument.companyURL);
		} catch (err) {
			throw new APIError(APIErrorName.E_INVALID_INPUT,
				`the company url in the account is not a valid url: ${accountDocument.companyURL}`);
		}

		const response = await fetch(accountDocument.companyURL, {
			method: "GET"
		});

		if (response.status === 404) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND,
				`the document in the account companyURL at ${accountDocument.companyURL} could not be retrieved`);
		}

		const root = parse(await response.text());

		const scriptTags: HTMLElement[] = root.querySelectorAll("script");

		const secrets: ISecrets = await getSecrets();

		const scriptSource = `${validateBackslash(secrets.player.host)}core.js`;

		const tag: HTMLElement | undefined = scriptTags.find(
			(s) => s.attributes.src === scriptSource && Object.prototype.hasOwnProperty.call(s.attributes, "defer"));

		return {
			installed: tag ? true : false
		};
	}
}
