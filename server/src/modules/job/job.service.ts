import mongoose, {
	ClientSession,
	FilterQuery
} from "mongoose";
import { APIErrorName } from "../../interfaces/apiTypes";
import { APIError } from "../../utils/helpers/apiError";
import {
	JobsStatus,
	JobsType,
	Job,
	JobModel
} from "./job.model";
import { AccountMetricModel } from "../account/account.metric.model";
import { VideoEncodeModel } from "../video/encode/video.encode.model";
import { ReportsAggregateModel } from "../reports/aggregate/aggregate.model";
import { ReportsGenerateModel } from "../reports/generate/reports.model";
import { AvatarService } from "../avatar/avatar.service";

export class JobService {
	private session: ClientSession | null;
	constructor (session: ClientSession | null) {
		this.session = session;
	}

	public static async createIndexes (): Promise<void> {
		await JobModel.collection.createIndexes([
			{ key: { tempFilename: 1 }, name: "tempFilename_index", unique: true }
		]);
	}

	public static async getIndexes (): Promise<unknown> {
		return await JobModel.collection.indexes();
	}

	async readOneByTempFilename (tempFilename: string): Promise<Job> {
		const filter: FilterQuery<Job> = {
			tempFilename: tempFilename
		};
		const document = await JobModel.findOne(filter).session(this.session);
		if (!document) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Job not found");
		}
		return document;
	}

	async readOneById (_id: string): Promise<Job> {
		const filter: FilterQuery<Job> = {
			_id: new mongoose.Types.ObjectId(_id)
		};
		const document = await JobModel.findOne(filter).session(this.session);
		if (!document) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Job not found");
		}
		return document;
	}

	async runJobById (jobId: string): Promise<void> {
		const job = await this.readOneById(jobId);

		if (job.type === JobsType.ENCODE_VIDEO) {
			const videoEncodeModel = new VideoEncodeModel(job);
			await videoEncodeModel.startEncoder();
		} else if (job.type === JobsType.SYNC_ACCOUNTS) {
			const accountMetricModel = new AccountMetricModel(null);
			await accountMetricModel.computeDailyWeeklyMetrics();
		} else if (job.type === JobsType.REPORTS_AGGREGATE) {
			const reportsAggregateModel = new ReportsAggregateModel(this.session);
			await reportsAggregateModel.aggregateAccounts(job);
		} else if (job.type === JobsType.REPORTS_AGGREGATE_SINCE) {
			const reportsAggregateModel = new ReportsAggregateModel(this.session);
			await reportsAggregateModel.aggregateAccounts(job);
		} else if (job.type === JobsType.REPORTS_GENERATE) {
			const reportsModel = new ReportsGenerateModel(this.session);
			await reportsModel.processAccounts(job);
		} else if (job.type === JobsType.AVATAR_VIDEO) {
			const avatarService = new AvatarService(this.session);
			await avatarService.processJob(jobId);
		} else {
			throw new APIError(APIErrorName.E_INVALID_INPUT, `Unsupported job type ${job.type}`);
		}
	}

	public async createJob (data: Partial<Job>): Promise<Job> {
		const options = { session: this.session };
		const job = new JobModel(data);
		const savedJob = await job.save(options);
		return savedJob;
	}

	public async updateJobStatus (jobId: string, status: JobsStatus, statusMessage: string): Promise<Job> {
		const filter: FilterQuery<Job> = { _id: new mongoose.Types.ObjectId(jobId) };
		const update = {
			status: status,
			statusMessage: statusMessage,
			updatedAt: Date.now()
		};

		const options = { session: this.session, new: true };

		const updatedJob = await JobModel.findOneAndUpdate(filter, update, options);
		if (!updatedJob) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Job not found");
		}

		return updatedJob;
	}
}


