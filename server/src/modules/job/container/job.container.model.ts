import mongoose, { ClientSession } from "mongoose";
import Docker, { ContainerCreateOptions } from "dockerode";
import CloudRun, { JobsClient } from "@google-cloud/run";
import { PassThrough } from "stream";
import { APIErrorName } from "../../../interfaces/apiTypes";
import { APIError } from "../../../utils/helpers/apiError";
import {
	gpLog,
	LogScope
} from "../../../utils/managers/gpLog.manager";
import { JobModel } from "../job.model";

interface Timestamp {
	seconds: string;
	nanos: number;
}
interface Condition {
	state: string;
	[key: string]: unknown;
}
interface GCPJob {
	name: string;
	updateTime: Timestamp;
	terminalCondition: Condition;
	[key: string]: unknown;
}

interface CloudRunConfig {
	projectId: string;
	locationId: string;
	cloudRunImageUri: string;
	secretsPath: string;
	vpcConnector: string;
	cloudRunParent: string;
}

interface LocalJobConfig {
	imageName: string;
	serverPath: string;
	volumePath: string;
	networkName: string;
}

export class JobContainerModel {
	private isLocal: boolean;
	private session: ClientSession | null;
	private jobClient: JobsClient | null;
	private dockerClient: Docker | null;
	private cloudRunConfig: CloudRunConfig | null;
	private localConfig: LocalJobConfig | null;

	constructor(session: ClientSession | null, isLocalJob = false) {
		this.session = session;
		this.isLocal = isLocalJob;

		if (this.isLocal) {
			this.dockerClient = new Docker();
			this.localConfig = this.getLocalConfig();
			this.jobClient = null;
			this.cloudRunConfig = null;
		} else {
			this.jobClient = new CloudRun.JobsClient();
			this.cloudRunConfig = this.getCloudRunConfig();
			this.dockerClient = null;
			this.localConfig = null;
		}
	}

	private getCloudRunVPCConnector(): string {
		const projectId = process.env.GCP_PROJECT_ID;
		if (!projectId) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				"Missing required GCP_PROJECT_ID environment variables for Cloud Run VPC connector."
			);
		}

		const region = process.env.GCP_CLOUD_RUN_REGION;
		if (!region) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				"Missing required GCP_CLOUD_RUN_REGION environment variables for Cloud Run VPC connector."
			);
		}

		const connectorId = process.env.GCP_CLOUD_RUN_CONNECTOR_ID;
		if (!connectorId) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				"Missing required GCP_CLOUD_RUN_CONNECTOR_ID environment variables for Cloud Run VPC connector."
			);
		}

		return `projects/${projectId}/locations/${region}/connectors/${connectorId}`;
	}

	private getCloudRunImageUri(): string {
		const projectId = process.env.GCP_PROJECT_ID;
		if (!projectId) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				"Missing required GCP_PROJECT_ID environment variables for Cloud Run Image Uri."
			);
		}

		const cloudRunName = process.env.GCP_CLOUD_RUN_NAME;
		if (!cloudRunName) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				"Missing required GCP_CLOUD_RUN_NAME environment variables for Cloud Run Image Uri."
			);
		}

		const repositoryName = process.env.REPOSITORY;
		if (!repositoryName) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				"Missing required GCP_PROJECT_ID environment variables for Cloud Run Image Uri."
			);
		}

		return `us-docker.pkg.dev/${projectId}/${repositoryName}/${cloudRunName}`;
	}

	private getCloudRunParent(): string {
		const projectId = process.env.GCP_PROJECT_ID;
		if (!projectId) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				"Missing required GCP_PROJECT_ID environment variables for Cloud Run parent."
			);
		}

		const region = process.env.GCP_CLOUD_RUN_REGION;
		if (!region) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				"Missing required GCP_CLOUD_RUN_REGION environment variables for Cloud Run parent."
			);
		}

		return `projects/${projectId}/locations/${region}`;
	}



	private getCloudRunConfig(): CloudRunConfig {
		const { SECRETS_RESOURCE, GCP_PROJECT_ID, GCP_CLOUD_RUN_REGION } = process.env;

		if (!SECRETS_RESOURCE) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				"Missing required SECRETS_RESOURCE environment variables for Cloud Run job creation."
			);
		}

		if (!GCP_PROJECT_ID) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				"Missing required GCP_PROJECT_ID environment variables for Cloud Run job creation."
			);
		}

		if (!GCP_CLOUD_RUN_REGION) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				"Missing required GCP_CLOUD_RUN_REGION environment variables for Cloud Run job creation."
			);
		}

		return {
			projectId: GCP_PROJECT_ID,
			locationId: GCP_CLOUD_RUN_REGION,
			cloudRunImageUri: this.getCloudRunImageUri(),
			secretsPath: SECRETS_RESOURCE,
			vpcConnector: this.getCloudRunVPCConnector(),
			cloudRunParent: this.getCloudRunParent()
		};
	}

	private getLocalConfig(): LocalJobConfig {
		const { IMAGE_NAME, SERVER_PATH, VOLUME_PATH, NETWORK_NAME } = process.env;

		if (!IMAGE_NAME) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				"Missing required IMAGE_NAME environment variables for local Docker Job Worker creation."
			);
		}

		if (!SERVER_PATH) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				"Missing required SERVER_PATH environment variables for local Docker Job Worker creation."
			);
		}

		if (!VOLUME_PATH) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				"Missing required VOLUME_PATH environment variables for local Docker Job Worker creation."
			);
		}

		if (!NETWORK_NAME) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				"Missing required NETWORK_NAME environment variables for local Docker Job Worker creation."
			);
		}

		return {
			imageName: IMAGE_NAME,
			serverPath: SERVER_PATH,
			volumePath: VOLUME_PATH,
			networkName: NETWORK_NAME
		};
	}

	public async runJobWorker(jobId: string, timeoutSeconds: number): Promise<void> {
		if (this.isLocal) {
			await this.runLocalJobWorker(jobId);
		} else {
			await this.runCloudJobWorker(jobId, timeoutSeconds);
		}
	}

	private async runCloudJobWorker(jobId: string, timeoutSeconds: number): Promise<void> {
		this.logInfo("Starting a Cloud Run Job Worker...", { jobId }, "runCloudJobWorker");
		try {
			if (!this.jobClient || !this.cloudRunConfig) {
				throw new APIError(APIErrorName.E_INTERNAL_ERROR, "Cloud Run client is not initialized.");
			}
			const createdJobName = await this.createCloudRunJob(jobId, timeoutSeconds);
			await this.startCloudRunJob(createdJobName, jobId);
		} catch (error: unknown) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				`Failed to create or execute Cloud Run job. ${(error as Error).message}`
			);
		}
	}

	private async runLocalJobWorker(jobId: string): Promise<void> {
		this.logInfo("Starting Docker Job Worker...", { jobId }, "runLocalJobWorker");

		try {
			if (!this.dockerClient || !this.localConfig) {
				throw new APIError(APIErrorName.E_INTERNAL_ERROR, "Docker client is not initialized.");
			}

			const containerOptions = this.buildContainerOptions(jobId);
			const container = await this.dockerClient.createContainer(containerOptions);
			await container.start();
			this.logInfo(
				"Docker container started successfully.",
				{ containerName: `job-${jobId}`, container, jobId },
				"runLocalJobWorker"
			);

			const logStream = await container.logs({
				follow: true,
				stdout: true,
				stderr: true,
				timestamps: false
			});

			// Use Docker's built-in demux to properly handle the log stream format
			const stdout = new PassThrough();
			const stderr = new PassThrough();

			container.modem.demuxStream(logStream, stdout, stderr);

			stdout.on("data", (chunk: Buffer) => {
				const cleanOutput = chunk.toString("utf8").trim();
				if (cleanOutput) {
					// eslint-disable-next-line no-console
					console.log(cleanOutput);
				}
			});

			stderr.on("data", (chunk: Buffer) => {
				const cleanOutput = chunk.toString("utf8").trim();
				if (cleanOutput) {
					// eslint-disable-next-line no-console
					console.error(cleanOutput);
				}
			});

			logStream.on("error", (err: Error) => {
				console.error(err.message);
			});
		} catch (error: unknown) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				`Failed to create or execute local Docker Job Worker. ${(error as Error).message}`
			);
		}
	}

	private async createCloudRunJob(jobId: string, timeoutSeconds: number): Promise<string> {
		if (!this.cloudRunConfig || !this.jobClient) {
			throw new APIError(APIErrorName.E_INTERNAL_ERROR, "Cloud Run client is not initialized.");
		}

		const cloudRunParent = this.cloudRunConfig.cloudRunParent;
		const cloudRunJobId = `job-${jobId}`;
		const jobConfig = this.buildCloudRunJobConfiguration(jobId, timeoutSeconds);

		const [operation] = await this.jobClient.createJob({
			parent: cloudRunParent,
			jobId: cloudRunJobId,
			job: jobConfig
		});
		const [completedOperation] = await operation.promise();

		if (completedOperation && completedOperation.name) {
			this.logInfo(
				"Cloud Run Job created successfully.",
				{ jobId, jobName: completedOperation.name },
				"createCloudRunJob"
			);
			return completedOperation.name;
		}
		throw new APIError(APIErrorName.E_INTERNAL_ERROR, "Cloud Run job creation failed.");
	}

	private buildCloudRunJobConfiguration(jobId: string, timeoutSeconds: number): any {
		if (!this.cloudRunConfig) {
			throw new APIError(APIErrorName.E_INTERNAL_ERROR, "Cloud Run configuration is not initialized.");
		}

		return {
			template: {
				template: {
					timeout: { seconds: timeoutSeconds },
					containers: [
						{
							image: this.cloudRunConfig.cloudRunImageUri,
							env: [
								{ name: "SECRETS_RESOURCE", value: this.cloudRunConfig.secretsPath },
								{ name: "jobId", value: jobId }
							],
							resources: { limits: { memory: "2Gi", cpu: "4" } }
						}
					],
					vpcAccess: {
						connector: this.cloudRunConfig.vpcConnector,
						egress: "PRIVATE_RANGES_ONLY"
					},
					maxRetries: 0
				}
			}
		};
	}

	private async startCloudRunJob(jobName: string, jobId: string): Promise<void> {
		if (!this.jobClient) {
			throw new APIError(APIErrorName.E_INTERNAL_ERROR, "Cloud Run client is not initialized.");
		}
		await this.jobClient.runJob({ name: jobName });
		this.logInfo("Cloud Run Job started successfully.", { jobId, jobName }, "startCloudRunJob");
	}

	private buildContainerOptions(jobId: string): ContainerCreateOptions {
		if (!this.localConfig) {
			throw new APIError(APIErrorName.E_INTERNAL_ERROR, "Local job configuration is not initialized.");
		}
		return {
			Image: this.localConfig.imageName,
			Cmd: ["/bin/sh", "-c", "npm install && npm run build && npm run start"],
			name: `job-${jobId}`,
			Env: [`jobId=${jobId}`],
			HostConfig: {
				Memory: 2 * 1024 * 1024 * 1024,
				CpuShares: 1024,
				AutoRemove: true,
				Binds: this.buildVolumeBindings(),
				NetworkMode: this.localConfig.networkName
			},
			AttachStdin: false,
			AttachStdout: true,
			AttachStderr: true,
			Tty: false
		};
	}

	private buildVolumeBindings(): string[] {
		if (!this.localConfig) {
			throw new APIError(APIErrorName.E_INTERNAL_ERROR, "Local job configuration is not initialized.");
		}
		const serverPath = this.localConfig.serverPath;
		const volumePath = this.localConfig.volumePath;
		return [
			`${serverPath}/src:/usr/src/app/server/src`,
			`${serverPath}/package.json:/usr/src/app/server/package.json`,
			`${serverPath}/package-lock.json:/usr/src/app/server/package-lock.json`,
			`${serverPath}/tsconfig.json:/usr/src/app/server/tsconfig.json`,
			`${serverPath}/nodemon.json:/usr/src/app/server/nodemon.json`,
			`${volumePath}/secrets:/secrets`,
			`${volumePath}/storage:/storage`
		];
	}

	public async cleanUpCompletedJobs(): Promise<void> {
		if (this.isLocal) {
			this.logInfo("Local jobs are cleaned up automatically.", {}, "cleanUpCompletedJobs");
		} else {
			await this.cleanUpCloudCompletedJobs();
		}
	}

	private async cleanUpCloudCompletedJobs(): Promise<void> {
		try {
			const completedGCPJobs = await this.fetchCompletedFromCloud();

			if (completedGCPJobs.length === 0) {
				await this.deleteExpired();
				return;
			}

			const removePromises = completedGCPJobs.map((job) => this.removeFromCloudAndDB(job));
			const removeResults = await Promise.allSettled(removePromises);
			const failedRemovals = removeResults.filter((result) => result.status === "rejected");

			if (failedRemovals.length > 0) {
				throw new APIError(APIErrorName.E_INTERNAL_ERROR, "Failed to delete all jobs.");
			}
		} catch (error: unknown) {
			throw new APIError(
				APIErrorName.E_INTERNAL_ERROR,
				"Error cleaning up completed jobs: " + (error as Error).message
			);
		}
	}




	private async fetchCompletedFromCloud(limit = 20): Promise<GCPJob[]> {
		const eligibleJobs: GCPJob[] = [];

		try {
			if (!this.jobClient || !this.cloudRunConfig) {
				throw new APIError(APIErrorName.E_INTERNAL_ERROR, "Cloud Run client is not initialized.");
			}

			const iterable = this.jobClient.listJobsAsync({
				parent: this.cloudRunConfig.cloudRunParent,
				pageSize: limit
			});

			for await (const job of iterable) {
				if (this.isJobEligibleForRemoval(job as GCPJob)) {
					eligibleJobs.push(job as GCPJob);
					if (eligibleJobs.length >= limit) {
						break;
					}
				}
			}
			return eligibleJobs;
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_SERVICE_FAILED,
				`Failed to fetchCompletedFromCloud. ${(error as Error).message}`);
		}
	}

	private isJobEligibleForRemoval(job: GCPJob): boolean {
		const oneHourAgo = new Date(Date.now() - 3600000);
		const updateTime = job.updateTime
			? new Date(parseInt(job.updateTime.seconds) * 1000 + job.updateTime.nanos / 1000000)
			: null;

		if (!updateTime || updateTime >= oneHourAgo) {
			return false;
		}

		if (job.terminalCondition) {
			const state = job.terminalCondition.state;
			return state === "CONDITION_SUCCEEDED" || state === "CONDITION_FAILED";
		}

		return false;
	}

	private async removeFromCloudAndDB(job: GCPJob): Promise<void> {
		try {
			if (!this.jobClient) {
				throw new Error("jobClient is undefined.");
			}
			if (!job.name) {
				throw new Error("GCP Job name is undefined.");
			}
			const jobId = job.name.split("/").pop()?.replace("job-", "");
			if (!jobId) {
				throw new Error("Invalid job ID extracted from job name.");
			}
			await this.jobClient.deleteJob({ name: job.name });
			await JobModel.findByIdAndDelete(new mongoose.Types.ObjectId(jobId));
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_SERVICE_FAILED,
				"Failed to removeFromCloudAndDB. " + (error as Error).message);
		}
	}

	private async deleteExpired(): Promise<void> {
		const oneHourAgoTimestamp = new Date(Date.now() - 3600 * 1000).getTime();
		await JobModel.deleteMany({ updatedAt: { $lt: oneHourAgoTimestamp } }).session(this.session);
	}

	private logInfo(message: string, objData: object, trace: string): void {
		gpLog({ message, objData, trace: `CloudRunJobManager | ${trace}`, scope: LogScope.INFO });
	}
}

