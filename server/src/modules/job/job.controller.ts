import {
	Request,
	Response
} from "express";
import { Controller } from "../base/base.controller";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";
import {
	JobGetSchema,
	JobListSchema,
	postJobSchema,
	Job,
	IJobPayload,
	JobGet,
	JobList,
	JobsType,
	JobModel
} from "./job.model";
import multer from "multer";
import { decodeAccess } from "../../middleware/decodeAccess.mw";
import { validateHeaders } from "../../middleware/validateHeaders.mw";
import { isSchemaValid } from "../../middleware/isSchemaValid.mw";
import { readAccount2 } from "../../services/mongodb/account.service";
import { UserModel } from "../user/user.model";
import {
	ImageModel,
	ImageOptimize
} from "../image/image.model";
import mongoose, { FilterQuery } from "mongoose";

export class JobController extends Controller {
	constructor () {
		super();

		this.router.get(
			"/",
			[],
			async (request: Request, response: Response) => {
				try {
					const payload = await this.validateListPayload(request);

					await this.verifyAccessToken(payload.accessToken);
					const accountToken = await this.verifyAccountToken(payload.accountToken);

					const filter: FilterQuery<Job> = {
						accountId: new mongoose.Types.ObjectId(accountToken.account._id)
					};

					if (payload.type) {
						filter.type = payload.type;
					}

					if (payload.status) {
						filter.status = payload.status;
					}

					const job = await JobModel.find(filter);

					return response.status(200).send(job);
				} catch (error: unknown) {
					return APIError.fromUnknownError(error).log().setResponse(response);
				}
			}
		);

		this.router.get(
			"/:id",
			[],
			async (request: Request, response: Response) => {
				try {
					const payload = await this.validateGetPayload(request);

					await this.verifyAccessToken(payload.accessToken);
					const accountToken = await this.verifyAccountToken(payload.accountToken);

					const filter: FilterQuery<Job> = {
						_id: new mongoose.Types.ObjectId(payload.id),
						accountId: new mongoose.Types.ObjectId(accountToken.account._id)
					};

					const job = await JobModel.findOne(filter);

					return response.status(200).send(job);
				} catch (error: unknown) {
					return APIError.fromUnknownError(error).log().setResponse(response);
				}
			}
		);

		this.router.post(
			"/",
			[
				multer().any(),
				decodeAccess,
				validateHeaders,
				isSchemaValid(postJobSchema.data)
			],
			postJobController
		);
	}

	private async validateListPayload(request: Request): Promise<JobList> {
		try {
			return await JobListSchema.validateAsync({
				apiVersion: request.headers["x-api-version"],
				accessToken: request.headers.authorization,
				accountToken: request.headers["x-account-token"],
				type: request.query.type,
				status: request.query.status
			});
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, error);
		}
	}

	private async validateGetPayload(request: Request): Promise<JobGet> {
		try {
			return await JobGetSchema.validateAsync({
				apiVersion: request.headers["x-api-version"],
				accessToken: request.headers.authorization,
				accountToken: request.headers["x-account-token"],
				id: request.params.id
			});
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, error);
		}
	}
}

const postJobController = async (req: Request, res: Response): Promise<Response> => {
	try {
		const payload = req.body as IJobPayload;

		if (!req.accountToken?.account._id) {
			throw new APIError(
				APIErrorName.E_MISSING_AUTHORIZATION,
				"Missing accountId in account token"
			);
		}

		const accountDocument = await readAccount2(
			{
				_id: req.accountToken.account._id
			},
			null
		);

		if (!accountDocument) {
			throw new APIError(
				APIErrorName.E_DOCUMENT_NOT_FOUND,
				"account not found."
			);
		}

		const userId = req.accessToken?.userId;
		if (!userId) {
			throw new APIError(APIErrorName.E_INVALID_AUTHORIZATION, "Missing userId in access token");
		}

		const userModel = new UserModel(null);
		const userDocument = await userModel.readOneById(userId);

		if (payload.jobType !== JobsType.OPTIMIZE_IMAGE) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid job type");
		}

		const imageModel = new ImageModel(null);

		const data: ImageOptimize = {
			imageWidthPx: payload.imageWidthPx,
			imageHeightPx: payload.imageHeightPx,
			sourceURL: payload.sourceURL
		};

		const job = await imageModel.createImageOptimizeJob(
			accountDocument._id.toString(), userDocument._id.toString(), data);
		return res.status(202).send({ job: job });

	} catch (error: any) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};
