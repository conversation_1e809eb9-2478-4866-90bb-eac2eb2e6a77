import {
	Request,
	Response
} from "express";
import { getSecrets } from "../../../modules/secrets/secrets.model";
import { VersionValidator } from "../../version/version.validators";
import { APIError } from "../../../utils/helpers/apiError";
import { APIErrorName } from "../../../interfaces/apiTypes";
import { JobContainerModel } from "../container/job.container.model";

export class JobCleanController {

	public post = async (request: Request, response: Response): Promise<Response> => {
		try {
			await VersionValidator.required().validateAsync(request.headers["x-api-version"]);
		} catch (error: unknown) {
			const apiError = new APIError(
				APIErrorName.E_INVALID_INPUT,
				error
			);
			return apiError.setResponse(response);
		}

		try {
			const secrets = await getSecrets();
			const cronKey = request.headers["x-cron-key"];
			if (cronKey !== secrets.cron.privateKey) {
				throw new APIError(APIErrorName.E_INVALID_AUTHORIZATION, "Unauthorized access via cron key.");
			}

			const jobContainerModel = new JobContainerModel(null, secrets.storage.isLocal);
			await jobContainerModel.cleanUpCompletedJobs();

			return response.status(200).send({});

		} catch (error: unknown) {
			return APIError.fromUnknownError(error).log().setResponse(response);
		}
	};


}
