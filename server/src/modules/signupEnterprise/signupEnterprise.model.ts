import { ClientSession } from "mongoose";
import { IAccount } from "../../modules/account/account.interfaces";
import {
	IUser,
	UserCreateOneInput
} from "../../modules/user/user.interfaces";
import { AccountDBModel } from "../../modules/account/accountDB.model";
import { InteractiveCollectionModel } from "../../modules/interactiveCollection/interactiveCollection.model";
import { VideoProfileModel } from "../../modules/videoProfile/videoProfile.model";
import { UserModel } from "../user/user.model";
import { AccountModel } from "../account/account.model";
import { StripeCustomerModel } from "../stripe/customer/customer.model";
import { StripeSubscriptionModel } from "../stripe/subscription/subscription.model";
import { StripePriceModel } from "../stripe/price/price.model";
import { ISignupPayload } from "../signup/signup.interfaces";
import { IAuthenticationOptions } from "../authentication/authentication.interface";
import { EmailPasswordAuthentication } from "../authentication/emailpassword.authentication.model";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";
import {
	getSecrets,
	ISecrets
} from "../secrets/secrets.model";
import Stripe from "stripe";
import {
	startDBTransaction,
	completeDBTransaction,
	cancelDBTransaction
} from "../../services/mongodb/transaction.service";
import {
	createAccessToken,
	createRefreshToken
} from "../../utils/tokens";

export class SignupEnterpriseModel {
	private session: ClientSession | null;

	constructor(session: ClientSession | null) {
		this.session = session;
	}

	public async createEnterpriseSignup(signupPayload: ISignupPayload): Promise<{
		accessToken: string;
		refreshToken: string;
	}> {
		try {
			await startDBTransaction(this.session);

			// Enterprise signups don't use invite tokens
			if (signupPayload.inviteToken) {
				throw new APIError(APIErrorName.E_INVALID_INPUT,
					"Enterprise signup does not support invitation tokens");
			}

			const secrets: ISecrets = await getSecrets();

			const createData: UserCreateOneInput = {
				firstName: signupPayload.firstName ?? "",
				lastName: signupPayload.lastName ?? "",
				email: signupPayload.email,
				postSignupCompleted: false,
				isPasswordSet: true,
				maxCompanies: signupPayload.maxCompanies ?? 1
			};

			const userModel = new UserModel(this.session);
			const userDocument = await userModel.createOne(createData);

			// Get company name
			const companyName = signupPayload.companyName ||
				AccountModel.createCompanyNameFromEmail(signupPayload.email);

			// Get an enterprise price from Stripe
			const stripePriceModel = new StripePriceModel();
			const enterprisePrice = await stripePriceModel.getEnterprisePrice();

			// Create a Stripe customer with enterprise type
			const stripeCustomerModel = new StripeCustomerModel();
			const stripeCustomer = await stripeCustomerModel.createCustomer(
				signupPayload.email,
				companyName,
				true
			);

			// Create subscription with the enterprise price
			const stripeSubscriptionModel = new StripeSubscriptionModel();
			const subscription = await stripeSubscriptionModel.createSubscription(stripeCustomer.id, enterprisePrice);

			// Ensure the subscription has the correct metadata for enterprise features
			const stripe = new Stripe(secrets.stripe.privateKey);
			await stripe.subscriptions.update(subscription.id, {
				metadata: {
					...subscription.metadata,
					type: "enterprise",
					allowLandscape: "true",
					allowCTALead: "true",
					allowThemes: "true",
					allowSharing: "true",
					enableConversionMetrics: "true",
					enableEngagementMetrics: "true",
					hideVanityBranding: "true",
					maxImpressionsPerCycle: "100000",
					maxInteractiveCollectionLimit: "500",
					maxInteractiveVideoLimit:	"500",
					maxVideoProductLinksLimit: "10"
				}
			});

			// Create account with enterprise settings
			let accountDocument = await this.createEnterpriseAccount(
				userDocument,
				stripeCustomer.id,
				companyName
			);

			// Set the subscription on the account
			const accountModel = new AccountModel(this.session);
			accountDocument = await accountModel.setSubscription(accountDocument._id.toString(), subscription.id);

			// Set account ID in Stripe customer metadata
			await stripeCustomerModel.setAccount(accountDocument);

			const options: IAuthenticationOptions = {
				verified: true,
				legalAgreement: signupPayload.legalAgreement
			};

			const authenticationModel = new EmailPasswordAuthentication(this.session);
			const authenticationDocument = await authenticationModel.createOne(
				userDocument._id.toString(),
				accountDocument._id.toString(),
				signupPayload.password,
				options
			);

			const accessToken = await createAccessToken(
				authenticationDocument,
				userDocument
			);
			if (!accessToken) {
				throw new APIError(APIErrorName.E_SERVICE_FAILED, "Failed to create access token");
			}

			const refreshToken = await createRefreshToken(authenticationDocument);
			if (!refreshToken) {
				throw new APIError(APIErrorName.E_SERVICE_FAILED, "Failed to create refresh token");
			}

			await completeDBTransaction(this.session);

			return {
				accessToken,
				refreshToken
			};
		} catch (error: unknown) {
			await cancelDBTransaction(this.session);
			throw error;
		}
	}

	public async createEnterpriseAccount(
		userDocument: IUser,
		stripeCustomerId: string,
		companyName?: string
	): Promise<IAccount> {
		const videoProfileModel = new VideoProfileModel(this.session);
		const defaultProfile = await videoProfileModel.readDefault();

		const insert: Partial<IAccount> = {
			ownerUserId: userDocument._id,
			companyName: companyName ?? "",
			stripeCustomerId: stripeCustomerId,
			videoProfile: defaultProfile._id
		};

		const model = new AccountDBModel(insert);
		let accountDocument = await model.save({ session: this.session });

		// Create default collection
		const interactiveCollectionModel = new InteractiveCollectionModel(this.session);
		const shoppableCollectionDocument = await interactiveCollectionModel.createOne({
			title: "Default",
			shoppableVideos: []
		}, accountDocument);

		shoppableCollectionDocument.throwIfError();

		// Update account with default collection ID
		accountDocument.defaultCollectionId = shoppableCollectionDocument.getData()._id;

		const filter = { _id: accountDocument._id };
		const update = { defaultCollectionId: shoppableCollectionDocument.getData()._id };

		accountDocument = await AccountDBModel.findOneAndUpdate(
			filter,
			update,
			{ new: true, session: this.session }
		) as IAccount;

		return accountDocument;
	}
}
