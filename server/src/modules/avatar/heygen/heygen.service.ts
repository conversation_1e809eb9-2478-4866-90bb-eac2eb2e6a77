import axios from "axios";
import { HeyGenVideoDimensions } from "./heygen.model";

export class HeyGenService {
	private apiKey: string;

	constructor(apiKey: string) {
		this.apiKey = apiKey;
	}

	public async createImageKey(name: string, buffer: Buffer, mimeType: string): Promise<string> {
		const uploadResponse = await axios.post("https://upload.heygen.com/v1/asset", buffer, {
			headers: {
				"X-Api-Key": this.apiKey,
				"Content-Type": mimeType
			}
		});

		if (!uploadResponse.data?.data?.id) {
			throw new Error("Failed to upload look image");
		}

		const imageKey = uploadResponse.data.data.image_key;

		return imageKey;
	}

	public async createGroup(name: string, imageKey: string): Promise<string> {
		const createGroupResponse = await axios.post(
			"https://api.heygen.com/v2/photo_avatar/avatar_group/create",
			{
				name: name,
				// eslint-disable-next-line camelcase
				image_key: imageKey
			},
			{
				headers: {
					"X-Api-Key": this.apiKey,
					"Content-Type": "application/json"
				}
			}
		);

		if (!createGroupResponse.data?.data?.group_id) {
			console.error("HeyGen API response:", createGroupResponse.data);
			throw new Error("Failed to create avatar group");
		}

		const groupId = createGroupResponse.data.data.group_id;

		await this.waitForGroupReady(groupId);

		return groupId;
	}

	public async getGroupDetails(groupId: string): Promise<any> {
		const response = await axios.get(`https://api.heygen.com/v2/avatar_group/${groupId}/avatars`, {
			headers: {
				"X-Api-Key": this.apiKey,
				"Content-Type": "application/json"
			}
		});

		if (!response.data?.data?.avatar_list) {
			throw new Error("Failed to get group details");
		}

		return response.data.data;
	}

	public async addToGroup(groupId: string, imageKey: string, name: string): Promise<void> {
		await axios.post(
			"https://api.heygen.com/v2/photo_avatar/avatar_group/add",
			{
				// eslint-disable-next-line camelcase
				group_id: groupId,
				// eslint-disable-next-line camelcase
				image_keys: [imageKey],
				name: name
			},
			{
				headers: {
					"X-Api-Key": this.apiKey,
					"Content-Type": "application/json"
				}
			}
		);

		await this.waitForGroupReady(groupId);
	}

	public async generateVideo(
		talkingPhotoId: string,
		audioUrl: string,
		videoDimensions: HeyGenVideoDimensions
	): Promise<string> {
		const headers = {
			"X-Api-Key": this.apiKey,
			"Content-Type": "application/json"
		};

		const body = {
			// eslint-disable-next-line camelcase
			video_inputs: [
				{
					character: {
						type: "talking_photo",
						// eslint-disable-next-line camelcase
						talking_photo_id: talkingPhotoId
					},
					voice: {
						type: "audio",
						// eslint-disable-next-line camelcase
						audio_url: audioUrl
					},
					background: {
						type: "color",
						value: "#000000"
					}
				}
			],
			caption: false,
			dimension: {
				width: videoDimensions.width,
				height: videoDimensions.height
			}
		};

		const response = await fetch(
			"https://api.heygen.com/v2/video/generate",
			{
				method: "POST",
				headers: headers,
				body: JSON.stringify(body)
			}
		);

		if (!response.ok) {
			const errorBody = await response.text();
			throw new Error(`HeyGen API request failed: ${response.status} ${response.statusText} ${errorBody}`);
		}

		const jsonResponse = await response.json();

		const heyGenVideoId = jsonResponse?.data?.video_id;
		if (!heyGenVideoId) {
			throw new Error("HeyGen API did not return a video id.");
		}

		const videoUrl = await this.getHeyGenVideoUrl(heyGenVideoId);

		if (!videoUrl) {
			throw new Error("HeyGen video URL is not available.");
		}

		return videoUrl;
	}

	private async getHeyGenVideoUrl(videoId: string): Promise<string> {
		const headers = {
			"X-Api-Key": this.apiKey,
			"Accept": "application/json"
		};
		const pollIntervalMs = 5000;
		const timeoutMinutes = 30;

		const overallTimeoutMs = timeoutMinutes * 60 * 1000;
		const startTime = Date.now();

		// Initial delay before polling
		await new Promise(res => setTimeout(res, pollIntervalMs));

		const poll = async (): Promise<string> => {
			const url = `https://api.heygen.com/v1/video_status.get?video_id=${videoId}`;
			const response = await fetch(url, { method: "GET", headers });
			if (!response.ok) {
				throw new Error(`HeyGen video status request failed: ${response.status} ${response.statusText}`);
			}

			const data = await response.json();

			if (data.data && data.data.video_url) {
				return data.data.video_url;
			}

			if (data.data && data.data.status === "failed") {
				const error = data.data.error;
				let errorMsg = "Unknown error";
				if (typeof error === "string") {
					errorMsg = error;
				} else if (error && typeof error === "object") {
					errorMsg = error.message || JSON.stringify(error);
				}
				throw new Error(`HeyGen video generation failed: ${errorMsg}`);
			}

			if (Date.now() - startTime > overallTimeoutMs) {
				throw new Error("Timed out waiting for HeyGen video to be ready.");
			}

			await new Promise(res => setTimeout(res, pollIntervalMs));
			return poll();
		};

		return poll();
	}

	private async waitForGroupReady(groupId: string): Promise<void> {
		const pollIntervalMs = 3000;
		const overallTimeoutMs = 5 * 60 * 1000;
		const startTime = Date.now();

		await new Promise(res => setTimeout(res, pollIntervalMs));

		const poll = async (): Promise<void> => {
			try {
				const groupDetails = await this.getGroupDetails(groupId);

				if (groupDetails.avatar_list && groupDetails.avatar_list.length > 0) {
					// eslint-disable-next-line camelcase
					const allReady = groupDetails.avatar_list.every(
						(al: any) => al.status === "completed"
					);

					const failed = groupDetails.avatar_list.some(
						(al: any) => al.status === "failed"
					);

					if (failed) {
						throw new Error("failed");
					}

					if (allReady) {
						return;
					}
				}

				if (Date.now() - startTime > overallTimeoutMs) {
					throw new Error("Timed out waiting for HeyGen group to be ready.");
				}

				await new Promise(res => setTimeout(res, pollIntervalMs));
				return poll();
			} catch (error) {
				if (Date.now() - startTime > overallTimeoutMs) {
					throw new Error("Timed out waiting for HeyGen group to be ready.");
				}

				if ((error as Error).message === "failed") {
					throw new Error("HeyGen group creation failed.");
				}

				await new Promise(res => setTimeout(res, pollIntervalMs));
				return poll();
			}
		};

		return poll();
	}

	public async getLookByName(groupId: string, name: string): Promise<any | null> {
		const groupDetails = await this.getGroupDetails(groupId);
		if (!groupDetails.avatar_list) return null;
		return groupDetails.avatar_list.find((a: any) => a.name === name) || null;
	}

	public async deleteLook(lookId: string): Promise<void> {
		const lookResponse = await axios.get(
			`https://api.heygen.com/v2/photo_avatar/${lookId}`,
			{
				headers: {
					"X-Api-Key": this.apiKey,
					"Accept": "application/json"
				}
			}
		);
		const groupId = lookResponse.data?.data?.group_id;

		await axios.delete(
			`https://api.heygen.com/v2/photo_avatar/${lookId}`,
			{
				headers: {
					"X-Api-Key": this.apiKey,
					"Accept": "application/json"
				}
			}
		);

		if (groupId) {
			const groupDetails = await this.getGroupDetails(groupId);
			if (groupDetails.avatar_list && groupDetails.avatar_list.length === 0) {
				await axios.delete(
					`https://api.heygen.com/v2/photo_avatar_group/${groupId}`,
					{
						headers: {
							"X-Api-Key": this.apiKey,
							"Accept": "application/json"
						}
					}
				);
			}
		}
	}
}
