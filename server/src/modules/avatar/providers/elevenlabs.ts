import FormData from "form-data";
import axios from "axios";
import { VoiceProvider } from "../voice/voice.model";

export class ElevenLabsProvider extends VoiceProvider {
	constructor(apiKey: string) {
		super(apiKey);
	}

	public static providerName = "elevenlabs";

	public async cloneVoice(name: string, mp3Buffer: Buffer): Promise<any> {
		const formData = new FormData();
		formData.append("name", name);

		const filename = `audio-${Date.now()}-${Math.floor(Math.random() * 10000)}.mp3`;
		formData.append("files", mp3Buffer, {
			filename,
			contentType: "audio/mpeg"
		});

		const response = await axios.post(
			"https://api.elevenlabs.io/v1/voices/add",
			formData,
			{
				headers: {
					"xi-api-key": this.apiKey,
					...formData.getHeaders()
				}
			}
		);

		return { voiceId: response.data.voice_id };
	}

	public async deleteVoice(voiceId: string): Promise<void> {
		const url = `https://api.elevenlabs.io/v1/voices/${voiceId}`;
		const headers = {
			"xi-api-key": this.apiKey,
			"Content-Type": "application/json"
		};

		try {
			await axios.delete(url, { headers });
		} catch (error) {
			if (axios.isAxiosError(error)) {
				if (!error.response) {
					throw new Error("Network error or invalid response from ElevenLabs API");
				}

				const errorBody = error.response.data;

				if (error.response.status === 400) {
					const isActuallyA404NotA400 = errorBody.detail.status === "voice_does_not_exist";

					if (isActuallyA404NotA400) {
						return;
					}
				}

				throw new Error(errorBody.detail.message);
			}

			throw error;
		}
	}

	public async generateAudio(text: string, voiceId: string): Promise<Buffer> {
		const url = `https://api.elevenlabs.io/v1/text-to-speech/${voiceId}?output_format=mp3_44100_128`;

		const headers = {
			"xi-api-key": this.apiKey,
			"Content-Type": "application/json"
		};

		const body = {
			text: text,
			// eslint-disable-next-line camelcase
			model_id: "eleven_multilingual_v2"
		};

		const response = await fetch(
			url,
			{
				method: "POST",
				headers,
				body: JSON.stringify(body)
			}
		);

		if (!response.ok) {
			throw new Error(`ElevenLabs API request failed: ${response.status} ${response.statusText}`);
		}

		const arrayBuffer = await response.arrayBuffer();
		const buffer = Buffer.from(arrayBuffer);
		return buffer;
	}
}
