import mongoose, { Schema } from "mongoose";
import { BaseJoi } from "../../base/base.joi";
import <PERSON><PERSON> from "joi";
import { BaseRequest } from "../../base/base.interfaces";

export enum VoiceProviderName {
	ELEVENLABS = "elevenlabs"
}

export interface Voice {
	_id: string;
	accountId: mongoose.Types.ObjectId;
	name: string;
	voiceId: string;
	provider: string;
	verified: boolean;
	createdAt: Date;
	updatedAt: Date;
}

export interface VoicePost extends BaseRequest {
	name: string;
	provider: string;
	audio: Express.Multer.File;
}

export interface VoiceDelete extends BaseRequest {
	id: string;
}

const VoiceSchema: Schema = new Schema<Voice>({
	accountId: {
		type: Schema.Types.ObjectId,
		required: true
	},
	name: {
		type: String,
		required: true
	},
	voiceId: {
		type: String,
		required: true,
		unique: true
	},
	provider: {
		type: String,
		required: true,
		enum: Object.values(VoiceProviderName)
	},
	verified: {
		type: Boolean,
		default: false
	},
	createdAt: {
		type: Date,
		default: Date.now
	},
	updatedAt: {
		type: Date,
		default: Date.now
	}
});

export const VoiceModel = mongoose.model<Voice>(
	"avatar_voices",
	VoiceSchema
);

export const VoicePostSchema = BaseJoi.append<VoicePost>({
	name: Joi.string().min(1).required(),
	provider: Joi.string().valid(...Object.values(VoiceProviderName)).required(),
	audio: Joi.object({
		fieldname: Joi.string().valid("audio").required(),
		originalname: Joi.string().required(),
		mimetype: Joi.string().pattern(/^audio\//).required(),
		size: Joi.number().min(1).required()
	}).required().unknown(true)
});

export const VoiceDeleteSchema = BaseJoi.append<VoiceDelete>({
	id: Joi.string().required().custom((value, helpers) => {
		if (!/^[a-fA-F0-9]{24}$/.test(value)) {
			return helpers.error("any.invalid");
		}
		return value;
	})
}).required();

export class VoiceProvider {
	protected apiKey: string;

	constructor(apiKey: string) {
		this.apiKey = apiKey;
	}

	// Each subclass should override this static property
	public static providerName = "generic";

	public get name(): string {
		return (this.constructor as typeof VoiceProvider).providerName;
	}

	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	public async cloneVoice(name: string, mp3Buffer: Buffer): Promise<any> {
		throw new Error("cloneVoice method not implemented for this provider");
	}

	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	public async deleteVoice(voiceId: string): Promise<void> {
		throw new Error("deleteVoice method not implemented for this provider");
	}

	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	public async generateAudio(text: string, voiceId: string): Promise<Buffer> {
		throw new Error("generateAudio method not implemented for this provider");
	}
}
