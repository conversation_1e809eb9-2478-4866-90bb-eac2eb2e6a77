import express, {
	Request,
	Response
} from "express";
import { Controller } from "../../base/base.controller";
import { APIError } from "../../../utils/helpers/apiError";
import { APIErrorName } from "../../../interfaces/apiTypes";
import multer from "multer";
import { VoiceService } from "./voice.service";
import { BaseJoi } from "../../base/base.joi";
import {
	VoiceDelete,
	VoiceDeleteSchema,
	VoicePost,
	VoicePostSchema
} from "./voice.model";

export class VoiceController extends Controller {
	constructor () {
		super();

		this.router.get(
			"/",
			[express.json({ limit: "2MB" })],
			async (request: Request, response: Response) => {
				try {
					const payload = await this.validateGetPayload(request);

					await this.verifyAccessToken(payload.accessToken);
					const accountToken = await this.verifyAccountToken(payload.accountToken);

					const voiceService = new VoiceService(response.locals.session);

					const voices = await voiceService.readByAccountId(accountToken.account._id);

					return response.status(200).send(voices);
				} catch (error: unknown) {
					return APIError.fromUnknownError(error).log().setResponse(response);
				}
			}
		);

		this.router.post(
			"/",
			[
				multer().single("audio")
			],
			async (request: Request, response: Response) => {
				try {
					const payload = await this.validatePostPayload(request);

					await this.verifyAccessToken(payload.accessToken);
					const accountToken = await this.verifyAccountToken(payload.accountToken);

					const voiceService = new VoiceService(response.locals.session);
					const voiceProvider = await voiceService.getVoiceProviderByName(payload.provider);

					const voice = await voiceService.createVoice(
						payload.name,
						payload.audio.buffer,
						accountToken.account._id.toString(),
						voiceProvider
					);

					return response.status(200).send(voice);
				} catch (error: unknown) {
					return APIError.fromUnknownError(error).log().setResponse(response);
				}
			}
		);

		this.router.delete(
			"/:id",
			async (request: Request, response: Response) => {
				try {
					const payload = await this.validateDeletePayload(request);

					await this.verifyAccessToken(payload.accessToken);
					const accountToken = await this.verifyAccountToken(payload.accountToken);
					await this.verifyDocumentAccess(payload.id, accountToken.account._id.toString());

					const voiceService = new VoiceService(response.locals.session);
					await voiceService.deleteVoice(payload.id);

					return response.status(204).send();
				} catch (error: unknown) {
					return APIError.fromUnknownError(error).log().setResponse(response);
				}
			}
		);
	}

	private async verifyDocumentAccess(_id: string, accountId: string): Promise<void> {
		const voiceService = new VoiceService(null);

		const result = await voiceService.verifyVoiceAccess(
			_id,
			accountId
		);

		if (result === null) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Voice not found");
		}

		if (result === false) {
			throw new APIError(
				APIErrorName.E_ACCESS_FORBIDDEN,
				"You do not have permission to delete this voice"
			);
		}
	}

	private async validateGetPayload(request: Request): Promise<{ accessToken: string; accountToken: string }> {
		try {
			return await BaseJoi.validateAsync({
				apiVersion: request.headers["x-api-version"],
				accessToken: request.headers.authorization,
				accountToken: request.headers["x-account-token"]
			});
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, error);
		}
	}

	private async validatePostPayload(request: Request): Promise<VoicePost> {
		try {
			return await VoicePostSchema.validateAsync({
				apiVersion: request.headers["x-api-version"],
				accessToken: request.headers.authorization,
				accountToken: request.headers["x-account-token"],
				name: request.body.name,
				audio: request.file,
				provider: request.body.provider
			});
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, error);
		}
	}

	private async validateDeletePayload(request: Request): Promise<VoiceDelete> {
		try {
			return await VoiceDeleteSchema.validateAsync({
				apiVersion: request.headers["x-api-version"],
				accessToken: request.headers.authorization,
				accountToken: request.headers["x-account-token"],
				id: request.params.id
			});
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, error);
		}
	}
}
