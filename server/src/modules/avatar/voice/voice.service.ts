import mongoose, { FilterQuery } from "mongoose";
import { ElevenLabsProvider } from "../providers/elevenlabs";
import {
	Voice,
	VoiceModel,
	VoiceProvider
} from "./voice.model";
import { getSecrets } from "../../secrets/secrets.model";

export class VoiceService {
	private session: mongoose.ClientSession | undefined;

	constructor(session: mongoose.ClientSession | null) {
		this.session = session || undefined;
	}

	public async getVoiceProviderByName(name: string): Promise<VoiceProvider> {
		const secrets = await getSecrets();
		if (name === ElevenLabsProvider.providerName) {
			return new ElevenLabsProvider(secrets.elevenlabs.apiKey);
		}

		throw new Error(`Unsupported voice API: ${name}`);
	}

	public async createVoice(
		name: string,
		buffer: Buffer,
		accountId: string,
		voiceAPI: VoiceProvider
	): Promise<Voice> {
		const createResult = await voiceAPI.cloneVoice(name, buffer);

		if (!createResult || !createResult.voiceId) {
			throw new Error("Failed to create voice");
		}

		const voiceDoc = {
			accountId: new mongoose.Types.ObjectId(accountId),
			name: name,
			voiceId: createResult.voiceId,
			provider: voiceAPI.name,
			verified: true,
			createdAt: new Date(),
			updatedAt: new Date()
		};

		const voiceModel = new VoiceModel(null);
		const result = await voiceModel.collection.insertOne(voiceDoc);
		return { _id: result.insertedId.toString(), ...voiceDoc };
	}

	public async readByAccountId(accountId: string): Promise<Voice[]> {
		const voiceModel = new VoiceModel(null);
		const filter: FilterQuery<Voice> = {
			accountId: new mongoose.Types.ObjectId(accountId)
		};

		const voices = await voiceModel.collection.find(filter).toArray();
		return voices.map(voice => ({
			_id: voice._id.toString(),
			accountId: voice.accountId.toString(),
			name: voice.name,
			voiceId: voice.voiceId,
			provider: voice.provider,
			verified: true,
			createdAt: voice.createdAt,
			updatedAt: voice.updatedAt
		}));
	}

	/**
	 * Verifies access to a voice document.
	 * @param _id document ID of the voice
	 * @param accountId ID of the account to verify access for
	 * @returns null if not found, false if access denied, true if access granted
	 */
	public async verifyVoiceAccess(
		_id: string,
		accountId: string
	): Promise<null | false | true> {
		const voiceModel = new VoiceModel(null);
		const filter: FilterQuery<Voice> = {
			_id: new mongoose.Types.ObjectId(_id)
		};

		const voice = await voiceModel.collection.findOne<Voice>(filter);
		if (!voice) {
			return null;
		}

		if (voice.accountId.toString() !== accountId) {
			return false;
		}

		return true;
	}

	public async deleteVoice(id: string): Promise<void> {
		const voiceModel = new VoiceModel(null);
		const filter: FilterQuery<Voice> = {
			_id: new mongoose.Types.ObjectId(id)
		};

		const voice = await voiceModel.collection.findOne(filter, { session: this.session });
		if (!voice) {
			throw new Error("Voice not found");
		}

		await voiceModel.collection.deleteOne(filter, { session: this.session });

		const voiceAPI = await this.getVoiceProviderByName(voice.provider);
		await voiceAPI.deleteVoice(voice.voiceId);
	}
}
