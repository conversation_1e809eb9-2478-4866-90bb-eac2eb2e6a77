import express, {
	Request,
	Response
} from "express";
import { Controller } from "../../base/base.controller";
import { APIError } from "../../../utils/helpers/apiError";
import {
	<PERSON>reateLookData,
	LookDelete,
	LookDeleteSchema,
	LookPost,
	LookPostSchema
} from "./look.model";
import { APIErrorName } from "../../../interfaces/apiTypes";
import { HeyGenService } from "../heygen/heygen.service";
import { LookService } from "./look.service";
import multer from "multer";
import { BaseJoi } from "../../base/base.joi";
import { getSecrets } from "../../secrets/secrets.model";

export class LookController extends Controller {
	constructor () {
		super();

		this.router.get(
			"/",
			[express.json({ limit: "2MB" })],
			async (request: Request, response: Response) => {
				try {
					const payload = await this.validateGetPayload(request);

					await this.verifyAccessToken(payload.accessToken);
					const accountToken = await this.verifyAccountToken(payload.accountToken);

					const lookService = new LookService();
					const looks = await lookService.readByAccountId(accountToken.account._id);

					return response.status(200).send(looks);
				} catch (error: unknown) {
					return APIError.fromUnknownError(error).log().setResponse(response);
				}
			}
		);

		this.router.post(
			"/",
			[
				multer().single("look")
			],
			async (request: Request, response: Response) => {
				try {
					const payload = await this.validatePostPayload(request);

					await this.verifyAccessToken(payload.accessToken);
					const accountToken = await this.verifyAccountToken(payload.accountToken);

					const createData: CreateLookData = {
						name: payload.name,
						buffer: payload.look.buffer,
						mimetype: payload.look.mimetype,
						accountId: accountToken.account._id.toString(),
						groupId: payload.groupId || null
					};

					const secrets = await getSecrets();
					const lookService = new LookService();
					const heyGenService = new HeyGenService(secrets.heygen.apiKey);
					const look = await lookService.createLook(createData, heyGenService);

					return response.status(200).send(look);
				} catch (error: unknown) {
					return APIError.fromUnknownError(error).log().setResponse(response);
				}
			}
		);
		this.router.delete(
			"/:id",
			async (request: Request, response: Response) => {
				try {
					const payload = await this.validateDeletePayload(request);

					await this.verifyAccessToken(payload.accessToken);
					await this.verifyAccountToken(payload.accountToken);

					const secrets = await getSecrets();
					const lookService = new LookService();
					const heyGenService = new HeyGenService(secrets.heygen.apiKey);
					await lookService.deleteLook(payload.id, heyGenService);

					return response.status(204).send();
				} catch (error: unknown) {
					return APIError.fromUnknownError(error).log().setResponse(response);
				}
			}
		);
	}

	private async validateGetPayload(request: Request): Promise<{ accessToken: string; accountToken: string }> {
		try {
			return await BaseJoi.validateAsync({
				apiVersion: request.headers["x-api-version"],
				accessToken: request.headers.authorization,
				accountToken: request.headers["x-account-token"]
			});
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, error);
		}
	}

	private async validatePostPayload(request: Request): Promise<LookPost> {
		try {
			return await LookPostSchema.validateAsync({
				apiVersion: request.headers["x-api-version"],
				accessToken: request.headers.authorization,
				accountToken: request.headers["x-account-token"],
				name: request.body.name,
				groupId: request.body.groupId || null,
				look: request.file
			});
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, error);
		}
	}

	private async validateDeletePayload(request: Request): Promise<LookDelete> {
		try {
			return await LookDeleteSchema.validateAsync({
				apiVersion: request.headers["x-api-version"],
				accessToken: request.headers.authorization,
				accountToken: request.headers["x-account-token"],
				id: request.params.id
			});
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, error);
		}
	}
}
