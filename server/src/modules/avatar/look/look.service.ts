import { BucketModel } from "../../bucket/bucket.model";
import { getSecrets } from "../../secrets/secrets.model";
import { HeyGenService } from "../heygen/heygen.service";
import {
	CreateLookData,
	Look,
	LookModel
} from "./look.model";
import mongoose, { FilterQuery } from "mongoose";
import { validateBackslash } from "../../../utils/helpers/gp.helper";
import axios from "axios";
import sharp from "sharp";

export class LookService {
	public async createLook(createData: CreateLookData, heyGenService: HeyGenService): Promise<Look> {
		const imageKey = await heyGenService.createImageKey(createData.name, createData.buffer, createData.mimetype);

		if (!imageKey) {
			console.error("Failed to create image key for look:", createData.name);
			throw new Error("Failed to create image key");
		}

		let groupId = createData.groupId || null;
		const groupName = `${createData.accountId} - ${createData.name}`;

		if (groupId) {
			await heyGenService.addToGroup(groupId, imageKey, groupName);
		} else {
			groupId = await heyGenService.createGroup(groupName, imageKey);
		}

		const look = await heyGenService.getLookByName(groupId, groupName);

		if (!look || !look.id) {
			throw new Error("Failed to find newly created look in group");
		}

		if (!look.image_url) {
			throw new Error("Look does not have an image URL");
		}

		const response = await axios.get(look.image_url, { responseType: "arraybuffer" });
		const imageBuffer = Buffer.from(response.data);

		const metadata = await sharp(imageBuffer).metadata();
		const width = metadata.width;
		const height = metadata.height;

		if (!width || !height) {
			throw new Error("Failed to get image dimensions");
		}

		const secrets = await getSecrets();
		const bucketModel = new BucketModel(
			secrets.storage.bucketName,
			secrets.storage.host,
			secrets.storage.credentials
		);

		const oneYear = ********;
		const cacheControl = `public, max-age=${oneYear}`;

		const imagePath = await bucketModel.uploadFileToBucketFromURL(
			look.image_url,
			"image/jpeg",
			cacheControl
		);

		const imageUrl = `${validateBackslash(secrets.cdn.host)}${imagePath}`;

		const lookDoc = {
			accountId: new mongoose.Types.ObjectId(createData.accountId),
			name: createData.name,
			lookId: look.id,
			groupId: groupId,
			imageUrl: imageUrl,
			imageWidth: width,
			imageHeight: height,
			createdAt: new Date(),
			updatedAt: new Date()
		};

		const lookModel = new LookModel(null);
		const result = await lookModel.collection.insertOne(lookDoc);
		return { _id: result.insertedId.toString(), ...lookDoc };
	}

	public async readByAccountId (accountId: string): Promise<Look[]> {
		const lookModel = new LookModel(null);
		const filter: FilterQuery<Look> = {
			accountId: new mongoose.Types.ObjectId(accountId)
		};

		const looks = await lookModel.collection.find(filter).toArray();
		return looks.map(look => ({
			_id: look._id.toString(),
			accountId: look.accountId.toString(),
			name: look.name,
			lookId: look.lookId,
			groupId: look.groupId,
			imageUrl: look.imageUrl,
			imageWidth: look.imageWidth,
			imageHeight: look.imageHeight,
			createdAt: look.createdAt,
			updatedAt: look.updatedAt
		}));
	}

	public async deleteLook(id: string, heyGenService: HeyGenService): Promise<void> {
		const lookModel = new LookModel(null);
		const look = await lookModel.collection.findOne({ _id: new mongoose.Types.ObjectId(id) });

		if (!look) {
			throw new Error("Look not found");
		}

		try {
			await heyGenService.deleteLook(look.lookId);
		} catch (error) {
			const is404 = error instanceof Error && error.message.includes("404");
			if (!is404) {
				throw error;
			}
		}

		await lookModel.collection.deleteOne({ _id: new mongoose.Types.ObjectId(id) });
	}
}

