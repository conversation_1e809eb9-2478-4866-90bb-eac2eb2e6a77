import mongoose, { Schema } from "mongoose";
import { <PERSON>J<PERSON> } from "../../base/base.joi";
import <PERSON><PERSON> from "joi";
import { BaseRequest } from "../../base/base.interfaces";

export interface Look {
	_id: string;
	accountId: mongoose.Types.ObjectId;
	name: string;
	groupId: string;
	lookId: string;
	imageUrl: string;
	imageWidth: number;
	imageHeight: number;
	createdAt: Date;
	updatedAt: Date;
}

export interface LookPost extends BaseRequest {
	name: string;
	groupId: string | null;
	look: Express.Multer.File;
}

export interface LookDelete extends BaseRequest {
	id: string;
}

export interface CreateLookData {
	name: string;
	buffer: Buffer;
	mimetype: string;
	accountId: string;
	groupId: string | null;
}

const LookSchema: Schema = new Schema<Look>({
	accountId: {
		type: Schema.Types.ObjectId,
		required: true
	},
	name: {
		type: String,
		required: true
	},
	lookId: {
		type: String,
		required: true,
		unique: true
	},
	groupId: {
		type: String,
		required: true,
		default: null
	},
	imageUrl: {
		type: String,
		required: true
	},
	imageWidth: {
		type: Number,
		required: true
	},
	imageHeight: {
		type: Number,
		required: true
	},
	createdAt: {
		type: Date,
		default: Date.now
	},
	updatedAt: {
		type: Date,
		default: Date.now
	}
});

export const LookModel = mongoose.model<Look>(
	"avatar_looks",
	LookSchema
);

export const LookPostSchema = BaseJoi.append<LookPost>({
	name: Joi.string().min(1).required(),
	groupId: Joi.string().allow(null),
	look: Joi.object({
		fieldname: Joi.string().valid("look").required(),
		originalname: Joi.string().required(),
		mimetype: Joi.string().valid("image/jpeg", "image/png").required(),
		size: Joi.number().min(1).required()
	}).required().unknown(true)
});

export const LookDeleteSchema = BaseJoi.append<LookDelete>({
	id: Joi.string().required().custom((value, helpers) => {
		if (!/^[a-fA-F0-9]{24}$/.test(value)) {
			return helpers.error("any.invalid");
		}
		return value;
	})
}).required();
