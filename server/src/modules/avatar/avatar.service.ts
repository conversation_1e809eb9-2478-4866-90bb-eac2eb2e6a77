import { HeyGenVideoDimensions } from "./heygen/heygen.model";
import mongoose, { ClientSession } from "mongoose";
import {
	Job,
	JobModel,
	JobsStatus,
	JobsType
} from "../job/job.model";
import {
	AvatarJob,
	AvatarPost
} from "./avatar.model";
import { JobService } from "../job/job.service";
import { getSecrets } from "../secrets/secrets.model";
import { JobContainerModel } from "../job/container/job.container.model";
import { IAccountToken } from "../account/account.interfaces";
import { VideoEncodeModel } from "../video/encode/video.encode.model";
import { HeyGenService } from "./heygen/heygen.service";
import { VoiceService } from "./voice/voice.service";
import { GCPStorageProvider } from "../storage/providers/gcp.storage";
import { StorageService } from "../storage/storage.service";
import { StorageCacheControl } from "../storage/storage.model";
import {
	Voice,
	VoiceModel
} from "./voice/voice.model";
import {
	Look,
	LookModel
} from "./look/look.model";

export class AvatarService {
	private session: ClientSession | null;

	constructor(session: ClientSession | null) {
		this.session = session;
	}

	public async createJob(payload: AvatarPost, accountToken: IAccountToken): Promise<Job> {
		const data: any = {
			type: JobsType.AVATAR_VIDEO,
			status: JobsStatus.CREATED,
			statusMessage: "avatar video processing job has been created.",
			accountId: accountToken.account._id,
			userId: accountToken.userId,
			avatarPost: payload
		};

		const jobService = new JobService(this.session);
		const job = await jobService.createJob(data);

		const jobTimeoutSeconds = 3600 * 1;
		const secrets = await getSecrets();
		const jobContainerModel = new JobContainerModel(this.session, secrets.storage.isLocal);
		await jobContainerModel.runJobWorker(job._id.toString(), jobTimeoutSeconds);
		return job;
	}

	public async processJob(jobId: string): Promise<void> {
		const jobService = new JobService(this.session);
		try {
			const job = await jobService.readOneById(jobId);
			const secrets = await getSecrets();

			const jobParams = this.validateJobParameters(job);

			jobService.updateJobStatus(job._id.toString(), JobsStatus.RUNNING, "generating audio");

			const voiceModel = new VoiceModel(this.session);
			const voiceDocument = await voiceModel.collection.findOne<Voice>({
				_id: new mongoose.Types.ObjectId(jobParams.voiceId),
				accountId: job.accountId
			});

			if (!voiceDocument) {
				throw new Error(`Voice with ID ${jobParams.voiceId} not found.`);
			}

			const voiceService = new VoiceService(null);
			const voiceProvider = await voiceService.getVoiceProviderByName(voiceDocument.provider);

			const audioBuffer = await voiceProvider.generateAudio(
				jobParams.text,
				voiceDocument.voiceId
			);

			jobService.updateJobStatus(job._id.toString(), JobsStatus.RUNNING, "uploading audio");

			const audioUrl = await this.uploadAudioToBucket(audioBuffer);

			jobService.updateJobStatus(job._id.toString(), JobsStatus.RUNNING, "generating video");

			const looksModel = new LookModel(this.session);
			const lookDocument = await looksModel.collection.findOne<Look>({
				_id: new mongoose.Types.ObjectId(jobParams.lookId),
				accountId: job.accountId
			});

			if (!lookDocument) {
				throw new Error(`Look with ID ${jobParams.lookId} not found.`);
			}

			const dimensions = this.scaleToMax1080p(
				lookDocument.imageWidth,
				lookDocument.imageHeight
			);

			const heygenService = new HeyGenService(secrets.heygen.apiKey);
			const videoUrl = await heygenService.generateVideo(
				lookDocument.lookId,
				audioUrl,
				dimensions
			);

			jobService.updateJobStatus(job._id.toString(), JobsStatus.RUNNING, "uploading video");
			const tempFile = await this.uploadVideoToBucket(videoUrl);

			await this.encodeVideo(job, tempFile);
		} catch (error: unknown) {
			const errorMessage = error instanceof Error ? error.message : "Unknown error";
			const jobService = new JobService(this.session);
			await jobService.updateJobStatus(jobId, JobsStatus.FAILED, "Job processing failed: " + errorMessage);
			throw error;
		}
	}

	private scaleToMax1080p(width: number, height: number): HeyGenVideoDimensions {
		const isPortrait = height > width;
		const maxWidth = isPortrait ? 1080 : 1920;
		const maxHeight = isPortrait ? 1920 : 1080;

		const widthRatio = maxWidth / width;
		const heightRatio = maxHeight / height;
		const scale = Math.min(1, widthRatio, heightRatio);

		let scaledWidth = Math.round(width * scale);
		let scaledHeight = Math.round(height * scale);

		// Ensure even dimensions
		if (scaledWidth % 2 !== 0) scaledWidth -= 1;
		if (scaledHeight % 2 !== 0) scaledHeight -= 1;

		return {
			width: scaledWidth,
			height: scaledHeight
		};
	}

	private validateJobParameters(job: Job): AvatarJob {
		if (job.type !== JobsType.AVATAR_VIDEO) {
			throw new Error(`Job type ${job.type} is not supported for Avatar processing.`);
		}
		if (!job.avatarPost?.lookId) {
			throw new Error("Job does not have the required parameters: avatarPost.lookId");
		}
		if (!job.avatarPost?.voiceId) {
			throw new Error("Job does not have the required parameters: avatarPost.voiceId");
		}
		if (!job.avatarPost?.text) {
			throw new Error("Job does not have the required parameters: avatarPost.text");
		}
		if (!job.accountId) {
			throw new Error("Job does not have the required parameters: accountId");
		}

		const avatarJob: AvatarJob = {
			jobId: job._id.toString(),
			text: job.avatarPost.text,
			lookId: job.avatarPost.lookId,
			voiceId: job.avatarPost.voiceId,
			callbackInfo: job.callbackInfo
		};

		return avatarJob;
	}

	private async encodeVideo(job: Job, tempFile: string): Promise<void> {
		job.tempFilename = tempFile;
		job.progressPercent = 0;
		job.type = JobsType.ENCODE_VIDEO;
		job.statusMessage = "setting up video encoding job";

		const jobModel = new JobModel();
		await jobModel.collection.updateOne(
			{ _id: job._id },
			{
				$set: {
					tempFilename: job.tempFilename,
					status: JobsStatus.CREATED,
					statusMessage: job.statusMessage,
					progressPercent: job.progressPercent,
					type: job.type
				}
			},
			{
				session: this.session ?? undefined
			}
		);

		const jobService = new JobService(this.session);
		jobService.updateJobStatus(job._id.toString(), JobsStatus.RUNNING, "encoding video");
		const videoEncodeModel = new VideoEncodeModel(job);
		await videoEncodeModel.startEncoder();
	}

	private async uploadVideoToBucket(videoUrl: string): Promise<string> {
		const secrets = await getSecrets();
		const bucketName = secrets.storage.tempBucketName;
		const gcpStorageProvider = new GCPStorageProvider(
			`https://storage.googleapis.com/${bucketName}`,
			bucketName,
			secrets.storage.host,
			secrets.storage.credentials
		);

		const storageService = new StorageService(gcpStorageProvider);
		const cacheControl: StorageCacheControl = {
			maxAge: 31536000,
			public: true
		};
		const randomName = storageService.createRandomFileName(".mp4");
		const storagePath = `video/${randomName}`;
		await storageService.writeFromUrl(
			storagePath,
			videoUrl,
			cacheControl
		);

		return storagePath;
	}

	private async uploadAudioToBucket(audioBuffer: Buffer): Promise<string> {
		const secrets = await getSecrets();
		const bucketName = secrets.storage.tempBucketName;
		const gcpStorageProvider = new GCPStorageProvider(
			`https://storage.googleapis.com/${bucketName}`,
			bucketName,
			secrets.storage.host,
			secrets.storage.credentials
		);

		const storageService = new StorageService(gcpStorageProvider);
		const cacheControl: StorageCacheControl = {
			maxAge: 31536000,
			public: true
		};

		const filename = storageService.createRandomFileName(".mp3");

		const storagePath = await storageService.writeFromBuffer(
			`audio/${filename}`,
			audioBuffer,
			cacheControl
		);

		return storageService.getUrl(storagePath);
	}
}
