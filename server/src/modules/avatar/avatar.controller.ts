import express, {
	Request,
	Response
} from "express";
import { Controller } from "../base/base.controller";
import { APIError } from "../../utils/helpers/apiError";
import { APIErrorName } from "../../interfaces/apiTypes";
import { AvatarService } from "./avatar.service";
import {
	AvatarPostSchema,
	AvatarPost
} from "./avatar.model";

export class AvatarController extends Controller {
	constructor () {
		super();

		this.router.post(
			"/",
			[express.json({ limit: "2MB" })],
			async (request: Request, response: Response) => {
				try {
					const payload = await this.validatePostPayload(request);

					await this.verifyAccessToken(payload.accessToken);
					const accountToken = await this.verifyAccountToken(payload.accountToken);

					const avatarService = new AvatarService(response.locals.session);
					const job = await avatarService.createJob(payload, accountToken);
					return response.status(202).send({ jobId: job._id });
				} catch (error: unknown) {
					return APIError.fromUnknownError(error).log().setResponse(response);
				}
			}
		);
	}

	private async validatePostPayload(request: Request): Promise<AvatarPost> {
		try {
			return await AvatarPostSchema.validateAsync({
				apiVersion: request.headers["x-api-version"],
				accessToken: request.headers.authorization,
				accountToken: request.headers["x-account-token"],
				callbackInfo: {
					callbackUrl: request.body.callbackUrl,
					callbackData: request.body.callbackData
				},
				lookId: request.body.lookId,
				voiceId: request.body.voiceId,
				text: request.body.text
			});
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, error);
		}
	}
}
