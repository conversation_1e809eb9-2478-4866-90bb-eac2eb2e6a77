import Jo<PERSON> from "joi";
import { <PERSON><PERSON><PERSON> } from "../base/base.joi";
import { BaseRequest } from "../base/base.interfaces";
import { CallbackInfo } from "../job/job.model";

export interface AvatarPost extends BaseRequest {
	lookId: string;
	voiceId: string;
	text: string;
	callbackInfo: CallbackInfo;
}

export interface AvatarJob {
	jobId: string;
	text: string;
	lookId: string;
	voiceId: string;
	callbackInfo: CallbackInfo | undefined;
}

export const AvatarPostSchema = BaseJoi.append<AvatarPost>({
	lookId: Joi.string().required(),
	voiceId: Joi.string().required(),
	text: Joi.string().min(1).max(1000).required(),
	callbackInfo: Joi.object({
		callbackUrl: Joi.string().uri().optional(),
		callbackData: Joi.string().optional()
	}).optional()
});
