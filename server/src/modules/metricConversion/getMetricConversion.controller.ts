import {
	Request,
	Response
} from "express";
import { APIError } from "../../utils/helpers/apiError";
import mongoose from "mongoose";
import { AccountModel } from "../account/account.model";
import { IGetMetricPayload } from "../metrics/metrics.interface";
import { APIErrorName } from "../../interfaces/apiTypes";
import { aggregateMetricConversion } from "../../services/mongodb/metricConversion.service";
import { aggregateMetricUserEngagement } from "../../services/mongodb/metricUserEngagement.service";

export const getMetricConversionController = async (req: Request, res: Response): Promise<Response> => {
	try {
		const payload = req.query as unknown as IGetMetricPayload;
		const accountId = req.accountToken?.account._id;

		if (!accountId) {
			res.status(401);
			throw new APIError(APIErrorName.E_MISSING_AUTHORIZATION, "Missing accountId in account token");
		}

		const startDate = new Date(payload.startDate);
		const endDate = new Date(payload.endDate);

		const { currentMetricConversions, previousMetricConversions } = await fetchCurrentAndPreviousMetricConversions(
			accountId,
			startDate,
			endDate
		);

		return res.status(200).send({ previousMetricConversions, currentMetricConversions });
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

interface IMetricConversion {
	totalOrders: number;
	averageBasketSize: number;
	userEngagementSessions: number;
	userConversionRate: number;
}

const fetchCurrentAndPreviousMetricConversions = async (
	accountId: string,
	startDate: Date,
	endDate: Date
): Promise<{ currentMetricConversions: IMetricConversion; previousMetricConversions: IMetricConversion }> => {
	const delta = endDate.getTime() - startDate.getTime();
	const prevStartDate = new Date(startDate.getTime() - delta);
	const prevEndDate = new Date(endDate.getTime() - delta);

	const [currentMetricConversions, previousMetricConversions] = await Promise.all([
		fetchMetricConversions(accountId, startDate, endDate),
		fetchMetricConversions(accountId, prevStartDate, prevEndDate)
	]);

	return { currentMetricConversions, previousMetricConversions };
};

const fetchMetricConversions = async (
	accountId: string,
	startDate: Date,
	endDate: Date
): Promise<IMetricConversion> => {
	const accountModel = new AccountModel(null);
	const accountDocument = await accountModel.readOneById(accountId);

	if (accountDocument.subscription.enableConversionMetrics !== true) {
		return {
			totalOrders: 0,
			averageBasketSize: 0,
			userEngagementSessions: 0,
			userConversionRate: 0
		};
	}

	const accountIdObj = new mongoose.Types.ObjectId(accountId);

	const conversionsMatchQuery = {
		accountId: accountIdObj,
		createdAt: { $gte: startDate, $lte: endDate }
	};

	const conversionsAggregation = [
		{ $match: conversionsMatchQuery },
		{
			$group: {
				_id: null,
				totalOrders: { $sum: 1 },
				totalItemsSold: { $sum: "$orderItemsCount" }
			}
		}
	];

	const conversionMetrics = await aggregateMetricConversion(conversionsAggregation, null);

	const engagementMatchQuery = {
		accountId: accountIdObj,
		createdAt: { $gte: startDate, $lte: endDate }
	};

	const engagementAggregation = [
		{ $match: engagementMatchQuery },
		{ $group: { _id: "$userSessionId" } },
		{ $count: "uniqueSessions" }
	];

	const engagementMetrics = await aggregateMetricUserEngagement(engagementAggregation, null);

	if (!conversionMetrics || !engagementMetrics) {
		throw new APIError(
			APIErrorName.E_SERVICE_FAILED,
			"Failed to read conversion metrics."
		);
	}

	const totalOrders = conversionMetrics[0]?.totalOrders || 0;
	const totalItemsSold = conversionMetrics[0]?.totalItemsSold || 0;
	const averageBasketSize = totalOrders > 0 ? totalItemsSold / totalOrders : 0;
	const userEngagementSessions = engagementMetrics[0]?.uniqueSessions || 0;
	const userConversionRate = userEngagementSessions > 0 ? (totalOrders / userEngagementSessions) * 100 : 0;

	return {
		totalOrders,
		averageBasketSize,
		userEngagementSessions,
		userConversionRate
	};
};



