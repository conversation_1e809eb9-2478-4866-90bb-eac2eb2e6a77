import {
	Storage,
	GetSignedUrlConfig,
	Bucket,
	UploadOptions
} from "@google-cloud/storage";
import {
	getSecrets,
	ISecrets
} from "../secrets/secrets.model";
import mongoose from "mongoose";
import path from "path";
import { validateBackslash } from "../../utils/helpers/gp.helper";
import { APIErrorName } from "../../interfaces/apiTypes";
import { APIError } from "../../utils/helpers/apiError";

import stream from "stream";

import { promisify } from "util";
import fs, {
	createReadStream,
	promises as fsPromises
} from "fs";
import {
	IFileInfo,
	saveFileURL
} from "../../utils/helpers/file.helper";
import os from "os";

export class BucketModel {
	private storage: Storage | null = null;
	public bucketName: string;
	public storageHost = "";

	constructor(bucketName: string, public apiEndpoint?: string, public credentials?: any) {
		this.bucketName = bucketName;
	}

	private async initStorage(): Promise<Storage> {
		if (!this.storage) {
			const secrets: ISecrets = await getSecrets();

			if (!this.apiEndpoint) {
				this.apiEndpoint = secrets.storage.host;
			}

			this.storage = new Storage({ apiEndpoint: this.apiEndpoint, credentials: this.credentials });
			this.storageHost = validateBackslash(secrets.storage.host);
		}
		return this.storage;
	}

	private async getBucket(): Promise<Bucket> {
		const storage = await this.initStorage();
		const bucket = storage.bucket(this.bucketName);

		const bucketExists: boolean[] = await bucket.exists();
		if (!bucketExists[0]) {
			throw new APIError(APIErrorName.E_SERVICE_FAILED, `Failed to find GCP Storage Bucket: ${this.bucketName}`);
		}
		return bucket;
	}


	async getSignedUrlForUpload(getSignedUrlConfig: GetSignedUrlConfig, fileDestination:string):
	Promise<string> {
		try {
			const bucket = await this.getBucket();
			const [signedURL] = await bucket.file(fileDestination).getSignedUrl(getSignedUrlConfig);
			return signedURL;
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_SERVICE_FAILED,
				`getTempSignedUrlForUpload failed: ${(error as Error).message}`);
		}
	}

	public static getMaxFileNameLength(): number {
		/*
		The maximum length of a file name allowed is 255 characters in most file systems.

		A GCP Storage Bucket allows for 1024 characters in a file name.
		A GCP Storage Bucket path must also be allocated in the 1024 character limit.

		Fouza/fake-gcs-server uses google renameio which appends up to 19 characters to the end of the filename.
		It also prefixes the filename with a dot (.)
		This adds up to an additional 20 characters to the filename.
		We subtract 20 characters from the 255 limit to account for this.

		renameio functionality is here: https://github.com/google/renameio/blob/master/tempfile.go ->
		name := prefix + strconv.FormatInt(nextrandom(), 10)

		By using a 235 limit, we maintain compatibility on GCP buckets tied to a local file system.
		A GCP Storage Bucket path will have a minimum of 789 characters for the path. (789 + 235) = 1024.

		Do not adjust this value unless you know what you are doing.
		*/

		return 235;
	}

	public static sanitizeFileName(fileName: string, suffix: string): string {
		const sanitizedFilename = fileName.replace(/[^a-zA-Z0-9\-_.~]/g, "");
		const parsedPath = path.parse(sanitizedFilename);

		const baseSubtraction = suffix.length + parsedPath.ext.length;

		const maxAllowedBaseLength = BucketModel.getMaxFileNameLength() - baseSubtraction;

		const truncatedBaseFileName = parsedPath.name.slice(0, maxAllowedBaseLength);

		const formattedFilename = `${truncatedBaseFileName}${suffix}${parsedPath.ext}`;

		return formattedFilename;
	}

	async getFileSize(fileLocation: string): Promise<number> {
		try {
			const bucket = await this.getBucket();
			const file = bucket.file(fileLocation);
			const [metadata] = await file.getMetadata();
			const size = Number(metadata.size);
			if (isNaN(size)) {
				throw new APIError(APIErrorName.E_SERVICE_FAILED, "Invalid file size.");
			}
			return size;
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_SERVICE_FAILED, `getFileSize failed: ${(error as Error).message}`);
		}
	}

	async fetchFileFromBucket(fileLocation: string): Promise<IFileInfo> {
		try {
			const bucket = await this.getBucket();
			const file = bucket.file(fileLocation);
			const tempDirectory = os.tmpdir();
			const tempFileName = path.basename(fileLocation);
			const tempFilePath = path.join(tempDirectory, tempFileName);

			const fileStream = file.createReadStream();
			const writeStream = fs.createWriteStream(tempFilePath);
			const pipeline = promisify(stream.pipeline);
			await pipeline(fileStream, writeStream);

			return {
				filePath: tempFilePath,
				fileName: path.basename(fileLocation, path.extname(fileLocation)),
				fileExt: path.extname(fileLocation)
			};
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_SERVICE_FAILED,
				`fetchFileFromBucket failed: ${(error as Error).message}`);
		}
	}

	async uploadFileToBucketFromURL(fileUrl: string, contentType: string, cacheControl: string): Promise<string> {
		try {
			const suffix = `-${new mongoose.Types.ObjectId().toString()}`;
			const fileInfo = await saveFileURL(fileUrl, suffix);

			const fileLocation = BucketModel.sanitizeFileName(fileInfo.fileName, "");
			const fileStream = createReadStream(fileInfo.filePath);
			const bucket = await this.getBucket();
			const uploadOptions: UploadOptions = {
				destination: fileLocation,
				metadata: {
					cacheControl: cacheControl,
					contentType: contentType
				}
			};

			const blob = bucket.file(fileLocation, uploadOptions);
			const blobStream = blob.createWriteStream({ resumable: false });
			await new Promise<void>((resolve, reject) => {
				fileStream
					.pipe(blobStream)
					.on("error", err => reject(err))
					.on("finish", resolve);
			});
			await fsPromises.unlink(fileInfo.filePath);
			return fileLocation;
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_SERVICE_FAILED,
				"uploadFileToBucketFromURL failed")
				.setDetail({
					"error": JSON.stringify(error),
					"fileUrl": fileUrl,
					"os.tmpdir()": os.tmpdir()
				});
		}
	}

	async uploadFileToBucketFromStream(
		tempPath: string,
		contentType: string,
		fileLocation: string,
		cacheControl: string
	): Promise<void> {
		try {
			const fileStream = createReadStream(tempPath);
			const bucket = await this.getBucket();
			const uploadOptions: UploadOptions = {
				destination: fileLocation,
				metadata: {
					cacheControl: cacheControl,
					contentType: contentType
				}
			};

			const blob = bucket.file(fileLocation, uploadOptions);
			const blobStream = blob.createWriteStream({ resumable: false });
			await new Promise<void>((resolve, reject) => {
				fileStream
					.pipe(blobStream)
					.on("error", err => reject(err))
					.on("finish", resolve);
			});
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_SERVICE_FAILED,
				`uploadFileToBucketFromStream failed: ${(error as Error).message}`);
		}
	}
}
