import { APIErrorName } from "../../interfaces/apiTypes";
import { APIError } from "../../utils/helpers/apiError";
import { validateBackslash } from "../../utils/helpers/gp.helper";
import {
	getSecrets,
	ISecrets
} from "../secrets/secrets.model";

export enum EmailDisposition {
	ATTACHMENT = "attachment"
}

export enum EmailType {
	APPLICATION_PDF = "application/pdf"
}

export interface EmailAttachment {
	filename: string;
	type: EmailType;
	content: string;
	disposition?: EmailDisposition;
}

export interface EmailInput {
	to: string | string[];
	subject: string;
	html: string;
	attachments?: EmailAttachment[];
	fromName?: string;
}

// Update SendGridEmailData to allow name on from
export interface SendGridEmailData {
	personalizations: Array<{
		to: Array<{ email: string }>;
		subject: string;
	}>;
	from: { email: string; name?: string };
	content: Array<{ type: string; value: string }>;
	attachments?: EmailAttachment[];
}

export class SendGridModel {
	private readonly sendGridPath: string = "v3/mail/send";

	public async send(input: EmailInput): Promise<void> {
		const secrets = await getSecrets();
		const emailData = this.buildSendGridEmailData(input, secrets.sendGrid.fromAddress);
		await this.callSendGrid(emailData, secrets);
	}

	private buildSendGridEmailData(input: EmailInput, fromAddress: string): SendGridEmailData {
		const from = input.fromName
			? { email: `${input.fromName} <${fromAddress}>` }
			: { email: fromAddress };

		return {
			personalizations: [
				{
					to: Array.isArray(input.to)
						? input.to.map(email => ({ email }))
						: [{ email: input.to }],
					subject: input.subject
				}
			],
			from,
			content: [
				{
					type: "text/html",
					value: input.html
				}
			],
			...(input.attachments ? { attachments: input.attachments } : {})
		};
	}

	private async callSendGrid(emailData: SendGridEmailData, secrets: ISecrets): Promise<void> {
		const sendGridHost = this.getSendGridHost(secrets);

		try {
			const response = await fetch(`${sendGridHost}${this.sendGridPath}`, {
				method: "POST",
				headers: {
					Authorization: `Bearer ${secrets.sendGrid.apiKey}`,
					"Content-Type": "application/json"
				},
				body: JSON.stringify(emailData)
			});

			if (!response.ok) {
				throw new APIError(APIErrorName.E_SERVICE_FAILED,
					`sendEmail response is not ok, status=${response.status}`);
			}

		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_SERVICE_FAILED,
				`Failed to send email. ${(error as Error).message}`);
		}
	}

	private getSendGridHost(secrets: ISecrets): string {
		return validateBackslash(secrets.sendGrid.host);
	}
}
