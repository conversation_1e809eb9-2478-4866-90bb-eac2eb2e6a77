import {
	Request,
	Response
} from "express";
import { IAccount } from "../../modules/account/account.interfaces";
import { UserCreateOneInput } from "../../modules/user/user.interfaces";
import { UserModel } from "../../modules/user/user.model";
import {
	APIErrorName,
	AdminLinkType
} from "../../interfaces/apiTypes";
import { ISignupPayload } from "../../modules/signup/signup.interfaces";
import {
	getSecrets,
	ISecrets
} from "../../modules/secrets/secrets.model";
import {
	startDBTransaction,
	completeDBTransaction,
	cancelDBTransaction
} from "../../services/mongodb/transaction.service";
import { APIError } from "../../utils/helpers/apiError";
import {
	processInvitationToken,
	sendEmailLink,
	handleAccountCreation
} from "../../utils/helpers/signup.helper";
import {
	createAccessToken,
	createRefreshToken
} from "../../utils/tokens";
import { EmailPasswordAuthentication } from "../authentication/emailpassword.authentication.model";
import { IAuthenticationOptions } from "../authentication/authentication.interface";
import { InvitationStatus } from "../../modules/invitation/invitation.enum";
import { updateInvitation } from "../../services/mongodb/invitations.service";

export const signupController = async (
	req: Request,
	res: Response
): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		const secrets: ISecrets = await getSecrets();
		const signupPayload = req.body as ISignupPayload;

		let accountDocument: IAccount | null = null;
		let verified = false;
		let invitationId = "";

		await startDBTransaction(res.locals.session);

		if (signupPayload.inviteToken) {
			const invitationResult = await processInvitationToken(
				signupPayload.inviteToken,
				signupPayload.email,
				secrets.hashkey.key,
				res.locals.session
			);
			accountDocument = invitationResult.accountDocument;
			verified = invitationResult.verified;
			invitationId = invitationResult.invitationId;
		}

		const createData: UserCreateOneInput = {
			firstName: signupPayload.firstName ?? "",
			lastName: signupPayload.lastName ?? "",
			email: signupPayload.email,
			postSignupCompleted: false,
			isPasswordSet: true,
			maxCompanies: signupPayload.maxCompanies ?? 1
		};

		const userModel = new UserModel(res.locals.session);
		const userDocument = await userModel.createOne(createData);

		// if there wasn't an accountDocument read due to an invitation, create one
		if (!accountDocument) {
			accountDocument = await handleAccountCreation(
				userDocument,
				res.locals.session,
				signupPayload.companyName
			);
		}

		const options: IAuthenticationOptions = {
			verified: verified,
			legalAgreement: signupPayload.legalAgreement
		};

		const authenticationModel = new EmailPasswordAuthentication(res.locals.session);
		const authenticationDocument = await authenticationModel.createOne(
			userDocument._id.toString(),
			accountDocument._id.toString(),
			signupPayload.password,
			options
		);

		if (invitationId && verified) {
			await updateInvitation({
				_id: invitationId,
				email: signupPayload.email
			}, {
				status: InvitationStatus.ACCEPTED,
				userId: accountDocument.ownerUserId.toString()
			}, res.locals.session);
		}

		if (!verified) {
			await sendEmailLink(
				{
					email: signupPayload.email,
					callbackEndpoint: signupPayload.callbackEndpoint,
					locale: signupPayload.locale,
					linkType: AdminLinkType.VERIFY,
					template: "verify-email",
					subject: "Verify Your Account"
				},
				authenticationDocument,
				secrets.hashkey.key
			);
		}

		const accessToken = await createAccessToken(
			authenticationDocument,
			userDocument
		);
		const refreshToken = await createRefreshToken(authenticationDocument);

		const responseData = {
			accessToken: accessToken,
			refreshToken: refreshToken
		};

		await completeDBTransaction(res.locals.session);
		return res.status(200).json(responseData);
	} catch (error: unknown) {
		await cancelDBTransaction(res.locals.session);
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};
