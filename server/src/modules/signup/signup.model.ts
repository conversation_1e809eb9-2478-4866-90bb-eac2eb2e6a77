import { ClientSession } from "mongoose";
import {
	SignUpEmailData,
	SignUpResponse
} from "./signup.interfaces";
import { UserModel } from "../user/user.model";
import {
	IUser,
	UserCreateOneInput
} from "../user/user.interfaces";
import { AccountModel } from "../account/account.model";
import { IAccount } from "../account/account.interfaces";
import { InteractiveCollectionModel } from "../interactiveCollection/interactiveCollection.model";
import { sendEmailLink } from "../../utils/helpers/signup.helper";
import { AdminLinkType } from "../../interfaces/apiTypes";
import { getSecrets } from "../secrets/secrets.model";
import { InvitationModel } from "../invitation/invitation.model";
import {
	IAuthentication,
	IAuthenticationOptions
} from "../authentication/authentication.interface";
import { EmailPasswordAuthentication } from "../authentication/emailpassword.authentication.model";
import { OIDCAuthentication } from "../authentication/oidc.authentication.model";

interface OIDCSignUpData {
	email: string;
	sub: string;
	inviteToken?: string;
}

export class SignUpModel {
	private session: ClientSession | null;

	constructor (session: ClientSession | null) {
		this.session = session;
	}

	private async createUserFromEmail(userData: { email: string }): Promise<IUser> {
		const userCreateData: UserCreateOneInput = {
			firstName: "",
			lastName: "",
			email: userData.email,
			postSignupCompleted: false,
			isPasswordSet: false,
			maxCompanies: 1
		};

		const userModel = new UserModel(this.session);
		return await userModel.createOne(userCreateData);
	}

	private async createAccount(userDocument: IUser, companyName = ""): Promise<IAccount> {
		const accountModel = new AccountModel(this.session);
		return await accountModel.create({
			companyName: companyName,
			companyURL: "",
			user: userDocument
		});
	}

	private async createDefaultCollection(accountDocument: IAccount): Promise<void> {
		const interactiveCollectionModel = new InteractiveCollectionModel(this.session);
		const shoppableCollectionDocument = await interactiveCollectionModel.createOne({
			title: "Default",
			shoppableVideos: []
		}, accountDocument);

		shoppableCollectionDocument.throwIfError();

		const accountModel = new AccountModel(this.session);
		await accountModel.updateOneById(accountDocument._id.toString(), {
			defaultCollectionId: shoppableCollectionDocument.getData()._id
		});
	}

	private async sendVerificationEmail
	(userDocument: IUser, authenticationDocument: IAuthentication, signupData: SignUpEmailData): Promise<void> {
		const secrets = await getSecrets();

		await sendEmailLink(
			{
				email: userDocument.email,
				callbackEndpoint: signupData.callbackEndpoint,
				locale: signupData.locale,
				linkType: AdminLinkType.VERIFY,
				template: "sign-up-email",
				subject: "Sign In to Your Account"
			},
			authenticationDocument,
			secrets.hashkey.key
		);
	}

	public async createFromEmail (signupCreateData: SignUpEmailData): Promise<SignUpResponse> {
		const userDocument = await this.createUserFromEmail({
			email: signupCreateData.email
		});

		const accountDocument = await this.createAccount(userDocument, signupCreateData.companyName);

		const options: IAuthenticationOptions = {
			verified: false,
			legalAgreement: true
		};

		const authenticationModel = new EmailPasswordAuthentication(this.session);
		const authenticationDocument = await authenticationModel.createOne(
			userDocument._id.toString(),
			accountDocument._id.toString(),
			signupCreateData.email,
			options);

		await this.createDefaultCollection(accountDocument);

		await this.sendVerificationEmail(userDocument, authenticationDocument, signupCreateData);

		return {
			userDoc: userDocument,
			authenticationDoc: authenticationDocument
		};
	}

	public async createFromOIDC(oidcSignUpData: OIDCSignUpData): Promise<SignUpResponse> {
		let accountDocument: IAccount | null = null;

		if (oidcSignUpData.inviteToken) {
			const invitationModel = new InvitationModel(this.session);
			accountDocument =
			await invitationModel.verifyInvitationToken(oidcSignUpData.inviteToken, oidcSignUpData.email);
		}

		const userDocument = await this.createUserFromEmail({
			email: oidcSignUpData.email
		});

		if (!accountDocument) {
			accountDocument = await this.createAccount(userDocument);
		}

		const options: IAuthenticationOptions = {
			verified: false,
			legalAgreement: true
		};

		const authenticationModel = new OIDCAuthentication(this.session);
		const authenticationDocument = await authenticationModel.createOne(
			userDocument._id.toString(),
			accountDocument._id.toString(),
			oidcSignUpData.sub,
			options);

		await this.createDefaultCollection(accountDocument);

		return {
			userDoc: userDocument,
			authenticationDoc: authenticationDocument
		};
	}
}
