import { IAuthentication } from "../authentication/authentication.interface";
import { LocaleAPI } from "../../interfaces/apiTypes";
import { IUser } from "../user/user.interfaces";
import { UserTeam } from "../user/user.enums";
import { AccountPlatform } from "../account/account.enum";

export interface SignUpEmailData {
	email: string;
	callbackEndpoint: string;
	locale: LocaleAPI;
	companyName?: string;
}

export interface SignUpResponse {
	userDoc: IUser;
	authenticationDoc: IAuthentication;
}

export interface ISignupPayload {
	email: string;
	firstName: string;
	lastName: string;
	password: string;
	companyName: string;
	locale: LocaleAPI;
	inviteToken?: string;
	callbackEndpoint: string;
	legalAgreement: boolean;
	maxCompanies?: number;
}

export interface ISignupEmailPayload {
	email: string;
	locale: LocaleAPI;
	callbackEndpoint: string;
}

export interface IPostSignupPayload {
	name: string;
	team: UserTeam;
	companyName: string;
	platform: AccountPlatform;
}
