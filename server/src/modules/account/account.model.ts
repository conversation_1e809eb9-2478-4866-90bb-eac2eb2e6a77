import mongoose, {
	ClientSession,
	FilterQuery
} from "mongoose";
import {
	IAccount,
	AccountCreateData
} from "./account.interfaces";
import { AccountDBModel } from "./accountDB.model";
import { APIError } from "../../utils/helpers/apiError";
import {
	APIErrorName,
	SearchType
} from "../../interfaces/apiTypes";
import Stripe from "stripe";
import { AccountManifestModel } from "./manifest/account.manifest.model";
import { UserModel } from "../user/user.model";
import { IUser } from "../user/user.interfaces";
import { StripeCustomerModel } from "../stripe/customer/customer.model";
import { StripeSubscriptionModel } from "../stripe/subscription/subscription.model";
import { StripeInvoicesModel } from "../stripe/invoice/invoice.model";
import { SubscriptionModel } from "../subscription/subscription.model";
import { StripePriceModel } from "../stripe/price/price.model";
import { AuthenticationModel } from "../authentication/authentication.model";
import { VideoProfileModel } from "../videoProfile/videoProfile.model";

export class AccountModel {
	private session: ClientSession | null;

	constructor (session: ClientSession | null) {
		this.session = session;
	}

	async countDocuments (filter: FilterQuery<IAccount>): Promise<number> {
		const count: number = await AccountDBModel.countDocuments(filter).session(
			this.session
		);

		return count;
	}

	async readOneById (_id: string): Promise<IAccount> {
		const filter: FilterQuery<IAccount> = {
			_id: new mongoose.Types.ObjectId(_id)
		};

		const document: IAccount | null = await AccountDBModel.findOne(filter).session(
			this.session
		);

		if (!document) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Account not found")
				.setDetail({
					_id
				});
		}

		return document;
	}

	async readOneByOwnerId (ownerId: string): Promise<IAccount> {
		try {
			const filter: FilterQuery<IAccount> = {
				ownerUserId: new mongoose.Types.ObjectId(ownerId)
			};

			const document: IAccount | null = await AccountDBModel.findOne(filter).session(
				this.session
			);

			if (!document) {
				throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Account not found")
					.setDetail({
						ownerUserId: ownerId
					});
			}

			return document;
		} catch (error: unknown) {
			throw APIError.fromUnknownError(error);
		}
	}

	async readOneByStripeCustomer (stripeCustomerId: string): Promise<IAccount> {
		const filter: FilterQuery<IAccount> = {
			stripeCustomerId: stripeCustomerId
		};

		const document: IAccount | null = await AccountDBModel.findOne(filter).session(
			this.session
		);

		if (!document) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Account not found")
				.setDetail({
					stripeCustomerId
				}).suppressLog();
		}

		return document;
	}

	async updateOneById (_id: string, update: mongoose.UpdateQuery<IAccount>): Promise<IAccount> {
		const filter: FilterQuery<IAccount> = {
			_id: new mongoose.Types.ObjectId(_id)
		};

		const previousDocument = await AccountDBModel.findOne(filter).session(this.session);

		if (!previousDocument) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Account not found.")
				.setDetail({
					_id
				});
		}

		const updatedDocument = await AccountDBModel.findOneAndUpdate(filter, update, {
			session: this.session,
			new: true
		});

		if (!updatedDocument) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Account not found.");
		}

		const accountManifest = new AccountManifestModel(updatedDocument);
		await accountManifest.update();

		return updatedDocument;
	}

	public static createCompanyNameFromEmail (email: string): string {
		const [username, domain] = email.split("@");
		const domainParts = domain.split(".");
		domainParts.pop();
		return `${username}-${domainParts.join(".")}`;
	}

	public async create (createData: AccountCreateData): Promise<IAccount> {
		if (!createData.companyName) {
			createData.companyName = AccountModel.createCompanyNameFromEmail(createData.user.email);
		}

		const stripePriceModel = new StripePriceModel();
		const freePrice = await stripePriceModel.getFreePrice();

		const stripeCustomerModel = new StripeCustomerModel();
		const stripeCustomer = await stripeCustomerModel.createCustomer(
			createData.user.email,
			createData.companyName,
			true
		);

		const stripeSubscriptionModel = new StripeSubscriptionModel();
		const subscription = await stripeSubscriptionModel.createSubscription(stripeCustomer.id, freePrice);

		const stripeInvoicesModel = new StripeInvoicesModel();
		const invoices = await stripeInvoicesModel.getInvoices(stripeCustomer.id, "paid");
		const videoProfileModel = new VideoProfileModel(this.session);
		const defaultProfile = await videoProfileModel.readDefault();

		const insert: Partial<IAccount> = {
			ownerUserId: createData.user._id,
			companyName: createData.companyName,
			stripeCustomerId: stripeCustomer.id,
			videoProfile: defaultProfile._id
		};

		const model = new AccountDBModel(insert);

		let accountDocument = await model.save({ session: this.session });

		await stripeCustomerModel.setAccount(accountDocument);

		const accountModel = new AccountModel(this.session);
		accountDocument = await accountModel.setSubscription(accountDocument._id.toString(), subscription.id);

		accountDocument = await accountModel.setInvoices(accountDocument._id.toString(), invoices);

		const accountManifestModel = new AccountManifestModel(accountDocument);
		await accountManifestModel.update();

		return accountDocument;
	}

	public async getOwnerUser (account: IAccount): Promise<IUser> {
		const userModel = new UserModel(this.session);
		const user = await userModel.readOneById(account.ownerUserId.toString());

		return user;
	}

	public async setSubscription (accountId: string, stripeSubscriptionId: string): Promise<IAccount> {
		const subscriptionModel = new SubscriptionModel();
		const subscription = await subscriptionModel.fromStripeSubscription(stripeSubscriptionId);

		const update: Partial<IAccount> = {
			subscription: subscription
		};

		return await this.updateOneById(accountId, update);
	}

	public async setInvoices (accountId: string, invoices: Stripe.Invoice[]): Promise<IAccount> {
		const update: Partial<IAccount> = {
			invoices: invoices.map((invoice) => ({
				id: invoice.id,
				amountDue: invoice.amount_due,
				created: invoice.created,
				currency: invoice.currency,
				dueDate: invoice.due_date ?? 0,
				periodEnd: invoice.period_end,
				periodStart: invoice.period_start,
				status: invoice.status ?? "",
				pdfInvoiceURL: invoice.invoice_pdf ?? "",
				hostedInvoiceURL: invoice.hosted_invoice_url ?? ""
			}))
		};

		return await this.updateOneById(accountId, update);
	}

	public async resetUsage (accountId: string): Promise<IAccount> {
		return this.setUsage(accountId, 0);
	}

	public async setUsage (accountId: string, totalImpressions: number): Promise<IAccount> {
		const update: Partial<IAccount> = {
			totalImpressionsCurrentCycle: totalImpressions
		};

		return await this.updateOneById(accountId, update);
	}

	public isImpressionLimitReached (account: IAccount): boolean {
		const currentImpressions = account.totalImpressionsCurrentCycle ?? 0;
		const maxImpressions = account.subscription.maxImpressionsPerCycle;
		return currentImpressions >= maxImpressions;
	}

	public async deleteOneById (_id: string): Promise<void> {
		this.session?.startTransaction();
		try {
			const account = await this.readOneById(_id);
			const accountManifestModel = new AccountManifestModel(account);
			accountManifestModel.delete();

			const authModel = new AuthenticationModel(this.session);
			await authModel.detachManyByAccountId(_id);

			await AccountDBModel.deleteOne({ _id: _id }).session(this.session);

			if (account.stripeCustomerId) {
				const stripeCustomerModel = new StripeCustomerModel();
				await stripeCustomerModel.deleteCustomer(account.stripeCustomerId);
			}
		} catch (error: unknown) {
			this.session?.abortTransaction();
			throw error;
		} finally {
			this.session?.commitTransaction();
		}
	}

	public static async isOwner (accountId: string, userId: string): Promise<boolean> {
		const filter: FilterQuery<IAccount> = {
			_id: new mongoose.Types.ObjectId(accountId),
			ownerUserId: new mongoose.Types.ObjectId(userId)
		};

		const account = await AccountDBModel.findOne(filter);
		return !!account;
	}

	async incrementImpressionCountWithinLimit (_id: string, maxImpressions: number): Promise<void> {
		const filter: FilterQuery<IAccount> = {
			_id: new mongoose.Types.ObjectId(_id),
			totalImpressionsCurrentCycle: { $lt: maxImpressions }
		};

		const update: mongoose.UpdateQuery<IAccount> = {
			$inc: {
				totalImpressionsCurrentCycle: 1
			}
		};

		const document = await AccountDBModel.findOneAndUpdate(filter, update, {
			session: this.session,
			new: true
		});

		if (!document) {
			throw new APIError(
				APIErrorName.E_IMPRESSION_LIMIT_REACHED,
				"Metric impression limit reached"
			);
		}
	}

	public async readAccountsWithFilter(
		search: string | undefined,
		searchType: SearchType
	): Promise<IAccount[]> {
		let queryCondition: Record<string, unknown> = {};

		const escapeRegex = (str: string): string => str.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
		const safeFilter = search ? escapeRegex(search) : "";

		if (searchType === SearchType.SEARCH_PREFIX && !safeFilter) {
			throw new Error("Search is required for SEARCH_PREFIX");
		}

		switch (searchType) {
			case SearchType.SEARCH_PREFIX:
				// Search for company names starting with a specific letter
				queryCondition = { companyName: { $regex: `^${safeFilter}`, $options: "i" } };
				break;

			case SearchType.SEARCH_ALL_NUMBER:
				// Search for company names starting with a number
				queryCondition = { companyName: { $regex: "^[0-9]", $options: "i" } };
				break;

			case SearchType.SEARCH_ALL_SPECIAL:
				// Search for company names starting with a special character
				queryCondition = { companyName: { $regex: "^[^a-zA-Z0-9]", $options: "i" } };
				break;

			default:
				throw new Error("Invalid SearchType provided.");
		}

		const documents: IAccount[] = await AccountDBModel.find(queryCondition)
			.select("companyName companyLogo")
			.session(this.session);

		return documents;
	}
}
