import mongoose, { Schem<PERSON> } from "mongoose";
import { IAccount } from "./account.interfaces";
import { SubscriptionDefaults } from "../subscription/subscription.interfaces";

const AccountSubscriptionSchema: Schema = new Schema(
	{
		hideVanityBranding: {
			type: Boolean,
			required: true,
			default: SubscriptionDefaults.hideVanityBranding
		},
		type: {
			type: String,
			required: true,
			default: SubscriptionDefaults.type
		},
		enableConversionMetrics: {
			type: Boolean,
			required: true,
			default: SubscriptionDefaults.enableConversionMetrics
		},
		enableEngagementMetrics: {
			type: Boolean,
			required: true,
			default: SubscriptionDefaults.enableEngagementMetrics
		},
		maxClicksMetricPerMonth: {
			type: Number,
			required: true,
			default: SubscriptionDefaults.maxClicksMetricPerMonth
		},
		maxImpressionsPerCycle: {
			type: Number,
			required: true,
			default: SubscriptionDefaults.maxImpressionsPerCycle
		},
		maxInteractiveCollectionLimit: {
			type: Number,
			required: true,
			default: SubscriptionDefaults.maxInteractiveCollectionLimit
		},
		maxInteractiveVideoLimit: {
			type: Number,
			required: true,
			default: SubscriptionDefaults.maxInteractiveVideoLimit
		},
		maxPlaysMetricPerMonth: {
			type: Number,
			required: true,
			default: SubscriptionDefaults.maxPlaysMetricPerMonth
		},
		maxVideoProductLinksLimit: {
			type: Number,
			required: true,
			default: SubscriptionDefaults.maxVideoProductLinksLimit
		},
		maxUserLimit: {
			type: Number,
			required: true,
			default: SubscriptionDefaults.maxUserLimit
		},
		trialActive: {
			type: Boolean,
			required: true,
			default: SubscriptionDefaults.trialActive
		},
		hasPaymentMethod: {
			type: Boolean,
			required: true,
			default: SubscriptionDefaults.hasPaymentMethod
		},
		firstPaymentDate: {
			type: Number,
			required: false,
			default: SubscriptionDefaults.firstPaymentDate
		},
		lastPaymentDate: {
			type: Number,
			required: false,
			default: SubscriptionDefaults.lastPaymentDate
		},
		stripeSubscriptionId: {
			type: String,
			required: true,
			default: null
		},
		stripeProductId: {
			type: String,
			required: true,
			default: null
		},
		stripePriceId: {
			type: String,
			required: true,
			default: null
		},
		trialAvailable: {
			type: Boolean,
			required: true,
			default: true
		},
		trialStartDate: {
			type: Number,
			required: false,
			default: null
		},
		trialEndDate: {
			type: Number,
			required: false,
			default: null
		},
		trialDaysTotal: {
			type: Number,
			required: false,
			default: null
		},
		pendingChangeDate: {
			type: Number,
			required: false,
			default: null
		},
		pendingChangePriceId: {
			type: String,
			required: false,
			default: null
		},
		pendingChangeProductId: {
			type: String,
			required: false,
			default: null
		},
		nextBillingDate: {
			type: Number,
			required: false,
			default: null
		},
		clockTime: {
			type: Number,
			required: false,
			default: 0
		},
		price: {
			type: Number,
			required: true,
			default: 0
		},
		allowLandscape: {
			type: Boolean,
			required: true,
			default: false
		},
		allowCTALead: {
			type: Boolean,
			required: true,
			default: false
		},
		allowThemes: {
			type: Boolean,
			required: true,
			default: false
		},
		allowSharing: {
			type: Boolean,
			required: true,
			default: false
		},
		allowAvatars: {
			type: Boolean,
			required: true,
			default: false
		},
		allowCaptions: {
			type: Boolean,
			required: true,
			default: true
		},
		allowRecordVideo: {
			type: Boolean,
			required: true,
			default: false
		}
	},
	{
		_id: false
	}
);

export const AccountInvoiceSchema: Schema = new Schema(
	{
		id: {
			type: String,
			required: true
		},
		amountDue: {
			type: Number,
			required: true
		},
		created: {
			type: Number,
			required: true
		},
		currency: {
			type: String,
			required: true
		},
		dueDate: {
			type: Number,
			required: true
		},
		periodEnd: {
			type: Number,
			required: true
		},
		periodStart: {
			type: Number,
			required: true
		},
		status: {
			type: String,
			required: true
		},
		pdfInvoiceURL: {
			type: String,
			required: true
		},
		hostedInvoiceURL: {
			type: String,
			required: true
		}
	},
	{
		_id: false
	}
);

export const AccountThemeSchema: Schema = new Schema(
	{
		primarySiteColor: {
			type: String,
			required: false,
			default: ""
		},
		backgroundColor: {
			type: String,
			required: false,
			default: ""
		},
		primaryTextColor: {
			type: String,
			required: false,
			default: ""
		},
		secondaryTextColor: {
			type: String,
			required: false,
			default: ""
		},
		buttonTextColor: {
			type: String,
			required: false,
			default: ""
		},
		disabledTextColor: {
			type: String,
			required: false,
			default: ""
		},
		contentBoxColor: {
			type: String,
			required: false,
			default: ""
		}
	},
	{
		_id: false
	}
);

export const AccountSchema: Schema = new Schema(
	{
		companyName: {
			type: String,
			required: true
		},
		companyLogo: {
			type: String,
			required: false
		},
		companyURL: {
			type: String,
			required: false
		},
		defaultCollectionId: {
			type: Schema.Types.ObjectId,
			required: false
		},
		ownerUserId: {
			type: Schema.Types.ObjectId,
			required: true
		},
		platform: {
			type: String,
			required: false
		},
		createdAt: {
			type: Number,
			default: () => Date.now()
		},
		updatedAt: {
			type: Number,
			default: () => Date.now()
		},
		stripeCustomerId: {
			type: String,
			required: false,
			default: ""
		},
		dailyMetricsUpdatedAt: {
			type: Number,
			required: false,
			default: null
		},
		totalVideosCreatedDaily: {
			type: Number,
			required: false,
			default: null
		},
		latestSnippetImpressionDateDaily: {
			type: Number,
			required: false,
			default: null
		},
		totalVideoImpressionsDaily: {
			type: Number,
			required: false,
			default: null
		},
		totalCollectionsCreatedDaily: {
			type: Number,
			required: false,
			default: null
		},
		totalVideoPlaysDaily: {
			type: Number,
			required: false,
			default: null
		},
		totalPlaytimeDaily: {
			type: Number,
			required: false,
			default: null
		},
		totalVideoCTAClicksDaily: {
			type: Number,
			required: false,
			default: null
		},
		totalConversionDaily: {
			type: Number,
			required: false,
			default: null
		},
		weeklyMetricsUpdatedAt: {
			type: Number,
			required: false,
			default: null
		},
		totalVideosCreatedWeekly: {
			type: Number,
			required: false,
			default: null
		},
		totalCollectionsCreatedWeekly: {
			type: Number,
			required: false,
			default: null
		},
		totalVideoPlaysWeekly: {
			type: Number,
			required: false,
			default: null
		},
		totalPlaytimeWeekly: {
			type: Number,
			required: false,
			default: null
		},
		totalConversionWeekly: {
			type: Number,
			required: false,
			default: null
		},
		dailySyncUpdatedAt: {
			type: Number,
			required: false,
			default: null
		},
		invoices: {
			type: [AccountInvoiceSchema],
			required: false,
			default: []
		},
		subscription: {
			type: AccountSubscriptionSchema,
			required: false,
			default: null
		},
		totalImpressionsCurrentCycle: {
			type: Number,
			required: true,
			default: 0
		},
		videoProfile: {
			type: Schema.Types.ObjectId,
			required: true
		},
		theme: {
			type: AccountThemeSchema,
			required: false,
			default: () => ({})
		}
	},
	{
		timestamps: true
	}
);

AccountSchema.path("companyName").set(function (value: string) {
	return value ? value.trim() : value;
});

export const AccountDBModel = mongoose.model<IAccount>("Accounts", AccountSchema);
