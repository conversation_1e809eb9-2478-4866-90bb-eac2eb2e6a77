import {
	Permission,
	SearchType
} from "../../interfaces/apiTypes";
import { AccountPlatform } from "./account.enum";
import {
	Document,
	ObjectId
} from "mongoose";
import { IUser } from "../user/user.interfaces";
import { Subscription } from "../subscription/subscription.interfaces";
import {
	BaseRequest,
	BaseAccessRequest
} from "../base/base.interfaces";

export interface AccountInvoice {
	id: string;
	amountDue: number,
	created: number,
	currency: string,
	dueDate: number,
	periodEnd: number,
	periodStart: number,
	status: string,
	pdfInvoiceURL: string,
	hostedInvoiceURL: string
}

export interface IAccountToken {
	account: {
		_id: string;
		companyName: string;
		companyLogo: string;
	};
	authenticationId: string;
	userId: string;
	permissions: Permission[];
	passwordRequiredToWrite: boolean;
}

export interface IPostAccountPayload {
	companyName: string;
	companyURL: string;
}

export interface IPatchAccountPayload {
	companyName: string;
	companyURL: string;
	platform: AccountPlatform;
	videoProfile: string;
}

export interface IPostAccountsTokenPayload {
	accountId: string;
}

export interface IAccount extends Document {
	_id: ObjectId;
	companyName: string;
	companyLogo: string;
	companyURL: string;
	platform: string;
	defaultCollectionId: ObjectId;
	ownerUserId: ObjectId;
	dailyMetricsUpdatedAt: number | null;
	totalVideosCreatedDaily: number | null;
	latestSnippetImpressionDateDaily: number | null;
	totalVideoImpressionsDaily: number | null;
	totalCollectionsCreatedDaily: number | null;
	totalVideoPlaysDaily: number | null;
	totalPlaytimeDaily: number | null;
	totalVideoCTAClicksDaily: number | null;
	totalConversionDaily: number | null;
	weeklyMetricsUpdatedAt: number | null;
	totalVideosCreatedWeekly: number | null;
	totalCollectionsCreatedWeekly: number | null;
	totalVideoPlaysWeekly: number | null;
	totalPlaytimeWeekly: number | null;
	totalConversionWeekly: number | null;
	dailySyncUpdatedAt: number | null;
	createdAt: number;
	stripeCustomerId: string;
	invoices: AccountInvoice[];
	subscription: Subscription;
	totalImpressionsCurrentCycle: number;
	videoProfile: ObjectId;
	accountTheme: AccountTheme;
}

interface AccountTheme {
	primarySiteColor: string;
	backgroundColor: string;
	primaryTextColor: string;
	secondaryTextColor: string;
	buttonTextColor: string;
	disabledTextColor: string;
	contentBoxColor: string;
}

export interface AccountCreateData {
	companyName: string;
	companyURL: string;
	user: IUser;
}

export interface DeleteAccountRequest extends BaseRequest {
	accountId: string;
}

export interface GetSearchPayload extends BaseAccessRequest {
	search?: string,
	searchType: SearchType
}
