import express, {
	Request,
	Response
} from "express";
import {
	IAccount,
	IPatchAccountPayload,
	IPostAccountPayload
} from "./account.interfaces";
import {
	cancelDBTransaction,
	completeDBTransaction,
	startDBTransaction
} from "../../services/mongodb/transaction.service";
import { readAccount } from "../../services/mongodb/account.service";
import { uploadAssetToCloud } from "../../services/gp/bucket.service";
import { UserModel } from "../user/user.model";
import { InteractiveCollectionModel } from "../interactiveCollection/interactiveCollection.model";
import { AuthenticationModel } from "../authentication/authentication.model";
import { Controller } from "../base/base.controller";
import multer from "multer";
import { decodeAccess } from "../../middleware/decodeAccess.mw";
import { isSchemaValid } from "../../middleware/isSchemaValid.mw";
import {
	DeleteAccount<PERSON>oi,
	patchAccountSchema,
	postAccountSchema
} from "./account.validator";
import { requireSuperWritePermission } from "../../middleware/requireSuperWritePermission.mw";
import { AccountModel } from "./account.model";
import { AccountTokenModel } from "./token/account.token.model";
import {
	APIErrorName,
	Permission
} from "../../interfaces/apiTypes";
import { APIError } from "../../utils/helpers/apiError";
import { CDN_DIR } from "../../utils/helpers/gp.helper";
import { VideoProfileModel } from "../videoProfile/videoProfile.model";

const encodeFinalUrl = (url: string): string => {
	const urlParts = url.split("/");
	const fileName = urlParts.pop();
	if (!fileName) {
		throw new APIError(
			APIErrorName.E_INTERNAL_ERROR,
			`encodeFinalUrl: unable to read filename from url: ${url}`
		);
	}
	const encodedFileName = encodeURIComponent(fileName);
	const encodedUrl = urlParts.join("/") + "/" + encodedFileName;
	return encodedUrl;
};

export class AccountController extends Controller {
	constructor () {
		super();

		this.router.delete(
			"/:id",
			[],
			async (request: Request, response: Response): Promise<Response> => {
				try {
					const validatedRequest = await DeleteAccountJoi.validateAsync({
						apiVersion: request.headers["x-api-version"],
						accessToken: request.headers.authorization,
						accountToken: request.headers["x-account-token"],
						accountId: request.params.id
					});

					await this.verifyAPIVersion(validatedRequest.apiVersion, 1);
					const accessToken = await this.verifyAccessToken(validatedRequest.accessToken);
					const accountToken = await this.verifyAccountToken(validatedRequest.accountToken);
					AccountTokenModel.verifyAccountTokenId(accountToken, validatedRequest.accountId);

					const isOwner = await AccountModel.isOwner(validatedRequest.accountId, accessToken.userId);
					if (!isOwner) {
						throw new APIError(APIErrorName.E_REQUEST_FORBIDDEN, "User is not the owner of the account");
					}

					const accountModel = new AccountModel(response.locals.session);
					await accountModel.deleteOneById(validatedRequest.accountId);
					return response.status(201).send({});
				} catch (error: unknown) {
					return APIError.fromUnknownError(error).log().setResponse(response);
				}
			}
		);

		this.router.post(
			"/",
			[multer().any(), decodeAccess, isSchemaValid(postAccountSchema.data)],
			(request: Request, response: Response) => {
				return postAccountController(request, response);
			}
		);

		this.router.get(
			"/:accountId",
			[express.json({ limit: "2MB" }), decodeAccess],
			(request: Request, response: Response) => {
				return getAccountController(request, response);
			}
		);

		this.router.patch(
			"/:accountId",
			[
				multer().any(),
				decodeAccess,
				requireSuperWritePermission,
				isSchemaValid(patchAccountSchema.data)
			],
			(request: Request, response: Response) => {
				return patchAccountController(request, response);
			}
		);
	}
}

const getAccountController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		if (!req.accountToken) {
			throw new APIError(APIErrorName.E_MISSING_AUTHORIZATION, "Missing x-account-token header");
		}

		if (req.accountToken.account._id.toString() !== req.params.accountId.toString()) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND,
				"account not found or account id does not belong to token");
		}

		const accountDocument: IAccount | null = await readAccount({
			query: {
				_id: req.params.accountId
			},
			path: req.url,
			permissions: [Permission.ALL]
		});

		if (!accountDocument) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "the account document does not exist");
		}

		return res.send({ account: accountDocument });
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

const postAccountController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		if (!req.accessToken?.userId) {
			throw new APIError(APIErrorName.E_INVALID_AUTHORIZATION, "missing userId in access token");
		}

		await startDBTransaction(res.locals.session);

		const payload = req.body as IPostAccountPayload;

		const userModel = new UserModel(res.locals.session);
		const userDocument = await userModel.readOneById(req.accessToken.userId);

		const accountModel = new AccountModel(res.locals.session);
		const numAccounts = await accountModel.countDocuments({ ownerUserId: userDocument._id });

		if (numAccounts >= (userDocument.maxCompanies ?? 1)) {
			await cancelDBTransaction(res.locals.session);
			throw new APIError(APIErrorName.E_REQUEST_FORBIDDEN, "Max Company Limit Reached");
		}

		let accountDocument = await accountModel.create({
			companyName: payload.companyName ?? "",
			companyURL: payload.companyURL ?? "",
			user: userDocument
		});

		// add the new account to the authentication document that matches the userId in the access token
		const authenticationModel = new AuthenticationModel(res.locals.session);
		await authenticationModel.attachAccountByUserId(
			userDocument._id.toString(),
			accountDocument._id.toString());

		const interactiveCollectionModel = new InteractiveCollectionModel(res.locals.session);
		const defaultCollection = await interactiveCollectionModel.createOne({
			title: "Default",
			shoppableVideos: []
		}, accountDocument);

		if (defaultCollection.hasError()) {
			return defaultCollection.getError().setResponse(res);
		}

		accountDocument = await accountModel.updateOneById(accountDocument._id.toString(), {
			defaultCollectionId: defaultCollection.getData()._id
		});

		await completeDBTransaction(res.locals.session);

		return res.send({ account: accountDocument });

	} catch (error: unknown) {
		await cancelDBTransaction(res.locals.session);
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};

const patchAccountController = async (req: Request, res: Response): Promise<Response> => {
	try {
		if (!req.headers["x-api-version"]) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Missing API version");
		}

		const apiVersion = Number(req.headers["x-api-version"]);
		if (isNaN(apiVersion) || apiVersion < 1) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, "Invalid API version");
		}

		if (!req.accountToken) {
			throw new APIError(APIErrorName.E_MISSING_AUTHORIZATION, "Missing x-account-token header");
		}

		if (req.accountToken.account._id.toString() !== req.params.accountId.toString()) {
			throw new APIError(APIErrorName.E_REQUEST_FORBIDDEN,
				"accountId in parameter does not match accountId in access token");
		}

		const requestBody = req.body as IPatchAccountPayload;

		const companyLogo: Express.Multer.File | undefined =
		(req.files as Express.Multer.File[])?.find((f:Express.Multer.File) =>
			f.fieldname === "companyLogo") as Express.Multer.File;

		const accountSet: any = {};

		if (companyLogo) {
			const cacheControl = "no-cache, max-age=0";
			const companyLogoURL = await uploadAssetToCloud(
				{
					file: companyLogo,
					outputFilePath: CDN_DIR.MEDIA + req.accountToken.account._id + "/",
					outputFileName: companyLogo.originalname
				},
				cacheControl
			);

			if (!companyLogoURL) {
				throw new APIError(APIErrorName.E_INTERNAL_ERROR, "failed to upload file for companyLogo input");
			}

			accountSet.companyLogo = encodeFinalUrl(companyLogoURL);
		}

		if (requestBody.companyName) {
			accountSet.companyName = requestBody.companyName;
		}

		if (requestBody.companyURL) {
			accountSet.companyURL = requestBody.companyURL;
		}

		if (requestBody.platform) {
			accountSet.platform = requestBody.platform;
		}

		if (requestBody.videoProfile){
			const videoProfileModel = new VideoProfileModel(null);
			const videoProfile = await videoProfileModel.readOneById(requestBody.videoProfile);
			accountSet.videoProfile = videoProfile._id;
		}

		const accountModel = new AccountModel(null);
		const accountDocument = await accountModel.updateOneById(req.params.accountId, accountSet);

		return res.send({ account: accountDocument });
	} catch (error: unknown) {
		return APIError.fromUnknownError(error).log().setResponse(res);
	}
};
