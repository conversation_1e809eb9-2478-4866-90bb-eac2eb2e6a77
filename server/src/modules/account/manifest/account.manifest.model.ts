import { StorageCacheControl } from "./../../storage/storage.model";
import path from "path";
import { IAccount } from "../account.interfaces";
import {
	DownloadOptions,
	DownloadResponse,
	Storage
} from "@google-cloud/storage";
import {
	getSecrets,
	ISecrets
} from "../../secrets/secrets.model";
import {
	gpLog,
	LogScope
} from "../../../utils/managers/gpLog.manager";
import { StorageService } from "../../storage/storage.service";
import { GCPStorageProvider } from "../../storage/providers/gcp.storage";

interface AccountManifestData {
	allowLandscape: boolean;
	allowCTALead: boolean;
	allowThemes: boolean;
	allowSharing: boolean;
	hideVanityBranding: boolean;
	subscriptionType: string;
}

export class AccountManifestModel {
	constructor (public account: IAccount) {
	}

	public setAccount (account: IAccount): void {
		this.account = account;
	}

	public async update (): Promise<boolean> {
		if (await this.isStale()) {
			await this.write();
			return true;
		}

		return false;
	}

	public getManifestFilePath (): string {
		return path.join("manifests", this.account._id.toString(), AccountManifestModel.manifestFilename);
	}

	public createData (): AccountManifestData {
		try {
			const data = {
				allowLandscape: this.account?.subscription?.allowLandscape ?? false,
				allowCTALead: this.account?.subscription?.allowCTALead ?? false,
				allowThemes: this.account?.subscription?.allowThemes ?? false,
				allowSharing: this.account?.subscription?.allowSharing ?? false,
				hideVanityBranding: this.account?.subscription?.hideVanityBranding ?? false,
				subscriptionType: this.account?.subscription?.type ?? "basic"
			};

			return data;
		} catch (error: unknown) {
			gpLog({
				message: `Error creating manifest data for account ${this.account._id}`,
				objData: error,
				trace: "account.manifest.model | createData",
				scope: LogScope.ERROR
			});

			return {
				subscriptionType: "basic",
				hideVanityBranding: false,
				allowLandscape: false,
				allowCTALead: false,
				allowThemes: false,
				allowSharing: false
			};
		}
	}

	public async read (): Promise<AccountManifestData> {
		try {
			const fileLocation = this.getManifestFilePath();
			const secrets: ISecrets = await getSecrets();
			const storage = new Storage({
				apiEndpoint: secrets.storage.host,
				credentials: secrets.storage.credentials
			});
			const bucket = storage.bucket(secrets.storage.bucketName);
			const file = bucket.file(fileLocation);
			const downloadOptions: DownloadOptions = {
				validation: false
			};
			const downloadResponse: DownloadResponse = await file.download(downloadOptions);
			const fileData = downloadResponse[0];
			return JSON.parse(fileData.toString()) as AccountManifestData;
		} catch (error: unknown) {
			return await this.write();
		}
	}

	public async isStale (): Promise<boolean> {
		const manifestData: AccountManifestData = await this.read();

		try {
			if (this.account.subscription.type !== manifestData.subscriptionType) {
				return true;
			}

			if (this.account.subscription.allowThemes !== manifestData.allowThemes) {
				return true;
			}

			if (this.account.subscription.allowSharing !== manifestData.allowSharing) {
				return true;
			}

			if (this.account.subscription.allowLandscape !== manifestData.allowLandscape) {
				return true;
			}

			if (this.account.subscription.allowCTALead !== manifestData.allowCTALead) {
				return true;
			}

			if (this.account.subscription.hideVanityBranding !== manifestData.hideVanityBranding) {
				return true;
			}

			return false;
		} catch (error) {
			gpLog({
				message: `Error testing manifest isStale for account ${this.account._id}`,
				objData: error,
				trace: "account.manifest.model | isStale",
				scope: LogScope.ERROR
			});
			return true;
		}
	}

	private async write (): Promise<AccountManifestData> {
		const fileLocation = this.getManifestFilePath();
		const manifestData = this.createData();
		const fileContent = JSON.stringify(manifestData);

		const secrets = await getSecrets();
		const storageProvider = new GCPStorageProvider(
			"https://storage.googleapis.com",
			secrets.storage.bucketName,
			secrets.storage.host,
			secrets.storage.credentials
		);
		const storageCacheControl: StorageCacheControl = {
			maxAge: 0,
			public: true
		};
		const storageService = new StorageService(storageProvider);

		await storageService.writeFromBuffer(
			fileLocation,
			Buffer.from(fileContent),
			storageCacheControl
		);

		return manifestData;
	}

	public async delete (): Promise<void> {
		const fileLocation = this.getManifestFilePath();
		const secrets: ISecrets = await getSecrets();
		const storage = new Storage({ apiEndpoint: secrets.storage.host, credentials: secrets.storage.credentials });
		const bucket = storage.bucket(secrets.storage.bucketName);
		const file = bucket.file(fileLocation);
		await file.delete();
	}

	private static manifestFilename = "account-manifest.json";
}
