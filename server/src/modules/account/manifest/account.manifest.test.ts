import { IAccount } from "../account.interfaces";
import { AccountManifestModel } from "./account.manifest.model";
import TestHelper from "../../../__tests__/mocks/testHelper";
import { LocaleAPI } from "../../../interfaces/apiTypes";
import { ISignupPayload } from "../../signup/signup.interfaces";
import {
	createServer,
	initExpressRoutes
} from "../../../express";


describe("AccountManifestModel", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let account: IAccount;
	let additionalAccount: IAccount;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "account.manifest.model.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true,
		maxCompanies: 10
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		account = await testHelper.getAccount(accessToken);
		additionalAccount = await testHelper.createAdditionalAccount(accessToken);
	});

	it("should determine isStale is false after updating the manifest and comparing again", async () => {
		const accountManifest = new AccountManifestModel(account);
		await expect(accountManifest.update()).resolves.toBe(false);
		await expect(accountManifest.isStale()).resolves.toBe(false);
	});

	it("should update the account manifest when hideVanityBranding Changes", async () => {
		const accountManifest = new AccountManifestModel(account);
		await expect(accountManifest.isStale()).resolves.toBe(false);
		account.subscription.hideVanityBranding = true;
		accountManifest.setAccount(account);
		await expect(accountManifest.isStale()).resolves.toBe(true);
		await expect(accountManifest.update()).resolves.toBe(true);
		await expect(accountManifest.isStale()).resolves.toBe(false);
	});

	it("should update the account manifest when the subscription.type Changes", async () => {
		const accountManifest = new AccountManifestModel(account);
		await expect(accountManifest.isStale()).resolves.toBe(false);
		account.subscription.type = "pro";
		accountManifest.setAccount(account);
		await expect(accountManifest.isStale()).resolves.toBe(true);
		await expect(accountManifest.update()).resolves.toBe(true);
		await expect(accountManifest.isStale()).resolves.toBe(false);
	});

	it("should update the account manifest when the data cannot be compared", async () => {
		const accountManifest = new AccountManifestModel({
			_id: account._id
		} as unknown as IAccount);

		await expect(accountManifest.isStale()).resolves.toBe(true);
	});

	it("should update the account object in the manifest", async () => {
		const accountManifest = new AccountManifestModel(account);
		expect(accountManifest.account).toEqual(account);
		accountManifest.setAccount(additionalAccount);
		expect(accountManifest.account).toEqual(additionalAccount);
	});

	it("should create default data when the account data is missing properties", async () => {
		const accountManifest = new AccountManifestModel({
			_id: account._id
		} as unknown as IAccount);

		expect(accountManifest.createData()).toMatchObject({
			allowLandscape: false,
			allowCTALead: false,
			allowThemes: false,
			allowSharing: false,
			hideVanityBranding: false,
			subscriptionType: "basic"
		});
	});
});
