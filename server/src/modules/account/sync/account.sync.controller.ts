import {
	Request,
	Response
} from "express";
import { readJob } from "../../../services/mongodb/jobs.service";
import { APIErrorName } from "../../../interfaces/apiTypes";
import {
	JobsStatus,
	JobsType,
	Job
} from "../../../modules/job/job.model";
import path from "path";
import { getSecrets } from "../../../modules/secrets/secrets.model";
import { APIError } from "../../../utils/helpers/apiError";
import {
	gpLog,
	LogScope
} from "../../../utils/managers/gpLog.manager";
import { VersionValidator } from "../../version/version.validators";
import { AccountSyncJobModel } from "./account.sync.model";

const logTrace = path.basename(__filename);

export class AccountSyncController {

	public post = async (request: Request, response: Response): Promise<Response> => {
		return this.postAccountSyncController(request, response);
	};

	private postAccountSyncController = async (req: Request, res: Response): Promise<Response> => {
		try {
			await VersionValidator.required().validateAsync(req.headers["x-api-version"]);
		} catch (error: unknown) {
			const apiError = new APIError(
				APIErrorName.E_INVALID_INPUT,
				error
			);
			return apiError.log().setResponse(res);
		}

		try {
			const secrets = await getSecrets();
			const cronKey = req.headers["x-cron-key"];
			if (cronKey !== secrets.cron.privateKey) {
				throw new APIError(APIErrorName.E_INVALID_AUTHORIZATION, "Unauthorized access via cron key");
			}

			const query: Partial<Job> = {
				status: JobsStatus.RUNNING,
				type: JobsType.SYNC_ACCOUNTS
			};

			let jobDocument: Job | null = null;
			jobDocument = await readJob(query, null);

			if (jobDocument) {
				gpLog({
					message: "Accounts sync job already running. Exiting with 200.",
					objData: { jobId: jobDocument._id },
					trace: logTrace,
					scope: LogScope.ERROR
				});
				return res.status(200).send({});
			}

			const accountSyncJobModel = new AccountSyncJobModel(null);
			await accountSyncJobModel.createSyncAccountJob();
			return res.status(202).send({});

		} catch (error: unknown) {
			return APIError.fromUnknownError(error).log().setResponse(res);
		}
	};
}
