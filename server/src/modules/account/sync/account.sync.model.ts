import mongoose, {
	ClientSession,
	SaveOptions
} from "mongoose";
import { APIErrorName } from "../../../interfaces/apiTypes";
import { APIError } from "../../../utils/helpers/apiError";
import {
	JobModel,
	JobsStatus,
	JobsType
} from "../../job/job.model";
import { getSecrets } from "../../secrets/secrets.model";
import { JobContainerModel } from "../../job/container/job.container.model";


export class AccountSyncJobModel {
	private session: ClientSession | null;

	constructor (session: ClientSession | null) {
		this.session = session;
	}

	async createSyncAccountJob(): Promise<void> {
		try {
			const options: SaveOptions = { session: this.session };
			const data = {
				tempFilename: new mongoose.Types.ObjectId().toString() + "sync-account",
				type: JobsType.SYNC_ACCOUNTS,
				status: JobsStatus.CREATED,
				statusMessage: "sync accounts job has been created."
			};

			const jobTimeoutSeconds = 3600 * 2;
			const job = await new JobModel(data).save(options);
			const secrets = await getSecrets();
			const jobContainerModel = new JobContainerModel(null, secrets.storage.isLocal);
			await jobContainerModel.runJobWorker(job._id.toString(), jobTimeoutSeconds);
		} catch (error: unknown) {
			if (error instanceof Error && error.name === "MongoServerError" && (error as any).code === 11000) {
				throw new APIError(APIErrorName.E_FILE_UPLOAD_EXISTING_FILENAME, "FILENAME already exists");
			}
			throw error;
		}
	}



}


