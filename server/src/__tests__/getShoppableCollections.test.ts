import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import {
	APIErrorName,
	LocaleAPI
} from "../interfaces/apiTypes";
import {
	IPostCollectionPayload,
	IShoppableCollection
} from "../modules/interactiveCollection/interactiveCollection.interface";
import { ISignupPayload } from "../modules/signup/signup.interfaces";
import { IAccount } from "../modules/account/account.interfaces";
import TestHelper from "./mocks/testHelper";

describe("GET /shoppable-collections", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "get.shoppable.collection.s.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true,
		maxCompanies: 10
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});


	it("[E_NOT_AUTHENTICATED]. Should return 401", async () => {
		const res = await supertest(expressApp)
			.get("/api/shoppable-collections")
			.set("x-api-version", "2")
			.send();
		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_MISSING_AUTHORIZATION);
	});

	it("[Successfully reads shoppable collections]. Should return 200", async () => {
		const res = await supertest(expressApp)
			.get("/api/shoppable-collections")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send();

		expect(res.statusCode).toBe(200);
		const collections: IShoppableCollection[] = res.body;

		// only default collection exists
		expect(collections.length).toBe(1);
		expect(collections[0]._id.toString()).toBe(account.defaultCollectionId.toString());

		const payload: IPostCollectionPayload = {
			title: "Test Collection - no videos",
			shoppableVideos: []
		};

		// create a second Collection associated with the original account
		const resAdditionalCollection = await supertest(expressApp)
			.post("/api/shoppable-collections")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send(payload);

		expect(resAdditionalCollection.statusCode).toBe(200);

		const resGetCollectionsAgain = await supertest(expressApp)
			.get("/api/shoppable-collections")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send();

		expect(resGetCollectionsAgain.statusCode).toBe(200);
		const collections2: IShoppableCollection[] = resGetCollectionsAgain.body;

		expect(collections2.length).toBe(2);

		// create another account that creates a new default collection
		await supertest(expressApp)
			.post("/api/accounts")
			.set("x-api-version", "2")
			.set("content-type", "application/json")
			.set("Authorization", `Bearer ${accessToken}`)
			.accept("json")
			.field("companyName", "my additional account");

		// even though there are now 3 collections - only 2 belong to the original account
		const resGetCollectionsPostAdditionalAccount = await supertest(expressApp)
			.get("/api/shoppable-collections")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send();

		expect(resGetCollectionsPostAdditionalAccount.statusCode).toBe(200);
		const collections3: IShoppableCollection[] = resGetCollectionsPostAdditionalAccount.body;

		expect(collections3.length).toBe(2);
	});

	it("[200]. Should return 200 when passing query params.", async () => {
		let collections: IShoppableCollection[];
		const payload: IPostCollectionPayload = {
			title: "Test Collection - no videos",
			shoppableVideos: []
		};

		const resAdditionalCollection = await supertest(expressApp)
			.post("/api/shoppable-collections")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send(payload);

		expect(resAdditionalCollection.statusCode).toBe(200);

		const resLimit1 = await supertest(expressApp)
			.get("/api/shoppable-collections?limit=1&sortKey=videoScore&SortBy=dsc")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send();

		expect(resLimit1.statusCode).toBe(200);

		collections = resLimit1.body;
		// query param limit=1
		expect(collections.length).toBe(1);

		const resLimit2 = await supertest(expressApp)
			.get("/api/shoppable-collections?limit=2&sortKey=videoScore&SortBy=dsc")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send();

		expect(resLimit2.statusCode).toBe(200);

		collections = resLimit2.body;
		// query param limit=2
		expect(collections.length).toBe(2);
	});

	it("[E_SCHEMA_VALIDATION_ERROR]. Should return 400 when passing invalid query params.", async () => {
		const res = await supertest(expressApp)
			.get("/api/shoppable-collections?limit=five&sortKey=videoScore&SortBy=dsc")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send();
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SCHEMA_VALIDATION_ERROR);
	});
});
