import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import {
	APIErrorName,
	LocaleAPI
} from "../interfaces/apiTypes";
import { IAccount } from "src/modules/account/account.interfaces";
import { ISignupPayload } from "src/modules/signup/signup.interfaces";
import TestHelper from "./mocks/testHelper";
import { randomBytes } from "crypto";
import { IShoppableCollection } from "../modules/interactiveCollection/interactiveCollection.interface";


describe("GET /shoppable-collections/:collectionId", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "get.shoppable.collection.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true,
		maxCompanies: 10
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});

	it("[E_DOCUMENT_NOT_FOUND]. collection does not exist", async () => {
		const mockId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.get(`/api/shoppable-collections/${mockId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send();
		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("[E_REQUEST_FORBIDDEN]. accessToken and collection accounts do not match", async () => {
		const resAdditionalAccount = await supertest(expressApp)
			.post("/api/accounts")
			.set("x-api-version", "2")
			.set("content-type", "application/json")
			.set("Authorization", `Bearer ${accessToken}`)
			.accept("json")
			.field("companyName", "my additional account");

		expect(resAdditionalAccount.statusCode).toBe(200);
		const additionalAccount: IAccount = resAdditionalAccount.body.account;

		const res = await supertest(expressApp)
			.get(`/api/shoppable-collections/${additionalAccount.defaultCollectionId.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send();
		expect(res.statusCode).toBe(403);
		expect(res.body.error).toEqual(APIErrorName.E_REQUEST_FORBIDDEN);
	});

	it("[200]. Should return 200", async () => {
		const res = await supertest(expressApp)
			.get(`/api/shoppable-collections/${account.defaultCollectionId.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send();

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("shoppableCollection");

		const collection = res.body.shoppableCollection as IShoppableCollection;
		expect(collection._id.toString()).toBe(account.defaultCollectionId.toString());
	});
});
