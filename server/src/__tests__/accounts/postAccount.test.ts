import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import {
	APIErrorName,
	LocaleAPI
} from "../../interfaces/apiTypes";
import { ISignupPayload } from "../../modules/signup/signup.interfaces";
import { AccountDBModel } from "../../modules/account/accountDB.model";
import { AuthenticationDBModel } from "../../modules/authentication/authenticationDBModel";
import { InteractiveCollectionDBModel } from "../../modules/interactiveCollection/interactiveCollectionDB.model";
import { UserDBModel } from "../../modules/user/userDB.model";
import { UserModel } from "../../modules/user/user.model";

const expressApp = createServer();
initExpressRoutes(expressApp);

const emailAddress = "<EMAIL>";

describe("POST /accounts", () => {
	let accessToken: string;
	beforeAll(async () => {
		const signupPayload: ISignupPayload = {
			email: emailAddress,
			password: "Password1!",
			firstName: "<PERSON>",
			lastName: "Doe",
			companyName: "John Doe Inc.",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "https://domain.tld",
			legalAgreement: true,
			maxCompanies: 10
		};

		const signUpResponse = await supertest(expressApp)
			.post("/api/auth/signup")
			.set("x-api-version", "3")
			.set("Content-Type", "application/json")
			.accept("json")
			.send(signupPayload);

		if (signUpResponse.statusCode !== 200) {
			console.error(signUpResponse.body);
		}

		expect(signUpResponse.statusCode).toBe(200);
		expect(signUpResponse.body).toHaveProperty("accessToken");

		accessToken = signUpResponse.body.accessToken;
	});

	it("Should return 403 [E_ACCESS_DENIED] when user max companies is exceeded", async () => {
		//Lowering user max companies to 1
		const originalReadOneById = UserModel.prototype.readOneById;
		const spy = jest.spyOn(UserModel.prototype, "readOneById").mockImplementationOnce(
			async function (this: any, ...args) {
				const result = await originalReadOneById.apply(this, args);
				if (result) {
					result.maxCompanies = 1;
				}
				return result;
			}
		);

		const res = await supertest(expressApp)
			.post("/api/accounts")
			.set("x-api-version", "2")
			.set("content-type", "application/json")
			.set("Authorization", `Bearer ${accessToken}`)
			.accept("json")
			.field("companyName", "my additional account");

		if (res.statusCode !== 403) {
			console.error(res.body);
		}

		spy.mockRestore();

		expect(res.statusCode).toBe(403);
		expect(res.body.error).toEqual(APIErrorName.E_REQUEST_FORBIDDEN);
	});

	it("Should return 404 [E_DOCUMENT_NOT_FOUND] when failing to read the user account", async () => {
		const spy = jest.spyOn(UserDBModel, "findOne").mockImplementationOnce((): any => {
			return {
				session: jest.fn().mockReturnValueOnce(null)
			};
		});

		const res = await supertest(expressApp)
			.post("/api/accounts")
			.set("x-api-version", "2")
			.set("Authorization", `Bearer ${accessToken}`);

		if (res.statusCode !== 404) {
			console.error(res.body);
		}

		spy.mockRestore();

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("Should return 500 [E_INTERNAL_ERROR] when failing to create the account", async () => {
		const spy = jest.spyOn(AccountDBModel.prototype, "save").mockImplementationOnce((): any => {
			return Promise.reject(new Error("failed to create account"));
		});

		const res = await supertest(expressApp)
			.post("/api/accounts")
			.set("x-api-version", "2")
			.set("Authorization", `Bearer ${accessToken}`);

		if (res.statusCode !== 500) {
			console.error(res.body);
		}

		spy.mockRestore();

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
	});

	it("Should return 500 [E_INTERNAL_ERROR] when failing to update the authentication document accounts array",
		async () => {
			const spy = jest.spyOn(AuthenticationDBModel, "findOneAndUpdate").mockImplementationOnce((): any => {
				return Promise.reject(new Error("failed to update authentication document."));
			});

			const res = await supertest(expressApp)
				.post("/api/accounts")
				.set("x-api-version", "2")
				.set("Authorization", `Bearer ${accessToken}`);

			if (res.statusCode !== 500) {
				console.error(res.body);
			}

			spy.mockRestore();

			expect(res.statusCode).toBe(500);
			expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
		}
	);

	it("Should return 500 [E_SERVICE_FAILED] when failing to create a default collection", async () => {
		const spy = jest.spyOn(InteractiveCollectionDBModel.prototype, "save").mockImplementationOnce((): any => {
			return Promise.reject(new Error("failed to create collection."));
		});

		const res = await supertest(expressApp)
			.post("/api/accounts")
			.set("x-api-version", "2")
			.set("Authorization", `Bearer ${accessToken}`);

		if (res.statusCode !== 500) {
			console.error(res.body);
		}

		spy.mockRestore();

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
	});

	it("Should return 500 [E_INTERNAL_ERROR] when failing to update the account with the default collection",
		async () => {
			const spy = jest.spyOn(AccountDBModel, "findOneAndUpdate").mockImplementationOnce((): any => {
				return Promise.reject(new Error("failed to update account document."));
			});

			const res = await supertest(expressApp)
				.post("/api/accounts")
				.set("x-api-version", "2")
				.set("Authorization", `Bearer ${accessToken}`);

			if (res.statusCode !== 500) {
				console.error(res.body);
			}

			spy.mockRestore();

			expect(res.statusCode).toBe(500);
			expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
		}
	);
});
