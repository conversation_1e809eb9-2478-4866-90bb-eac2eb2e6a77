import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import {
	APIErrorName,
	LocaleAPI
} from "../../interfaces/apiTypes";
import { AccountModel } from "../../modules/account/account.model";
import { IAccount } from "../../modules/account/account.interfaces";
import { APIError } from "../../utils/helpers/apiError";
import TestHelper from "../mocks/testHelper";
import { ISignupPayload } from "../../modules/signup/signup.interfaces";
import { randomBytes } from "crypto";
import { VideoProfileModel } from "../../modules/videoProfile/videoProfile.model";

import * as BucketService from "../../services/gp/bucket.service";
import path from "path";

describe("PATCH api/accounts/:accountId", () => {
	const mockLogoFilename = "mockCompanyLogoForPatchAccountTest";
	const mockLogoPath = `${__dirname}/${mockLogoFilename}.jpg`;

	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let accountId: string;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "patch.account.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		const account = await testHelper.getAccount(accessToken);
		accountId = account._id.toString();
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});


	it("Return 403 [E_REQUEST_FORBIDDEN] - parameter accountId does not match accountId accountToken", async () => {
		const bogusAccountId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.patch(`/api/accounts/${bogusAccountId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "1");

		expect(res.statusCode).toBe(403);
		expect(res.body.error).toEqual(APIErrorName.E_REQUEST_FORBIDDEN);
	});

	it("Return 500 [E_INTERNAL_ERROR] - failed to upload file for companyLogoFile input", async () => {
		jest.spyOn(BucketService, "uploadAssetToCloud").mockResolvedValueOnce(undefined);

		const res = await supertest(expressApp)
			.patch(`/api/accounts/${accountId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "1")
			.attach("companyLogo", mockLogoPath);

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_INTERNAL_ERROR);
	});

	it("Return 200 - Storage mock successfully upload file for companyLogoFile input", async () => {
		const res = await supertest(expressApp)
			.patch(`/api/accounts/${accountId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "1")
			.attach("companyLogo", mockLogoPath);

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("account");

		const account: IAccount = res.body.account;
		const baseNameCompanyLogo = path.basename(account.companyLogo, path.extname(account.companyLogo));

		expect(baseNameCompanyLogo.startsWith(mockLogoFilename)).toBe(true);
	});

	it("Should return 404 [E_DOCUMENT_NOT_FOUND] when the account document does not exist", async () => {
		jest.spyOn(AccountModel.prototype, "updateOneById").mockRejectedValueOnce(
			new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Account not found")
		);

		const res = await supertest(expressApp)
			.patch(`/api/accounts/${accountId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("ENG-988 ensure beginning and trailing spaces are trimmed", async () => {
		const badCompanyName = "    a company name with leading and trailing spaces   ";

		const res = await supertest(expressApp)
			.patch(`/api/accounts/${accountId}`)
			.set("x-api-version", "2")
			.set("content-type", "multipart/form-data")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.field("companyName", badCompanyName);

		expect(res.statusCode).toBe(200);

		const a : IAccount = res.body.account;
		expect(a.companyName).toBe(badCompanyName.trim());
	});

	it("Should failed to update account due to invalid videoProfile ID.", async () => {
		const res = await supertest(expressApp)
			.patch(`/api/accounts/${accountId}`)
			.set("x-api-version", "3")
			.set("x-account-token", accountToken)
			.set("Authorization", `Bearer ${accessToken}`)
			.field("videoProfile", "invalid-video-profile-id");
		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should successfully update account with videoProfile ID.", async () => {
		const videoProfileModel = new VideoProfileModel(null);
		const defaultProfile = await videoProfileModel.readDefault();
		const res = await supertest(expressApp)
			.patch(`/api/accounts/${accountId}`)
			.set("x-api-version", "3")
			.set("x-account-token", accountToken)
			.set("Authorization", `Bearer ${accessToken}`)
			.field("videoProfile", defaultProfile._id.toString());
		expect(res.statusCode).toBe(200);
		expect(res.body.account.videoProfile).toBe(defaultProfile._id.toString());
	});
});

