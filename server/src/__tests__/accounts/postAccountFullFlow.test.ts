import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import { IAccount } from "../../modules/account/account.interfaces";
import { LocaleAPI } from "../../interfaces/apiTypes";
import { ISignupPayload } from "../../modules/signup/signup.interfaces";

describe("POST /accounts", () => {
	let accessToken: string;
	let expressApp: any;
	const emailAddress = "<EMAIL>";

	beforeAll(async () => {
		expressApp = createServer();
		initExpressRoutes(expressApp);

		const signupPayload: ISignupPayload = {
			email: emailAddress,
			password: "Password1!",
			firstName: "John",
			lastName: "Doe",
			companyName: "John Doe FullFlow Inc.",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "https://domain.tld",
			legalAgreement: true,
			maxCompanies: 10
		};

		const signUpResponse = await supertest(expressApp)
			.post("/api/auth/signup")
			.set("x-api-version", "3")
			.set("Content-Type", "application/json")
			.accept("json")
			.send(signupPayload);

		if (signUpResponse.statusCode !== 200) {
			console.error(signUpResponse.body);
		}

		expect(signUpResponse.statusCode).toBe(200);
		expect(signUpResponse.body).toHaveProperty("accessToken");

		accessToken = signUpResponse.body.accessToken;
	});

	it("ENG-988 ensure beginning and trailing spaces are trimmed", async () => {
		const badCompanyName = "   company name with spaces before and after    ";

		const res = await supertest(expressApp)
			.post("/api/accounts")
			.set("x-api-version", "2")
			.set("content-type", "application/json")
			.set("Authorization", `Bearer ${accessToken}`)
			.accept("json")
			.field("companyName", badCompanyName);

		if (res.statusCode !== 200) {
			console.error(res.body);
		}

		expect(res.statusCode).toBe(200);
		const newAccount: IAccount = res.body?.account ?? null;
		expect(newAccount.companyName).toBe(badCompanyName.trim());
	});
});
