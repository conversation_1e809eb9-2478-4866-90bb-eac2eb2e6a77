/* eslint-disable max-lines-per-function */
import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import { InteractiveCollectionModel } from "../modules/interactiveCollection/interactiveCollection.model";
import { APIError } from "../utils/helpers/apiError";
import {
	APIErrorName,
	LocaleAPI,
	Permission
} from "../interfaces/apiTypes";
import {
	IPutCollectionPayload,
	IShoppableCollection
} from "../modules/interactiveCollection/interactiveCollection.interface";
import { readShoppableVideos } from "../services/mongodb/shoppableVideo.service";
import { IAccount } from "../modules/account/account.interfaces";
import { ISignupPayload } from "../modules/signup/signup.interfaces";
import TestHelper from "./mocks/testHelper";
import { randomBytes } from "crypto";
import { IVideo } from "../modules/video/video.interfaces";
import { IShoppableVideo } from "src/modules/interactiveVideo/interactiveVideo.interface";
import { ObjectId } from "mongoose";

import * as AccountService from "../services/mongodb/account.service";

describe("PUT /shoppable-collections/:collectionId", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	let shoppableVideo: IShoppableVideo;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "get.shoppable.collection.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true,
		maxCompanies: 10
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);

		const video: IVideo = await testHelper.createVideo(account._id.toString());
		const videoId = video._id.toString();

		shoppableVideo = await testHelper.createShoppableVideo(videoId, accountToken, accessToken);

		const collectionModel = new InteractiveCollectionModel(null);
		const defaultCollection = await collectionModel.readOneById(account.defaultCollectionId.toString());
		expect(defaultCollection.shoppableVideos.length).toBe(1);
		expect(defaultCollection.shoppableVideos[0].toString()).toBe(shoppableVideo._id.toString());
	});

	it("[E_INVALID_AUTHORIZATION]. return 401 for invalid authentication token", async () => {
		const mockId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.put(`/api/shoppable-collections/${mockId}`)
			.set("Authorization", "Bearer 123")
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send({});

		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_AUTHORIZATION);
	});

	it("[E_INTERNAL_ERROR]. return 500 failing to read the account document", async () => {
		jest.spyOn(AccountService, "readAccount").mockResolvedValueOnce(null);

		const mockId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.put(`/api/shoppable-collections/${mockId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send({});

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_INTERNAL_ERROR);
	});

	it("[E_SERVICE_FAILED]. return 500 failing to update the shoppable collection", async () => {
		jest.spyOn(InteractiveCollectionModel.prototype, "updateOneById").mockRejectedValueOnce(
			new APIError(APIErrorName.E_SERVICE_FAILED,	"failed to update collection")
		);

		const res = await supertest(expressApp)
			.put(`/api/shoppable-collections/${account.defaultCollectionId.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send({});

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
	});

	it("return 200 - update default collection without any data", async () => {
		const res = await supertest(expressApp)
			.put(`/api/shoppable-collections/${account.defaultCollectionId.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send({});

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("shoppableCollection");

		expect(res.body.shoppableCollection).toHaveProperty("_id");
		expect(res.body.shoppableCollection).toHaveProperty("accountId");
		expect(res.body.shoppableCollection).toHaveProperty("createdAt");
		expect(res.body.shoppableCollection).toHaveProperty("updatedAt");
		expect(res.body.shoppableCollection).toHaveProperty("shoppableVideos");
		expect(res.body.shoppableCollection).toHaveProperty("buttonBackgroundColor");
		expect(res.body.shoppableCollection).toHaveProperty("buttonBackgroundBlur");
		expect(res.body.shoppableCollection).toHaveProperty("iconTextColor");
		expect(res.body.shoppableCollection).toHaveProperty("displayFont");
		expect(res.body.shoppableCollection).toHaveProperty("title");

		expect(res.body.shoppableCollection.title).toBe("Default");

		// optional parameters will not be included due to no default value being set
		expect(res.body.shoppableCollection).not.toHaveProperty("carouselBorderRadius");
		expect(res.body.shoppableCollection).not.toHaveProperty("carouselIsCentered");
		expect(res.body.shoppableCollection).not.toHaveProperty("carouselMargin");
		expect(res.body.shoppableCollection).not.toHaveProperty("carouselGap");
		expect(res.body.shoppableCollection).not.toHaveProperty("widgetBorderRadius");
		expect(res.body.shoppableCollection).not.toHaveProperty("widgetPosition");
		expect(res.body.shoppableCollection).not.toHaveProperty("inlineBorderRadius");
	});

	it("return 200 - update new collection with optional data", async () => {
		const interactiveCollectionModel = new InteractiveCollectionModel(null);
		const modelRes = await interactiveCollectionModel.createOne({
			title: "Not Default",
			shoppableVideos: []
		}, account);

		const newCollection = modelRes.getData();

		const res = await supertest(expressApp)
			.put(`/api/shoppable-collections/${newCollection._id.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send({
				carouselBorderRadius: 5
			});

		expect(res.statusCode).toBe(200);
		expect(res.body.shoppableCollection).toHaveProperty("carouselBorderRadius");
		expect(res.body.shoppableCollection.carouselBorderRadius).toBe(5);
		expect(res.body.shoppableCollection._id.toString()).toBe(newCollection._id.toString());
	});

	it("return 200 - update new collection with full payload", async () => {
		const interactiveCollectionModel = new InteractiveCollectionModel(null);
		const modelRes = await interactiveCollectionModel.createOne({
			title: "Not Default",
			shoppableVideos: []
		}, account);

		const newCollection = modelRes.getData();

		const fullPayload: IPutCollectionPayload = {
			title: "updated",
			shoppableVideos: [],
			buttonBackgroundColor: "updated",
			buttonBackgroundBlur: false,
			iconTextColor: "updated",
			displayFont: "updated",
			carouselBorderRadius: 1,
			carouselIsCentered: false,
			carouselMargin: 2,
			carouselGap: 3,
			widgetBorderRadius: 4,
			widgetPosition: "right",
			inlineBorderRadius: 5
		};

		const res = await supertest(expressApp)
			.put(`/api/shoppable-collections/${newCollection._id.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send(fullPayload);

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("shoppableCollection");
		const collection = res.body.shoppableCollection as IShoppableCollection;

		expect(collection._id.toString()).toBe(newCollection._id.toString());
		expect(collection.accountId).toBe(account._id.toString());
		expect(collection.shoppableVideos).toStrictEqual(fullPayload.shoppableVideos);
		expect(collection.buttonBackgroundColor).toBe(fullPayload.buttonBackgroundColor);
		expect(collection.buttonBackgroundBlur).toBe(fullPayload.buttonBackgroundBlur);
		expect(collection.iconTextColor).toBe(fullPayload.iconTextColor);
		expect(collection.displayFont).toBe(fullPayload.displayFont);
		expect(collection.carouselBorderRadius).toBe(fullPayload.carouselBorderRadius);
		expect(collection.carouselIsCentered).toBe(fullPayload.carouselIsCentered);
		expect(collection.carouselMargin).toBe(fullPayload.carouselMargin);
		expect(collection.carouselGap).toBe(fullPayload.carouselGap);
		expect(collection.widgetBorderRadius).toBe(fullPayload.widgetBorderRadius);
		expect(collection.widgetPosition).toBe(fullPayload.widgetPosition);
		expect(collection.inlineBorderRadius).toBe(fullPayload.inlineBorderRadius);
	});


	it("return 200 update shoppable collection with only the shoppableVideos", async () => {
		const interactiveCollectionModel = new InteractiveCollectionModel(null);
		const modelRes = await interactiveCollectionModel.createOne({
			title: "Soon to have videos",
			shoppableVideos: []
		}, account);

		const newCollection = modelRes.getData();

		const res = await supertest(expressApp)
			.put(`/api/shoppable-collections/${newCollection._id.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send({
				shoppableVideos: [
					shoppableVideo._id
				]
			});

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("shoppableCollection");
		const updatedCollection: IShoppableCollection = res.body.shoppableCollection;

		expect(updatedCollection._id.toString()).toBe(newCollection._id.toString());

		const shoppableVideoIds: ObjectId[] = updatedCollection.shoppableVideos;
		expect(shoppableVideoIds.length).toBe(1);
		expect(shoppableVideoIds[0].toString()).toBe(shoppableVideo._id.toString());
	});

	it("return 200 - update new shoppable collection - include both title and shoppableVideos",	async () => {
		const interactiveCollectionModel = new InteractiveCollectionModel(null);
		const modelRes = await interactiveCollectionModel.createOne({
			title: "Soon to have a new title and a video",
			shoppableVideos: []
		}, account);

		const newCollection = modelRes.getData();

		expect(newCollection.title).toBe("Soon to have a new title and a video");
		expect(newCollection.shoppableVideos).toStrictEqual([]);

		const res = await supertest(expressApp)
			.put(`/api/shoppable-collections/${newCollection._id.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send({
				title: "New Title and one video",
				shoppableVideos: [
					shoppableVideo._id.toString()
				]
			});

		expect(res.statusCode).toBe(200);
		const updatedCollection = res.body.shoppableCollection;

		expect(updatedCollection.title).toBe("New Title and one video");
		expect(updatedCollection.shoppableVideos).toStrictEqual([shoppableVideo._id.toString()]);
	});

	it("return 400 - update shoppable collection - shoppableVideo does not belong to account",	async () => {
		const interactiveCollectionModel = new InteractiveCollectionModel(null);
		const modelRes = await interactiveCollectionModel.createOne({
			title: "Soon to have a new title and a video",
			shoppableVideos: []
		}, account);

		const newCollection = modelRes.getData();

		const additionalAccount = await testHelper.createAdditionalAccount(accessToken);
		const additionalAccountToken = await testHelper.getAccountToken(additionalAccount._id.toString(), accessToken);
		const newVideo = await testHelper.createVideo(additionalAccount._id.toString());
		const newShoppableVideo = await testHelper.createShoppableVideo(
			newVideo._id.toString(),
			additionalAccountToken,
			accountToken);


		const res = await supertest(expressApp)
			.put(`/api/shoppable-collections/${newCollection._id.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send({
				title: "New Title",
				shoppableVideos: [
					newShoppableVideo._id
				]
			});

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	// eslint-disable-next-line max-len
	it("return 400 - update default shoppable collection - shoppableVideos on account does not match default collection",	async () => {
		const additionalVideo = await testHelper.createVideo(account._id.toString());

		// eslint-disable-next-line @typescript-eslint/no-unused-vars
		const additionalShoppableVideo1 = await testHelper.createShoppableVideo(
			additionalVideo._id.toString(),
			accountToken,
			accessToken);

		const additionalShoppableVideo2 = await testHelper.createShoppableVideo(
			additionalVideo._id.toString(),
			accountToken,
			accessToken);

		// The 2 shoppableVideos above are not part of the default collection.
		// Add just one and expect to fail.  Must pass all to pass.
		const resFail = await supertest(expressApp)
			.put(`/api/shoppable-collections/${account.defaultCollectionId.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send({
				title: "New Title",
				shoppableVideos: [
					additionalShoppableVideo2._id
				]
			});

		expect(resFail.statusCode).toBe(400);
		expect(resFail.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	// eslint-disable-next-line max-len
	it("return 200 - update default shoppable collection - shoppableVideos on account does match default collection",	async () => {
		const additionalVideo = await testHelper.createVideo(account._id.toString());

		// shoppable video not part of default collection
		const additionalShoppableVideo = await testHelper.createShoppableVideo(
			additionalVideo._id.toString(),
			accountToken,
			accessToken);

		// get all shoppableVideos on account.
		const shoppableVideosOnAccount: IShoppableVideo[] = await readShoppableVideos({
			query: {
				accountId: account._id
			},
			path: "",
			permissions: [Permission.ALL],
			dbSession: null
		});

		//ShoppableVideo created above is expected to be in the list
		const newShoppableVideoExistsOnAccount = shoppableVideosOnAccount.some(
			sv => sv._id.toString() == additionalShoppableVideo._id.toString()
		);
		expect(newShoppableVideoExistsOnAccount).toBe(true);

		const videoIds = shoppableVideosOnAccount.map((videoDocument: IShoppableVideo) => videoDocument._id.toString());

		// Add all videos on account to default collection and expect to pass
		// which really does not make much sense.
		const resFail = await supertest(expressApp)
			.put(`/api/shoppable-collections/${account.defaultCollectionId.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send({
				title: "New Title",
				shoppableVideos: videoIds
			});

		expect(resFail.statusCode).toBe(200);
	});
});
