import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import { APIError } from "../../utils/helpers/apiError";
import { MetricImpressionModel } from "../../modules/metricImpression/metricImpression.model";
import {
	APIErrorName,
	LocaleAPI
} from "../../interfaces/apiTypes";
import TestHelper from "../mocks/testHelper";
import { ISignupEmailPayload } from "../../modules/signup/signup.interfaces";

import * as MetricVideoClicks from "../../services/mongodb/metricVideoClicks.service";
import { MetricVideoPlayTimeModel } from "../../modules/metricVideoPlayTime/metricVideoPlayTime.model";


describe("GET /metrics/", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	const queryParam =
        "startDate=2023-08-20T16:40:40.529Z&endDate=2023-08-30T16:45:40.529Z";

	const testHelper = new TestHelper(expressApp);
	const inviterSignupEmailPayload: ISignupEmailPayload = {
		email: "<EMAIL>",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "/"
	};

	beforeAll(async () => {
		({ accessToken } = await testHelper.signupEmail(inviterSignupEmailPayload));
		const account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});


	it("Should return 400 [E_INVALID_INPUT] for missing x-api-version.", async () => {
		const res = await supertest(expressApp)
			.get(`/api/metrics?${queryParam}`)
			.set("Authorization", `Bearer ${accessToken}`);

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 401 [E_MISSING_AUTHORIZATION] when missing account token", async () => {
		const res = await supertest(expressApp)
			.get(`/api/metrics?${queryParam}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2");

		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_MISSING_AUTHORIZATION);
	});

	it("Should return 400 [E_SCHEMA_VALIDATION_ERROR] invalid query is passed.", async () => {
		const badQuery = "start=some date";

		const res = await supertest(expressApp)
			.get(`/api/metrics?${badQuery}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_SCHEMA_VALIDATION_ERROR);
	});

	it("Should return 500 [E_SERVICE_FAILED] failed to count documents from DB.", async () => {
		jest.spyOn(MetricImpressionModel.prototype, "countDocuments").mockRejectedValueOnce(
			new APIError(APIErrorName.E_SERVICE_FAILED, "does not matter")
		);

		const res = await supertest(expressApp)
			.get(`/api/metrics?${queryParam}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
	});

	it("Should return 200 [OK].", async () => {
		const metrics = {
			totalImpressions: 2,
			totalClicks: 4,
			totalPlays: 44,
			totalPlayTimeSeconds: 1234
		};

		jest.spyOn(MetricImpressionModel.prototype, "countDocuments")
			.mockResolvedValue(metrics.totalImpressions);
		jest.spyOn(MetricVideoPlayTimeModel.prototype, "countTotalPlaysByDateRange")
			.mockResolvedValue(metrics.totalPlays);
		jest.spyOn(MetricVideoClicks, "countMetricVideoClicks")
			.mockResolvedValue(metrics.totalClicks);
		jest.spyOn(MetricVideoPlayTimeModel.prototype, "sumPlayTimeSecondsByDateRange")
			.mockResolvedValue(metrics.totalPlayTimeSeconds);

		const res = await supertest(expressApp)
			.get(`/api/metrics?${queryParam}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", accountToken);

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("previousMetrics");
		expect(res.body).toHaveProperty("currentMetrics");

		const currentMetrics = res.body.currentMetrics;
		expect(currentMetrics.totalImpressions).toBe(metrics.totalImpressions);
		expect(currentMetrics.totalClicks).toBe(metrics.totalClicks);
		expect(currentMetrics.totalPlays).toBe(metrics.totalPlays);
		expect(currentMetrics.totalPlayTimeSeconds).toBe(metrics.totalPlayTimeSeconds);
	});
});
