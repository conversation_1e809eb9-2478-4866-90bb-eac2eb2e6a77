import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import { InteractiveCollectionModel } from "../modules/interactiveCollection/interactiveCollection.model";
import { ModelResponse } from "../modules/modelResponse/modelResponse.model";
import { AuthenticationModel } from "../modules/authentication/authentication.model";
import { UserModel } from "../modules/user/user.model";
import { deleteInvitations } from "../services/mongodb/invitations.service";
import {
	APIErrorName,
	LocaleAPI
} from "../interfaces/apiTypes";
import { IShoppableCollection } from "../modules/interactiveCollection/interactiveCollection.interface";
import { APIError } from "../utils/helpers/apiError";
import { createInvitationToken } from "../utils/tokens";
import { AccountModel } from "../modules/account/account.model";
import { IAccount } from "../modules/account/account.interfaces";
import { EmailPasswordAuthentication } from "../modules/authentication/emailpassword.authentication.model";
import TestHelper from "./mocks/testHelper";
import { ISignupPayload } from "../modules/signup/signup.interfaces";
import { InvitationModel } from "../modules/invitation/invitation.model";


// eslint-disable-next-line max-lines-per-function
describe("POST /auth/signup failure mode", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "signup.failure.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true,
		maxCompanies: 10
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});


	it("return 400 [E_INVALID_INPUT] for missing x-api-version", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/signup")
			.send({
				email: "<EMAIL>",
				password: "Password1!",
				callbackEndpoint: "https://domain.tld/callback"
			});

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
		expect(res.body.message).toEqual("Missing API version");
	});

	it("return 400 [E_INVALID_INPUT] for x-api-version < 1", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/signup")
			.set("x-api-version", "0")
			.send({
				email: "<EMAIL>",
				password: "Password1!",
				callbackEndpoint: "https://domain.tld/callback"
			});

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("return 400 [E_INVALID_INPUT] for x-api-version NaN", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/signup")
			.set("x-api-version", "x")
			.send({
				email: "<EMAIL>",
				password: "Password1!",
				callbackEndpoint: "https://domain.tld/callback"
			});

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("return 400 [E_INVALID_INPUT] when failing to decode the invitation token", async () => {
		const res = await supertest(expressApp)
			.post("/api/auth/signup")
			.set("x-api-version", "2")
			.send({
				firstName: "John",
				lastName: "Doe",
				email: "<EMAIL>",
				password: "Password1!",
				companyName: "Test Co.",
				locale: "en_US",
				callbackEndpoint: "https://domain.tld/callback",
				legalAgreement: true,
				inviteToken: "x"
			});

		if (res.statusCode !== 400) {
			console.error(res.body);
		}

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("return 404 [E_DOCUMENT_NOT_FOUND] when failing to read the invitation document", async () => {
		const invitationEmail = "<EMAIL>";
		const userExists = false;
		const invitationWithSaltRedacted = await testHelper.createInvitation(
			accountToken,
			invitationEmail,
			accessToken);

		const invitationModel = new InvitationModel(null);
		const invitation = await invitationModel.readOneById(invitationWithSaltRedacted._id.toString());

		const invitationToken = await createInvitationToken(invitation, account, userExists);

		const filter = {
			_id: invitation._id
		};
		const deleteResult = await deleteInvitations(filter, null);
		expect(deleteResult.deletedCount).toBe(1);

		const res = await supertest(expressApp)
			.post("/api/auth/signup")
			.set("x-api-version", "2")
			.send({
				firstName: "John",
				lastName: "Doe",
				email: invitationEmail,
				password: "Password1!",
				companyName: "Test Co.",
				locale: "en_US",
				callbackEndpoint: "https://domain.tld/callback",
				legalAgreement: true,
				inviteToken: invitationToken
			});

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("Should return 400 [E_INVALID_INPUT] if the payload email does not match the invite email", async () => {
		const invitationEmail = "<EMAIL>";
		const userExists = false;

		const invitationWithSaltRedacted = await testHelper.createInvitation(
			accountToken,
			invitationEmail,
			accessToken);
		const invitationModel = new InvitationModel(null);
		const invitation = await invitationModel.readOneById(invitationWithSaltRedacted._id.toString());

		const invitationToken = await createInvitationToken(invitation, account, userExists);

		const res = await supertest(expressApp)
			.post("/api/auth/signup")
			.set("x-api-version", "2")
			.send({
				firstName: "John",
				lastName: "Doe",
				email: "<EMAIL>",
				password: "Password1!",
				companyName: createUserPayload.companyName,
				locale: "en_US",
				callbackEndpoint: "https://domain.tld/callback",
				legalAgreement: true,
				inviteToken: invitationToken
			});

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 404 [E_DOCUMENT_NOT_FOUND] if the account from the invitation does not exist", async () => {
		const invitationEmail = "<EMAIL>";
		const userExists = false;

		const additionalAccount = await testHelper.createAdditionalAccount(accessToken);
		const additionalAccountToken = await testHelper.getAccountToken(additionalAccount._id.toString(), accessToken);

		const invitationWithSaltRedacted = await testHelper.createInvitation(
			additionalAccountToken,
			invitationEmail,
			accessToken);
		const invitationModel = new InvitationModel(null);
		const invitation = await invitationModel.readOneById(invitationWithSaltRedacted._id.toString());

		const invitationToken = await createInvitationToken(invitation, additionalAccount, userExists);

		const accountModel = new AccountModel(null);
		await accountModel.deleteOneById(additionalAccount._id.toString());

		const res = await supertest(expressApp)
			.post("/api/auth/signup")
			.set("x-api-version", "2")
			.send({
				firstName: "John",
				lastName: "Doe",
				email: invitationEmail,
				password: "Password1!",
				companyName: "does not matter",
				locale: "en_US",
				callbackEndpoint: "https://domain.tld/callback",
				legalAgreement: true,
				inviteToken: invitationToken
			});

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("Should return 500 [E_SERVICE_FAILED] when failing to create a user", async () => {
		jest.spyOn(UserModel.prototype, "createOne").mockRejectedValueOnce(
			new APIError(APIErrorName.E_SERVICE_FAILED, "Failed to create user")
		);

		const res = await supertest(expressApp)
			.post("/api/auth/signup")
			.set("x-api-version", "2")
			.send({
				firstName: "John",
				lastName: "Doe",
				email: "<EMAIL>",
				password: "Password1!",
				companyName: "Test Co.",
				locale: "en_US",
				callbackEndpoint: "https://domain.tld/callback",
				legalAgreement: true
			});

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
	});

	it("Should return 500 [E_SERVICE_FAILED] when failing to create a default shoppable collection", async () => {
		jest.spyOn(InteractiveCollectionModel.prototype, "createOne").mockResolvedValueOnce(
			new ModelResponse<IShoppableCollection>(
				null,
				new APIError(APIErrorName.E_SERVICE_FAILED, "failed to create collection")
			)
		);

		const res = await supertest(expressApp)
			.post("/api/auth/signup")
			.set("x-api-version", "2")
			.send({
				firstName: "John",
				lastName: "Doe",
				email: "<EMAIL>",
				password: "Password1!",
				companyName: "Test Co.",
				locale: "en_US",
				callbackEndpoint: "https://domain.tld/callback",
				legalAgreement: true
			});

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
	});

	it("Should return 500 [E_SERVICE_FAILED] when failing to update the account", async () => {
		jest.spyOn(AccountModel.prototype, "updateOneById").mockRejectedValueOnce(
			new APIError(APIErrorName.E_SERVICE_FAILED, "Failed to update account.")
		);

		const res = await supertest(expressApp)
			.post("/api/auth/signup")
			.set("x-api-version", "2")
			.send({
				firstName: "John",
				lastName: "Doe",
				email: "<EMAIL>",
				password: "Password1!",
				companyName: "Test Co.",
				locale: "en_US",
				callbackEndpoint: "https://domain.tld/callback",
				legalAgreement: true
			});

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
	});

	it("Should return 409 [E_AUTH_EXISTS_DUPLICIATE] if an authentication document already exists", async () => {
		jest.spyOn(AuthenticationModel.prototype, "exists").mockResolvedValueOnce(true);

		const res = await supertest(expressApp)
			.post("/api/auth/signup")
			.set("x-api-version", "2")
			.send({
				firstName: "John",
				lastName: "Doe",
				email: "<EMAIL>",
				password: "Password1!",
				companyName: "Test Co.",
				locale: "en_US",
				callbackEndpoint: "https://domain.tld/callback",
				legalAgreement: true
			});

		if (res.statusCode !== 409) {
			console.error(res.body);
		}

		expect(res.statusCode).toBe(409);
		expect(res.body.error).toEqual(APIErrorName.E_AUTH_EXISTS_DUPLICIATE);
	});

	it("Should return 500 [E_SERVICE_FAILED] when failing to create an authentication record", async () => {
		jest.spyOn(EmailPasswordAuthentication.prototype, "createOne").mockRejectedValueOnce(
			new APIError(APIErrorName.E_SERVICE_FAILED, "failed to create auth document.")
		);

		const res = await supertest(expressApp)
			.post("/api/auth/signup")
			.set("x-api-version", "2")
			.send({
				firstName: "John",
				lastName: "Doe",
				email: "<EMAIL>",
				password: "Password1!",
				companyName: "Test Co.",
				locale: "en_US",
				callbackEndpoint: "https://domain.tld/callback",
				legalAgreement: true
			});

		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
	});
});
