{"mongo": {"mongoProtocol": "mongodb", "mongoHost": "localhost:27017", "mongoUsername": "<PERSON><PERSON><PERSON>", "mongoPassword": "password"}, "sendGrid": {"fromAddress": "no-reply@localhost", "apiKey": "1234", "host": "http://localhost:5005"}, "hashkey": {"key": "supersecret"}, "storage": {"bucketName": "local-gpcdn.localhost", "tempBucketName": "local-gp-temp-video-uploads", "host": "http://localhost:5006", "isLocal": true, "credentials": {"type": "service_account", "project_id": "xxxxxxxxxxxxxxxxxxxxxxx", "private_key_id": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "private_key": "-----"}}, "cdn": {"host": "http://localhost:5001/"}, "player": {"host": "http://localhost:5003/"}, "iframely": {"host": "https://iframe.ly", "apiKey": "api-key-provided-by-if<PERSON><PERSON>"}, "stripe": {"privateKey": "sk_", "webhookSecret": "whsec_1234"}, "cron": {"privateKey": "some-private-key"}, "oidc": [{"provider": "intuit", "userInfoEndpoint": "https://pingidc.com/userinfo", "jwksEndpoint": "https://pingidc.com/jwks", "tokenEndpoint": "https://pingidc.com/token", "clientId": "client-id-1", "clientSecret": "client-secret", "redirectEndpoint": "http://localhost/sign-in/intuit"}, {"provider": "google", "userInfoEndpoint": "https://google.com/userinfo", "jwksEndpoint": "https://google.com/jwks", "tokenEndpoint": "https://google.com/token", "clientId": "client-id-2", "clientSecret": "client-secret2", "redirectEndpoint": "https://localhost/sign-in/intuit"}], "speechToText": {"bucketName": "some-bucket-name", "apiEndpoint": "speech.googleapis.com", "storageUrl": "https://storage.googleapis.com", "credentials": **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************}, "vertex": {"credentials": **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************}, "appConfig": {"appName": "Autoplay Video", "shareEndpoint": "http://localhost:5003", "helpEmail": "help@localhost", "instagramURL": "http://localhost", "youtubeURL": "http://localhost", "xURL": "http://localhost", "privacyPolicyURL": "http://localhost/privacy-policy", "termsOfServiceURL": "http://localhost/terms-of-service", "gcpProjectId": "ap-development-461915", "gcpRegion": "us-central1", "gcpVertexModel": "gemini-2.5-flash"}, "heygen": {"apiKey": "xxxx"}, "elevenlabs": {"apiKey": "sk_"}}