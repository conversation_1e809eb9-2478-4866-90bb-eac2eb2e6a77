import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import { IAccount } from "../../modules/account/account.interfaces";
import { InteractiveVideoModel } from "../../modules/interactiveVideo/interactiveVideo.model";
import { IShoppableVideo } from "../../modules/interactiveVideo/interactiveVideo.interface";
import {
	APIErrorName,
	LocaleAPI
} from "../../interfaces/apiTypes";
import { ISignupPayload } from "../../modules/signup/signup.interfaces";
import TestHelper from "../mocks/testHelper";
import { APIError } from "../../utils/helpers/apiError";


describe("GET /shoppable-videos/:videoId", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "get.shoppable.video.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true,
		maxCompanies: 10
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});

	it("[E_SERVICE_FAILED]. Should return 500 due to unknown mongo error", async () => {
		jest.spyOn(InteractiveVideoModel.prototype, "readOneById").mockRejectedValueOnce(
			new APIError(APIErrorName.E_SERVICE_FAILED, "does not matter")
		);

		const res = await supertest(expressApp)
			.get("/api/shoppable-videos/63e27e5a1f734c5a973761c3")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.send();
		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_SERVICE_FAILED);
	});

	it("[E_DOCUMENT_NOT_FOUND]. Should return 404 - InteractiveVideo id does not exist", async () => {
		const res = await supertest(expressApp)
			.get("/api/shoppable-videos/63e27e5a1f734c5a973761c3")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.send();
		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("[E_REQUEST_FORBIDDEN]. Should return 403, video.account and accounttoken do not match", async () => {
		const additionalAccount = await testHelper.createAdditionalAccount(accessToken);
		const additionalAccountToken = await testHelper.getAccountToken(additionalAccount._id.toString(), accessToken);
		const newVideo = await testHelper.createVideo(additionalAccount._id.toString());
		const newShoppableVideoCreatedWithAdditionalAccount = await testHelper.createShoppableVideo(
			newVideo._id.toString(),
			additionalAccountToken,
			accessToken);


		const res = await supertest(expressApp)
			.get(`/api/shoppable-videos/${newShoppableVideoCreatedWithAdditionalAccount._id.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.send();
		expect(res.statusCode).toBe(403);
		expect(res.body.error).toEqual(APIErrorName.E_REQUEST_FORBIDDEN);
	});

	it("[200]. Should return 200", async () => {
		const newVideo = await testHelper.createVideo(account._id.toString());
		const newShoppableVideo = await testHelper.createShoppableVideo(
			newVideo._id.toString(),
			accountToken,
			accessToken);

		const res = await supertest(expressApp)
			.get(`/api/shoppable-videos/${newShoppableVideo._id.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "1")
			.set("x-account-token", accountToken)
			.send();

		expect(res.statusCode).toBe(200);
		expect(res.body).toHaveProperty("shoppableVideo");

		const returnedShoppableVideo: IShoppableVideo = res.body.shoppableVideo;
		expect(returnedShoppableVideo._id.toString()).toBe(newShoppableVideo._id.toString());
		expect(returnedShoppableVideo.accountId).toBe(newShoppableVideo.accountId);
		expect(returnedShoppableVideo.videoId).toBe(newShoppableVideo.videoId);
	});
});
