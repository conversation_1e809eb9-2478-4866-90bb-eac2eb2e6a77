import { UserModel } from "../../modules/user/user.model";
import { initMongoDB } from "../../mongo";
import { AuthorizationServerMock } from "../../modules/oidc/__mocks__/oidc.authorization.server";
import mongoose from "mongoose";
import * as BucketService from "../../services/gp/bucket.service";
import TestHelper from "./testHelper";
import { Readable } from "stream";

process.env.IMAGE_NAME = "test-image";
process.env.SERVER_PATH = "/path/to/server";
process.env.VOLUME_PATH = "/path/to/volume";
process.env.NETWORK_NAME = "test-network";

jest.mock("dockerode", () => {
	return jest.fn().mockImplementation(() => ({
		createContainer: jest.fn().mockResolvedValue({
			start: jest.fn().mockResolvedValue(undefined),
			logs: jest.fn().mockResolvedValue(
				Readable.from(["mock log output"])
			),
			modem: {
				// eslint-disable-next-line @typescript-eslint/no-unused-vars
				demuxStream: jest.fn((stream, stdout, stderr) => {
					// No-op mock for demuxStream
					return undefined;
				})
			}
		})
	}));
});

jest.mock("../../services/product/iframely.service", () => ({
	fetchProductData: jest.fn().mockResolvedValue({
		meta: {
			title: "Product title",
			price: "100",
			currency: "USD",
			description: "Product description"
		},
		links: {
			thumbnail: [
				{
					href: "https://www.site.com/product/image.jpg"
				}
			]
		}
	})
}));

jest.mock("../../services/email/sendEmail", () => ({
	sendEmail: jest.fn().mockResolvedValue(true)
}));
jest.mock("../../modules/oidc/oidc.authorization.server", () => {
	return {
		AuthorizationServer: AuthorizationServerMock
	};
});

jest.spyOn(BucketService, "uploadAssetChunksToCloud").mockImplementation(
	(input): Promise<IUploadChunksOut | undefined> => {
		const ret: IUploadChunksOut = {
			url: "http://localhost:5001/" + input.location
		};
		return new Promise((resolve) => {
			resolve(ret);
		});
	}
);

// npm modules we have mocks for to be used for all tests
jest.mock("stripe");
jest.mock("@google-cloud/storage");

import jwksClient from "jwks-rsa";
import { IUploadChunksOut } from "../../services/gp/bucket.service";
jest.mock("jwks-rsa");
(jwksClient as jest.MockedFunction<typeof jwksClient>).mockReturnValue({
	getSigningKey: jest.fn().mockImplementation((kid, callback) => {
		callback(null, { getPublicKey: () => AuthorizationServerMock.publicKey });
	})
} as unknown as jwksClient.JwksClient);


beforeAll(async () => {
	await initMongoDB(process.env.MONGO_URI as string);

	const indexes = await UserModel.getIndexes();

	expect(indexes).toEqual(
		expect.arrayContaining([
			expect.objectContaining({
				name: "email_index",
				key: { email: 1 },
				unique: true
			})
		])
	);

	await TestHelper.initializeEssentialData();
});

afterAll(async () => {
	await mongoose.disconnect();
	jest.restoreAllMocks();
});
