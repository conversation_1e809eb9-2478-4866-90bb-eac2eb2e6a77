import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import {
	APIErrorName,
	LocaleAPI
} from "../interfaces/apiTypes";
import { ISignupPayload } from "../modules/signup/signup.interfaces";
import TestHelper from "./mocks/testHelper";
import { randomBytes } from "crypto";
import { IAccount } from "../modules/account/account.interfaces";
import { IKey } from "../modules/apiKey/apiKey.interfaces";


describe("DELETE /api/keys/:keyId", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "delete.key.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true,
		maxCompanies: 10
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		const account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});

	it("[404]. return 404 if key document not found", async () => {
		const mockKeyId = randomBytes(12).toString("hex");
		const res = await supertest(expressApp)
			.delete(`/api/keys/${mockKeyId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send();
		expect(res.statusCode).toBe(404);
	});

	it("[403]. return 403 if mixed accountId in accessToken and key document", async () => {
		const resAdditionalAccount = await supertest(expressApp)
			.post("/api/accounts")
			.set("x-api-version", "2")
			.set("content-type", "application/json")
			.set("Authorization", `Bearer ${accessToken}`)
			.accept("json")
			.field("companyName", "my additional account");

		expect(resAdditionalAccount.statusCode).toBe(200);
		const additionalAccount: IAccount = resAdditionalAccount.body.account;

		const wrongAccountToken = await testHelper.getAccountToken(additionalAccount._id.toString(), accessToken);

		const correctAccountToken = accountToken;
		const resNewApikey = await supertest(expressApp)
			.post("/api/keys")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", correctAccountToken);

		expect(resNewApikey.statusCode).toBe(200);
		const key: IKey = resNewApikey.body.key;

		const res = await supertest(expressApp)
			.delete(`/api/keys/${key._id.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", wrongAccountToken)
			.set("x-api-version", "2")
			.send();

		expect(res.statusCode).toBe(403);
		expect(res.body.error).toEqual(APIErrorName.E_REQUEST_FORBIDDEN);
	});

	it("[200]. return 200", async () => {
		const resNewApikey = await supertest(expressApp)
			.post("/api/keys")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", accountToken);

		expect(resNewApikey.statusCode).toBe(200);
		const key: IKey = resNewApikey.body.key;

		const res = await supertest(expressApp)
			.delete(`/api/keys/${key._id.toString()}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send();

		expect(res.statusCode).toBe(200);
	});
});
