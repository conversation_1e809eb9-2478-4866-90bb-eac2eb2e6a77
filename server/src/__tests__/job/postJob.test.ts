// DEPRECATED

import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../../express";
import {
	APIErrorName,
	LocaleAPI
} from "../../interfaces/apiTypes";
import { ISignupEmailPayload } from "src/modules/signup/signup.interfaces";
import TestHelper from "../mocks/testHelper";
import { IAccount } from "../../modules/account/account.interfaces";
import { JobsType } from "../../modules/job/job.model";
import { UserModel } from "../../modules/user/user.model";
import { APIError } from "../../utils/helpers/apiError";

import * as AccountService from "../../services/mongodb/account.service";


describe("POST /jobs", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	const testHelper = new TestHelper(expressApp);

	beforeAll(async () => {
		const signupEmailPayload: ISignupEmailPayload = {
			email: "<EMAIL>",
			locale: LocaleAPI.EN_US,
			callbackEndpoint: "/"
		};

		({ accessToken } = await testHelper.signupEmail(signupEmailPayload));
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});

	it("Should return 400 [E_INVALID_INPUT] for missing x-api-version.", async () => {
		const res = await supertest(expressApp)
			.post("/api/jobs")
			.set("Authorization", `Bearer ${accessToken}`);

		expect(res.statusCode).toBe(400);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_INPUT);
	});

	it("Should return 401 [E_MISSING_AUTHORIZATION] when missing account token", async () => {
		const res = await supertest(expressApp)
			.post("/api/jobs")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.field("sourceURL", "does not matter")
			.field("jobType", JobsType.ENCODE_VIDEO);

		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_MISSING_AUTHORIZATION);
	});

	it("Should return 404 [E_DOCUMENT_NOT_FOUND] when the account document does not exist.", async () => {
		jest.spyOn(AccountService, "readAccount2").mockResolvedValueOnce(null);

		const res = await supertest(expressApp)
			.post("/api/jobs")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", accountToken)
			.field("sourceURL", "does not matter")
			.field("jobType", JobsType.ENCODE_VIDEO);

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});

	it("Should return 404 [E_DOCUMENT_NOT_FOUND] when the user document does not exist.", async () => {
		jest.spyOn(UserModel.prototype, "readOneById").mockRejectedValueOnce(
			new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "User not found")
		);

		const res = await supertest(expressApp)
			.post("/api/jobs")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-api-version", "2")
			.set("x-account-token", accountToken)
			.field("sourceURL", "does not matter")
			.field("jobType", JobsType.ENCODE_VIDEO);

		expect(res.statusCode).toBe(404);
		expect(res.body.error).toEqual(APIErrorName.E_DOCUMENT_NOT_FOUND);
	});
});
