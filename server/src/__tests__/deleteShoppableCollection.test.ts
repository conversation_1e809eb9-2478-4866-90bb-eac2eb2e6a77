import supertest from "supertest";
import {
	createServer,
	initExpressRoutes
} from "../express";
import { APIError } from "../utils/helpers/apiError";
import {
	APIErrorName,
	LocaleAPI
} from "../interfaces/apiTypes";
import { ISignupPayload } from "src/modules/signup/signup.interfaces";
import TestHelper from "./mocks/testHelper";
import { IPostCollectionPayload } from "../modules/interactiveCollection/interactiveCollection.interface";
import { IAccount } from "../modules/account/account.interfaces";

import * as BucketService from "../services/gp/bucket.service";


describe("DELETE /shoppable-collections/:collectionId", () => {
	const expressApp = createServer();
	initExpressRoutes(expressApp);

	let accessToken: string;
	let accountToken: string;
	let account: IAccount;
	const testHelper = new TestHelper(expressApp);

	const createUserPayload = {
		firstName: "John",
		lastName: "Doe",
		email: "<EMAIL>",
		password: "Password1!",
		companyName: "delete.shoppable.collection.tests Co.",
		locale: LocaleAPI.EN_US,
		callbackEndpoint: "https://domain.tld/callback",
		legalAgreement: true,
		maxCompanies: 10
	} as ISignupPayload;

	beforeAll(async () => {
		({ accessToken } = await testHelper.signup(createUserPayload));
		account = await testHelper.getAccount(accessToken);
		accountToken = await testHelper.getAccountToken(account._id.toString(), accessToken);
	});

	it("[200]. return 200", async () => {
		const payload: IPostCollectionPayload = {
			title: "Test Collection - empty",
			shoppableVideos: []
		};

		const resNewCollection = await supertest(expressApp)
			.post("/api/shoppable-collections")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send(payload);

		expect(resNewCollection.statusCode).toBe(200);
		expect(resNewCollection.body).toHaveProperty("shoppableCollection");

		const shoppableCollectionId = resNewCollection.body.shoppableCollection;

		const res = await supertest(expressApp)
			.delete(`/api/shoppable-collections/${shoppableCollectionId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send();
		expect(res.statusCode).toBe(200);
	});

	it("[403]. return 403 when attempting to delete default collection", async () => {
		const defaultCollectionId = account.defaultCollectionId;

		const res = await supertest(expressApp)
			.delete(`/api/shoppable-collections/${defaultCollectionId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send();
		expect(res.statusCode).toBe(403);
		expect(res.body.error).toEqual(APIErrorName.E_REQUEST_FORBIDDEN);
	});

	it("[401]. Should return 401 when attempting to delete collection from another account", async () => {
		const resAdditionalAccount = await supertest(expressApp)
			.post("/api/accounts")
			.set("x-api-version", "2")
			.set("content-type", "application/json")
			.set("Authorization", `Bearer ${accessToken}`)
			.accept("json")
			.field("companyName", "my additional account");

		expect(resAdditionalAccount.statusCode).toBe(200);
		const additionalAccount: IAccount = resAdditionalAccount.body.account;

		const res = await supertest(expressApp)
			.delete(`/api/shoppable-collections/${additionalAccount.defaultCollectionId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send();
		expect(res.statusCode).toBe(401);
		expect(res.body.error).toEqual(APIErrorName.E_INVALID_AUTHORIZATION);
	});

	it("[500]. Should return 500 when delete Assets From Cloud fails.", async () => {
		jest.spyOn(BucketService, "deleteAssetsFromCloud").mockRejectedValueOnce(
			new APIError(APIErrorName.E_INTERNAL_ERROR, "does not matter")
		);

		const payload: IPostCollectionPayload = {
			title: "Test Collection - empty again",
			shoppableVideos: []
		};

		const resNewCollection = await supertest(expressApp)
			.post("/api/shoppable-collections")
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send(payload);

		expect(resNewCollection.statusCode).toBe(200);
		expect(resNewCollection.body).toHaveProperty("shoppableCollection");

		const shoppableCollectionId = resNewCollection.body.shoppableCollection;

		const res = await supertest(expressApp)
			.delete(`/api/shoppable-collections/${shoppableCollectionId}`)
			.set("Authorization", `Bearer ${accessToken}`)
			.set("x-account-token", accountToken)
			.set("x-api-version", "2")
			.send();
		expect(res.statusCode).toBe(500);
		expect(res.body.error).toEqual(APIErrorName.E_INTERNAL_ERROR);
	});
});
