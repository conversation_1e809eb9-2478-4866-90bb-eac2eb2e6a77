#!/bin/bash

# Load environment variables
if [ -f .env ]; then
  export $(grep -v '^#' .env | xargs)
else
  echo "❌ .env file not found!"
  exit 1
fi

# Validate required vars
if [ -z "$PROJECT_ID" ] || [ -z "$CDN_BUCKET_NAME" ] || [ -z "$TEMP_BUCKET_NAME" ] || [ -z "$GOOGLE_APPLICATION_CREDENTIALS" ]; then
  echo "❌ Missing one or more required variables in .env"
  exit 1
fi

# Activate service account and set project
echo "🔐 Activating service account..."
gcloud auth activate-service-account --key-file="$GOOGLE_APPLICATION_CREDENTIALS"
gcloud config set project "$PROJECT_ID"

# CORS config file
CORS_CONFIG_FILE="cors.json"
if [ ! -f "$CORS_CONFIG_FILE" ]; then
  echo "❌ CORS config file '$CORS_CONFIG_FILE' not found!"
  exit 1
fi

# Function to apply CORS
apply_cors() {
  local BUCKET=$1
  echo "📦 Applying CORS config to bucket: $BUCKET"
  gsutil cors set "$CORS_CONFIG_FILE" gs://"$BUCKET"

  echo "🔍 Verifying CORS config for bucket: $BUCKET"
  gsutil cors get gs://"$BUCKET"
  echo "---------------------------------------------"
}

# Apply CORS to both buckets
apply_cors "$CDN_BUCKET_NAME"
apply_cors "$TEMP_BUCKET_NAME"
