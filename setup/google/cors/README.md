# Google Cloud Storage CORS Setup

This script configures CORS for two Google Cloud Storage buckets using a provided JSON config.

## Prerequisites

- Google Cloud SDK (`gcloud` and `gsutil` installed)
- Service account credentials JSON file

## Setup Instructions

1. **Copy the environment sample file:**

   ```sh
   cp .env-sample .env
   ```

2. **Edit `.env` and set your values:**

   ```
   PROJECT_ID=your-gcp-project-id
   CDN_BUCKET_NAME=your-cdn-bucket
   TEMP_BUCKET_NAME=your-temp-bucket
   GOOGLE_APPLICATION_CREDENTIALS=/absolute/path/to/credentials.json
   ```

3. **Ensure `cors.json` exists and is configured as needed.**  
   The default allows all origins and methods.

4. **Run the script:**

   ```sh
   chmod +x cors.sh
   ./cors.sh
   ```

## What the script does

- Loads environment variables from `.env`
- Activates your service account and sets the GCP project
- Applies the CORS config in `cors.json` to both buckets
- Verifies the CORS settings

---
