import React from "react";

Object.defineProperty(window, "localStorage", {
	value: {
		getItem: (item: string) => {
			if (item === "accessToken") return "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0eXBlIjoiYWNjZXNzdG9rZW4iLCJhdXRoZW50aWNhdGlvbklkIjoiIiwidXNlcklkIjoiIiwiaWF0Ijo5OTk5OTk5OTk5LCJleHAiOjk5OTk5OTk5OTl9.TyxGOiWA_l25X4SDdN5CZBf0AXSXgX-fwC5GwQixzZU";
			return "";
		},
		setItem: jest.fn(),
		removeItem: jest.fn(),
		clear: jest.fn()
	},
	writable: true
});

jest.mock("lottie-react", () => () => "LottieAnimation");
jest.mock("react-datepicker/dist/react-datepicker.css", () => () => "ReactDatePicker");
jest.mock("react-bootstrap/ProgressBar", () => {
  return {
    __esModule: true,
    default: (props: any) => React.createElement('div', { 'data-testid': 'mock-progress-bar', ...props }, props.children)
  };
});

interface mockColorInterface {
	hex: () => {};
	isLight: () => {};
	isDark: () => {};
	lighten: () => {};
	darken: () => {};
	luminosity: () => {};
	lightness: () => {};
}

const mockColorInstance: mockColorInterface = {
	hex: jest.fn(() => '#000000'),
	isLight: jest.fn(() => true),
	isDark: jest.fn(() => false),
	lighten: jest.fn(() => mockColorInstance),
	darken: jest.fn(() => mockColorInstance),
	luminosity: jest.fn(() => 0.5),
	lightness: jest.fn(() => mockColorInstance),
};

jest.mock('color', () => ({
	__esModule: true,
	default: jest.fn(() => mockColorInstance)
}));
