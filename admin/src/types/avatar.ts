export enum VoiceProviderName {
	ELEVENLABS = "elevenlabs"
}

export interface Voice {
	_id: string;
	accountId: string;
	name: string;
	voiceId: string;
	provider: string;
	verified: boolean;
	createdAt: Date;
	updatedAt: Date;
}

export interface Look {
	_id: string;
	accountId: string;
	name: string;
	groupId: string;
	imageUrl: string;
	lookId: string;
	createdAt: Date;
	updatedAt: Date;
}

export interface GenerateAvatarVideoOptions {
	voiceId: string;
	lookId: string;
	text: string;
}
