import { CaptionData } from "./caption";

export interface Video {
	tempFilename: string;
	_id: string;
	createdAt: string;
	updatedAt: string;
	accountId: string;
	posterFileLocation: string;
	publicPosterURL: string;
	publicGifURL: string;
	publicPosterPlayEmbedURL: string;
	videoFileLocation?: string;
	publicVideoURL: string;
	videoWidthPx?: number;
	videoHeightPx?: number;
	captionData?: CaptionData;
}

export enum JobsType {
	ENCODE_VIDEO = "encodeVideo",
	AVATAR_VIDEO = "avatarVideo",
}

export enum JobVideoStatus {
	CREATED = "created",
	RUNNING = "running",
	COMPLETE = "complete",
	FAILED = "failed"
}

export interface VideoEncodeResponse {
	tempFilename: string;
	status: string;
	statusMessage: string;
	createdAt: number;
	updatedAt: number;
	nextStatusCheck: number;
	statusPercent?: number;
	videoId?: string;
}
