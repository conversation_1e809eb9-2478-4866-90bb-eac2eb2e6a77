export enum MetricTypes {
	IMPRESSIONS = "impressions",
	PLAYS = "plays",
	CLICKS = "clicks",
	PLAYTIME = "playtime"
}

export interface Metrics {
	previousMetrics: MetricsPeriod;
	currentMetrics: MetricsPeriod;
}

export interface MetricsPeriod {
	totalImpressions: number;
	totalPlays: number;
	totalClicks: number;
	totalPlayTimeSeconds: number;
}

export interface MetricsDisplay {
	// Engagement
	impressions?: number;
	impressionsPrev?: number;
	impressionsDiff?: number;
	plays?: number;
	playsPrev?: number;
	playsDiff?: number;
	clicks?: number;
	clicksPrev?: number;
	clicksDiff?: number;
	playtime?: number;
	playtimePrev?: number;
	playtimeDiff?: number;
	topVideos?: MetricsDisplayVideo[];

	// Conversion
	totalOrders?: number;
	totalOrdersDiff?: number;
	engagedSessions?: number;
	engagedSessionsDiff?: number;
	avgBasketSize?: number;
	avgBasketSizeDiff?: number;
	conversionRate?: number;
	conversionRateDiff?: number;
	topConversionVideos?: MetricsDisplayVideo[];
	topCollections?: MetricsDisplayCollection[];
}

export interface MetricsDisplayVideo {
	_id: string;
	title?: string | null;
	plays?: number | null;
	clicks?: number | null;
	playtime?: number | null;
	score?: number | null;
	videoPosterURL?: string;
	videoDisplayMode?: string;
	orderCount?: number | null;
}

export interface MetricsDisplayCollection {
	// Engagement
	_id: string;
	title?: string | null;
	plays?: number | null;
	clicks?: number | null;
	playtime?: number | null;
	score?: number | null;

	// Conversion
	orderCount?: number | null;
}

export enum MetricsTab {
	ENGAGEMENT = "engagement",
	CONVERSION = "conversion"
}

export enum ConversionSecondaryTab {
	TOP_VIDEOS = "top_videos",
	TOP_COLLECTIONS = "top_collections"
}
