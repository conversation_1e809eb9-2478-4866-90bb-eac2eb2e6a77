import React, { useEffect, useRef, useState } from "react";
import { isValidColor } from "@src/utils/colors";
import { useRecoilValue } from "recoil";
import { accountThemeAtom } from "../authentication/state";
import { IconDefinition } from "@fortawesome/fontawesome-svg-core";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

type SVGProps = {
	src: string | IconDefinition;
	width?: string;
	height?: string;
	fill?: string;
	className?: string;
	alt?: string;
	useFastLoading?: boolean;
};

const SVG: React.FC<SVGProps> = ({ src, width, height, fill, className, alt, useFastLoading = false }) => {
	const objectRef = useRef<HTMLObjectElement>(null);
	const svgRef = useRef<SVGSVGElement>(null);
	const [calcWidth, setCalcWidth] = useState<number>(0);
	const [calcHeight, setCalcHeight] = useState<number>(0);
	const [isVisible, setIsVisible] = useState<boolean>(false);
	const accountTheme = useRecoilValue(accountThemeAtom);

	const svgStyle: React.CSSProperties = {
		width: `${width ?? "100%"}`,
		height: `${height ?? "100%"}`,
		border: "none",
		fill: `${fill ?? "inherit"}`,
		color: `${fill ?? "inherit"}`,
		pointerEvents: "none"
	};

	useEffect(() => {
		if (useFastLoading) {
			const element = svgRef.current;
			if (!element) return;

			const observer = new IntersectionObserver(([entry]) => {
				setIsVisible(entry.isIntersecting);
			});

			observer.observe(element);

			return () => observer.disconnect();
		}
	}, [svgRef, useFastLoading]);

	useEffect(() => {
		if (!useFastLoading) {
			if (objectRef.current) {
				const objectElement = objectRef.current;

				const updateSvgStyle = (): void => {
					const objectContent = objectElement?.contentDocument;
					if (objectContent) {
						const svgElement = objectContent.querySelector("svg") as SVGElement;
						if (svgElement) {
							svgElement.style.width = "100%";
							svgElement.style.height = "100%";

							const svgFill = fill && isValidColor(fill) ? fill : getComputedStyle(objectElement).fill;
							svgElement.style.fill = svgFill;
							svgElement.querySelectorAll("*").forEach(child => {
								const childFill = (child as HTMLElement).getAttribute("fill");
								if (childFill)
									(child as HTMLElement).style.fill = svgFill;
							});
						}
					}
				};

				objectElement.addEventListener("load", updateSvgStyle);
				updateSvgStyle();
				return () => objectElement?.removeEventListener("load", updateSvgStyle);
			}
		}
	}, [accountTheme, fill, useFastLoading]);

	useEffect(() => {
		if (useFastLoading) {
			if (svgRef.current) {
				const bbox = svgRef.current.getBBox();
				if (bbox.width > 0 && bbox.height > 0) {
					setCalcWidth(bbox.width);
					setCalcHeight(bbox.height);
				}
			}
		}
	}, [svgRef, useFastLoading, isVisible]);

	return (typeof src === "string")
		? (!useFastLoading
			? <object type="image/svg+xml" ref={objectRef} data={src} style={svgStyle} className={className}>
				<img src={src} alt={alt}/>
			</object>
			: <svg style={svgStyle} ref={svgRef} viewBox={`0 0 ${calcWidth} ${calcHeight}`} className={className}>
				<use href={src} style={svgStyle} />
			</svg>)
		: <FontAwesomeIcon icon={src} style={svgStyle} />;
};

export default SVG;
