import { ControlProps, StylesConfig } from "react-select";
import { apTheme } from "../../config/theme";
import React from "react";
import { RemoveIcon, SelectItem, SelectItemContainer, SelectItemSubtext, SelectItemTitle } from "@src/styles/components";
import { useTheme } from "styled-components";

export type CustomSelectOption = {
	value?: string;
	label?: string;
};

interface CustomSelectItemProps {
	_id: string;
	title: string;
	subtext: string;
	readonly?: boolean;
	onRemove?: (_id: string) => void;
}

export const CustomSelectStyles = (): StylesConfig<CustomSelectOption, false> => {

	const theme: apTheme = useTheme() as apTheme;

	return ({
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		option: (provided, state: any) => ({
			...provided,
			backgroundColor: state.isFocused ? theme.colors.apInputBackground : theme.colors.apBackgroundColor,
			color: theme.colors.apInputColor,
			cursor: "pointer",
			fontWeight: state.data.value === "new_collection" ? "bold" : "normal"
		}),
		menuList: (provided) => ({
			...provided,
			padding: 0
		}),
		singleValue: (provided) => ({
			...provided,
			color: theme.colors.apInputColor
		}),
		input: (provided) => ({
			...provided,
			color: theme.colors.apInputColor
		}),
		noOptionsMessage: (provided) => ({
			...provided,
			color: theme.colors.apInputColor,
			backgroundColor: theme.colors.apBackgroundColor
		}),
		control: (provided, state: ControlProps<CustomSelectOption, false>) => ({
			...provided,
			"borderRadius": "10px",
			"cursor": "pointer",
			"padding": "calc(1rem - 5px) 1rem",
			"transition": "border-color .15s ease-in-out,box-shadow .15s ease-in-out",
			"boxShadow": state.isFocused ? `0 0 0 0.25rem ${theme.colors.inputBorderBoxShadow}` : "none",
			"border": state.isFocused ? `1px solid ${theme.colors.inputBorderColor}` : "1px solid transparent",
			"&:hover": {
				border: state.isFocused ? `1px solid ${theme.colors.inputBorderColor}` : "1px solid transparent"
			},
			backgroundColor: theme.colors.apBackgroundColor,
			color: theme.colors.apInputColor
		}),
		indicatorSeparator: () => ({ display: "none" })
	});
};

export const CustomSelectItem: React.FC<CustomSelectItemProps> = ({
	_id,
	title,
	subtext,
	readonly = false,
	onRemove
}) => {
	return (
		<SelectItem readonly={readonly}>
			<SelectItemTitle>{title}</SelectItemTitle>
			<SelectItemContainer>
				<SelectItemSubtext>{subtext}</SelectItemSubtext>
				{!readonly && (
					<RemoveIcon
						onClick={() => {
							if (onRemove) {
								onRemove(_id);
							}
						}}
					/>
				)}
			</SelectItemContainer>
		</SelectItem>
	);
};
