import "@testing-library/jest-dom";
import React from "react";
import { render, fireEvent, waitFor, act } from "@testing-library/react";
import InputDevicesSelect from "../InputDevicesSelect";
import { ThemeProvider } from "styled-components";
import apTheme from "@src/config/theme";
import { LanguageContextProvider } from "../../contexts/LanguageContext";

jest.mock("../../hooks/translations", () => ({
	useTranslation: () => ({
		general: {
			inputDevices: "Input Devices",
			camera: "Camera",
			microphone: "Microphone",
			screen: "Screen"
		}
	})
}));

jest.mock("../../svg/SVG", () => {
	return {
		__esModule: true,
		default: function MockSVG({ src }: { src: string }) {
			return <div data-testid={`svg-${src}`} />;
		}
	};
});

jest.mock("@src/assets", () => ({
	HelpIcon: "help-icon.svg",
	MicIcon: "mic-icon.svg"
}));

const renderWithProviders = (ui: React.ReactElement) =>
	render(
		<ThemeProvider theme={apTheme}>
			<LanguageContextProvider>
				{ui}
			</LanguageContextProvider>
		</ThemeProvider>
	);

class MockMediaStreamTrack {
	kind: string;
	label: string;

	constructor(kind: string, label: string = "") {
		this.kind = kind;
		this.label = label;
	}

	stop = jest.fn();
	addEventListener = jest.fn();
}

class MockMediaStream {
	tracks: MockMediaStreamTrack[] = [];

	constructor(tracks: MockMediaStreamTrack[] = []) {
		this.tracks = tracks;
	}

	getTracks = jest.fn(() => this.tracks);
	getVideoTracks = jest.fn(() => this.tracks.filter(t => t.kind === "video"));
}

const mockVideoDevices: MediaDeviceInfo[] = [
	{
		deviceId: "video-device-1",
		kind: "videoinput",
		label: "Camera 1",
		groupId: "group-1"
	} as MediaDeviceInfo,
	{
		deviceId: "video-device-2", 
		kind: "videoinput",
		label: "Camera 2",
		groupId: "group-2"
	} as MediaDeviceInfo
];

const mockAudioDevices: MediaDeviceInfo[] = [
	{
		deviceId: "audio-device-1",
		kind: "audioinput", 
		label: "Microphone 1",
		groupId: "group-3"
	} as MediaDeviceInfo,
	{
		deviceId: "audio-device-2",
		kind: "audioinput",
		label: "Microphone 2", 
		groupId: "group-4"
	} as MediaDeviceInfo
];

describe("InputDevicesSelect", () => {
	let mockGetUserMedia: jest.Mock;
	let mockGetDisplayMedia: jest.Mock;
	let mockEnumerateDevices: jest.Mock;
	let mockVideoStream: MockMediaStream;
	let mockAudioStream: MockMediaStream;
	let mockScreenStream: MockMediaStream;

	const defaultProps = {
		disabled: false,
		videoStream: undefined,
		setVideoStream: jest.fn(),
		audioStream: undefined,
		setAudioStream: jest.fn(),
		screenStream: undefined,
		setScreenStream: jest.fn()
	};

	beforeEach(() => {
		jest.clearAllMocks();

		mockVideoStream = new MockMediaStream([new MockMediaStreamTrack("video", "Camera 1")]) as any;
		mockAudioStream = new MockMediaStream([new MockMediaStreamTrack("audio", "Microphone 1")]) as any;
		mockScreenStream = new MockMediaStream([new MockMediaStreamTrack("video", "screen:0:0")]) as any;

		mockGetUserMedia = jest.fn();
		mockGetDisplayMedia = jest.fn();
		mockEnumerateDevices = jest.fn();

		Object.defineProperty(global.navigator, "mediaDevices", {
			value: {
				getUserMedia: mockGetUserMedia,
				getDisplayMedia: mockGetDisplayMedia,
				enumerateDevices: mockEnumerateDevices
			},
			writable: true
		});

		// Initial video and audio permission requests
		mockGetUserMedia
			.mockResolvedValueOnce(mockVideoStream)
			.mockResolvedValueOnce(mockAudioStream)
			.mockResolvedValueOnce(mockVideoStream)
			.mockResolvedValueOnce(mockAudioStream);

		mockEnumerateDevices
			.mockResolvedValue([...mockVideoDevices, ...mockAudioDevices]);
	});

	afterEach(() => {
		jest.restoreAllMocks();
	});

	it("asks permission for video and microphone upon first load", async () => {
		await act(async () => {
			renderWithProviders(<InputDevicesSelect {...defaultProps} />);
		});

		expect(mockGetUserMedia).toHaveBeenCalledWith({ video: true });
		expect(mockGetUserMedia).toHaveBeenCalledWith({ audio: true });
		expect(mockGetUserMedia).toHaveBeenCalledWith({ video: { deviceId: "video-device-1" } });
		expect(mockGetUserMedia).toHaveBeenCalledWith({ audio: { deviceId: "audio-device-1" } });
		expect(mockGetUserMedia).toHaveBeenCalledTimes(4);
		expect(mockEnumerateDevices).toHaveBeenCalledTimes(2);
	});

	it("changes video stream when a different video device is selected", async () => {
		const setVideoStream = jest.fn();
		const newVideoStream = new MockMediaStream([new MockMediaStreamTrack("video", "Camera 2")]) as any;

		mockGetUserMedia.mockResolvedValueOnce(newVideoStream);

		const { getByDisplayValue } = await act(async () => {
			return renderWithProviders(
				<InputDevicesSelect
					{...defaultProps}
					setVideoStream={setVideoStream}
				/>
			);
		});

		const videoSelect = getByDisplayValue("Camera 1");
		await act(async () => {
			fireEvent.change(videoSelect, { target: { value: "video-device-2" } });
		});

		expect(mockGetUserMedia).toHaveBeenCalledWith({
			video: { deviceId: "video-device-2" }
		});
		expect(setVideoStream).toHaveBeenCalledWith(newVideoStream);
	});

	it("changes audio stream when a different audio device is selected", async () => {
		const setAudioStream = jest.fn();
		const newAudioStream = new MockMediaStream([new MockMediaStreamTrack("audio", "Microphone 2")]) as any;

		mockGetUserMedia.mockResolvedValueOnce(newAudioStream);

		const { getByDisplayValue } = await act(async () => {
			return renderWithProviders(
				<InputDevicesSelect
					{...defaultProps}
					setAudioStream={setAudioStream}
				/>
			);
		});

		const audioSelect = getByDisplayValue("Microphone 1");
		await act(async () => {
			fireEvent.change(audioSelect, { target: { value: "audio-device-2" } });
		});

		expect(mockGetUserMedia).toHaveBeenCalledWith({
			audio: { deviceId: "audio-device-2" }
		});
		expect(setAudioStream).toHaveBeenCalledWith(newAudioStream);
	});

	it("asks permission for screen share when select button is clicked", async () => {
		const setScreenStream = jest.fn();
		mockGetDisplayMedia.mockResolvedValueOnce(mockScreenStream);

		const { getByText } = await act(async () => {
			return renderWithProviders(
				<InputDevicesSelect
					{...defaultProps}
					setScreenStream={setScreenStream}
				/>
			);
		});

		const selectButton = getByText("Select");
		await act(async () => {
			fireEvent.click(selectButton);
		});

		expect(mockGetDisplayMedia).toHaveBeenCalledWith({ video: true });
		expect(setScreenStream).toHaveBeenCalledWith(mockScreenStream);
	});

	it("frees active screenStream when 'stop sharing' button is clicked", async () => {
		const setScreenStream = jest.fn();
		const existingScreenStream = new MockMediaStream([new MockMediaStreamTrack("video", "screen:0:0")]) as any;

		const { getByText } = await act(async () => {
			return renderWithProviders(
				<InputDevicesSelect
					{...defaultProps}
					screenStream={existingScreenStream}
					setScreenStream={setScreenStream}
				/>
			);
		});

		const stopButton = getByText("Stop Sharing");
		await act(async () => {
			fireEvent.click(stopButton);
		});

		expect(existingScreenStream.getTracks()[0].stop).toHaveBeenCalled();
		expect(setScreenStream).toHaveBeenCalledWith(undefined);
	});

	it("frees all active streams on dismount", async () => {
		const existingVideoStream = new MockMediaStream([new MockMediaStreamTrack("video", "Camera 1")]) as any;
		const existingAudioStream = new MockMediaStream([new MockMediaStreamTrack("audio", "Microphone 1")]) as any;
		const existingScreenStream = new MockMediaStream([new MockMediaStreamTrack("video", "screen:0:0")]) as any;

		const { unmount } = await act(async () => {
			return renderWithProviders(
				<InputDevicesSelect
					{...defaultProps}
					videoStream={existingVideoStream}
					audioStream={existingAudioStream}
					screenStream={existingScreenStream}
				/>
			);
		});

		await act(async () => {
			unmount();
		});

		expect(existingVideoStream.getTracks()[0].stop).toHaveBeenCalled();
		expect(existingAudioStream.getTracks()[0].stop).toHaveBeenCalled();
		expect(existingScreenStream.getTracks()[0].stop).toHaveBeenCalled();
	});
});
