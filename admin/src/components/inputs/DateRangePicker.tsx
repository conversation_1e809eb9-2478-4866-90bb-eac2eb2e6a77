/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from "react";
import { useTranslation } from "../hooks/translations";
import { DateRange, InlineMetricIcon, StyledDatePickerWrapper } from "@src/styles/components";
import { formatDate } from "@src/utils/dates";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { faCalendarDays } from "@fortawesome/free-solid-svg-icons";
import SVG from "../svg/SVG";

interface DateRangePickerProps {
	compact?: boolean;
	startDate: Date;
	setStartDate: (date: Date) => void;
	endDate: Date;
	setEndDate: (date: Date) => void;
	setNewStartDate: (date: Date) => void;
	setNewEndDate: (date: Date) => void;
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({ compact, startDate, setStartDate, endDate, setEndDate, setNewStartDate, setNewEndDate }) => {
	const translation = useTranslation();
	const [datePickerVisible, setDatePickerVisible] = useState(false);
	return (
		<div style={{ position: "relative" }}>
			<DateRange onClick={() => setDatePickerVisible(!datePickerVisible)} style={compact ? { marginTop: "1rem" } : {}}>
				{!compact && (
					<>
						<InlineMetricIcon>
							<SVG src={faCalendarDays} />
						</InlineMetricIcon>
						{translation.performancePage.dateRangeHeading}
					</>
				)}
				<div style={{ fontWeight: "bold", width: "11rem" }}>
					{startDate && formatDate(startDate)} - {endDate && formatDate(endDate)}
				</div>
			</DateRange>
			{datePickerVisible && (
				<StyledDatePickerWrapper>
					<DatePicker
						selected={startDate}
						onChange={(dates: any) => {
							const [start, end] = dates;
							setStartDate(start);
							setEndDate(end);

							if (end) {
								setNewStartDate(start);
								const adjustedEndDate = new Date(end.toString());
								adjustedEndDate.setHours(23, 59, 59, 59);
								setNewEndDate(adjustedEndDate);
								setDatePickerVisible(false);
							}
						}}
						startDate={startDate}
						endDate={endDate}
						selectsRange
						inline
						showMonthDropdown
						showYearDropdown
					/>
				</StyledDatePickerWrapper>
			)}
		</div>
	);
};

export default DateRangePicker;
