import React, { useState } from "react";
import { Form, Row, Col } from "react-bootstrap";
import axios from "axios";
import { useTranslation } from "../hooks/translations";
import { getErrorString } from "../utils/getErrorString";
import { PageBody, PageRow, MainButton } from "@src/styles/components";
import {
	HeadingText,
	BodyText,
	CustomInput,
	ConfirmationBox,
	ConfirmationBoxWrapper,
	EyeIcon,
	EyeSlashIcon
} from "@src/styles/forms";
import { AxiosError } from "@src/types/axios";
import ErrorMessage from "@src/components/utils/ErrorMessage";
import { useNavigate } from "react-router-dom";
import jwt_decode from "jwt-decode";
import { accessToken } from "@src/types/videos";

const AddAccountForm: React.FC = () => {
	const translation = useTranslation();
	const [userError, setUserError] = useState("");
	const [validated, setValidated] = useState(false);
	const [showPassword, setShowPassword] = useState(false);
	const [formPassword, setFormPassword] = useState("");
	const [formEmail, setFormEmail] = useState("");
	const [loading, setLoading] = useState(false);
	const [confirmationText, setConfirmationText] = useState("");
	const navigate = useNavigate();

	const accessToken = localStorage.getItem("accessToken");
	if (accessToken) {
		const decoded = jwt_decode(accessToken) as accessToken;
		if (!decoded.super) {
			navigate("/");
		}
	}

	interface SignUpRequest {
		email: string;
		password: string;
		locale: string;
		legalAgreement: boolean;
		callbackEndpoint: string | undefined;
	}

	const submitForm = async (event: React.FormEvent<HTMLFormElement>) => {
		setLoading(true);
		event.preventDefault();
		const form = event.currentTarget;
		if (form.checkValidity() === false) {
			event.stopPropagation();
			setValidated(true);
			setLoading(false);
		} else {
			try {
				const API_ENDPOINT = process.env.API_ENDPOINT;
				const CMS_ENDPOINT = process.env.CMS_ENDPOINT;
				const API_VERSION = process.env.API_VERSION;

				const email = (form.elements.namedItem("formEmail") as HTMLInputElement).value;
				const password = (form.elements.namedItem("formPassword") as HTMLInputElement).value;

				const requestObject: SignUpRequest = {
					email: email,
					password: password,
					locale: translation.locale,
					legalAgreement: true,
					callbackEndpoint: CMS_ENDPOINT
				};

				const returnObject = await axios.request({
					url: `${API_ENDPOINT}/api/auth/signup/enterprise`,
					method: "POST",
					data: JSON.stringify(requestObject),
					headers: {
						Authorization: `Bearer ${accessToken}`,
						"content-type": "application/json",
						"x-api-version": API_VERSION
					}
				});

				if (returnObject?.data?.accessToken && returnObject?.data?.refreshToken) {
					setUserError("");
					setFormPassword("");
					setFormEmail("");
					setConfirmationText(translation.addAccountPage.accountCreated);
					setTimeout(() => {
						setConfirmationText("");
						setLoading(false);
					}, 1000);
				}
			} catch (error: unknown) {
				setValidated(false);
				setLoading(false);
				setFormPassword("");
				setFormEmail("");
				if ((error as AxiosError).response) {
					const axiosError = error as AxiosError;
					setUserError(getErrorString(translation, axiosError?.response?.data?.error));
				}
			}
		}
	};

	return (
		<>
			<ConfirmationBoxWrapper>
				{!!confirmationText && (
					<ConfirmationBox className="text-center" data-testid="deleteConfirmation">
						{confirmationText}
					</ConfirmationBox>
				)}
				{!!userError && <ErrorMessage error={userError} setError={setUserError} displayCloseIcon={true} />}
			</ConfirmationBoxWrapper>

			<PageBody>
				<PageRow className="mb-4">
					<HeadingText data-testid="editVideoPage">{translation.addAccountPage.pageTitle}</HeadingText>
				</PageRow>
				<Row pl="0" pr="0">
					<Col sm="12" md="4">
						<BodyText data-testid="createAccountSubText">{translation.addAccountPage.createUser}</BodyText>

						<Form
							className="mb-4 mt-4"
							noValidate
							onSubmit={submitForm}
							validated={validated}
							data-testid="CreateAccountSection"
						>
							<Form.Group className="mb-3">
								<CustomInput
									type="email"
									required
									pattern="[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,20}$"
									className="form-control"
									placeholder={translation.createAccountPage.businessEmail}
									onInput={(e: React.ChangeEvent<HTMLInputElement>) =>
										(e.target.value = ("" + e.target.value).toLowerCase())
									}
									onChange={(value) => setFormEmail(value.target.value)}
									value={formEmail}
									autoComplete="username"
									id="formEmail"
									data-testid="formEmail"
								/>
								<Form.Control.Feedback type="invalid" data-testid="emailTextError">
									{translation.errors.emailFormat}
								</Form.Control.Feedback>
							</Form.Group>
							<Form.Group className="mb-3">
								<CustomInput
									type={showPassword ? "text" : "password"}
									required
									pattern="^.{8,}$"
									className="form-control"
									style={{ paddingRight: "60px" }}
									placeholder={translation.general.password}
									onChange={(value) => setFormPassword(value.target.value)}
									value={formPassword}
									autoComplete="current-password"
									id="formPassword"
									data-testid="formPassword"
								/>
								{showPassword ? (
									<EyeSlashIcon
										data-testid="EyeSlashIcon"
										onClick={() => {
											setShowPassword(false);
										}}
									/>
								) : (
									<EyeIcon
										data-testid="EyeIcon"
										onClick={() => {
											setShowPassword(true);
										}}
									/>
								)}
								<Form.Control.Feedback type="invalid" data-testid="passwordTextError">
									{translation.errors.passwordFormat}
								</Form.Control.Feedback>
							</Form.Group>
							<MainButton type="submit" className="mt-3" data-testid="createAccountSubmit" disabled={loading}>
								{translation.general.createAccount}
							</MainButton>
						</Form>
					</Col>
				</Row>
			</PageBody>
		</>
	);
};

export default AddAccountForm;
