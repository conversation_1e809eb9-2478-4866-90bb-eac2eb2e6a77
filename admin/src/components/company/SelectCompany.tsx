import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Skeleton from "react-loading-skeleton";
import {
	Flex,
	ListBox,
	ListItemContainer,
	ListRowItem,
	CompanySection,
	CompanyImage,
	InitialsBox,
	PageBody,
	LinkSpan,
	InvitePending,
	InviteActions,
	SelectorRow,
	LetterButton,
	ListRowFlex
} from "@src/styles/components";
import { HeadingText, ConfirmationBoxWrapper, BodyText, AngleRight, AcceptInvite, RejectInvite } from "@src/styles/forms";
import { useTranslation } from "../hooks/translations";
import getCompanies from "../utils/getCompanies";
import getAccessToken from "../utils/getAccessToken";
import { useTokenCheck } from "../hooks/useTokenCheck";
import { accountsList, Accounts, accessToken, SearchType } from "@src/types/videos";
import getInvites from "../utils/getInvites";
import { Invitation } from "@src/types/invitations";
import acceptInvite from "../utils/acceptInvite";
import rejectInvite from "../utils/rejectInvite";
import jwt_decode from "jwt-decode";
import getAllCompanies from "../utils/getAllCompanies";
import ErrorMessage from "@src/components/utils/ErrorMessage";

// eslint-disable-next-line max-lines-per-function
const SelectCompany: React.FC<{profileView?: boolean}> = ({ profileView = false }) => {
	const navigate = useNavigate();
	const translation = useTranslation();
	const { apiRetryHandler } = useTokenCheck();
	const [loadingError, setLoadingError] = useState("");
	const [refetch, setRefetch] = useState(true);
	const [refetchList, setRefetchList] = useState(true);
	const [firstName, setFirstName] = useState("");
	const [companiesList, setCompaniesList] = useState<accountsList>([] as accountsList);
	const [invitesList, setInvitesList] = useState<Invitation[]>([] as Invitation[]);
	const [groupedInvitesList, setGroupedInvitesList] = useState<{[key: string]: Invitation[]}>({});
	const [displayPage, setDisplayPage] = useState(false);
	const [isSuperAdmin, setIsSuperAdmin] = useState(false);
	const [allCompanies, setAllCompanies] = useState<Accounts[]>();
	const [selectedGroup, setSelectedGroup] = useState<string>(translation.companyPage.selectedCharGroup);
	const [searchType, setSearchType] = useState(SearchType.SEARCH_PREFIX);
	const [maxCompanies, setMaxCompanies] = useState(1);

	const getCompany = async (id: string) => {
		const { error } = await apiRetryHandler(async () => await getAccessToken(id));

		if (error) {
			setLoadingError(translation.companyPage.loadingIssue);
		} else {
			navigate("/");
		}
	};

	const getListByCharacter = async (character: string, type: SearchType) => {
		setSelectedGroup(character);
		setSearchType(type);
		setRefetchList(true);
	};

	const handleAcceptInvite = async (inviteId: string) => {
		await apiRetryHandler(async () => await acceptInvite(inviteId));
		setRefetch(true);
		setRefetchList(true);
	};

	const handleRejectInvite = async (inviteId: string) => {
		await apiRetryHandler(async () => await rejectInvite(inviteId));
		setRefetch(true);
		setRefetchList(true);
	};

	useEffect(() => {
		const go = async () => {
			const accessToken = localStorage.getItem("accessToken");
			if (accessToken && !profileView) {
				const decoded = jwt_decode(accessToken) as accessToken;
				if (decoded.super) {
					setIsSuperAdmin(true);
					const { data, error } = await apiRetryHandler(async () => await getAllCompanies(selectedGroup, searchType));
					if (error) {
						setLoadingError(translation.companyPage.loadingIssue);
					} else {
						const companies: Accounts[] = data.accounts;
						companies.sort((a, b) => (a.companyName || "").localeCompare(b.companyName || ""));
						setAllCompanies(companies);
					}
				}
			}
			setRefetchList(false);
		};
		if (refetchList) {
			go();
		}
	}, [apiRetryHandler, profileView, refetchList, selectedGroup, translation.companyPage.loadingIssue, searchType]);

	useEffect(() => {
		function classifyInvitation(invitation: Invitation): string | null {
			const firstChar = invitation.account?.[0]?.companyName?.[0];
			if (!firstChar) return null;
			if (/[a-z]/i.test(firstChar)) return firstChar.toUpperCase();
			if (/\d/.test(firstChar)) return translation.companyPage.numbers;
			return translation.companyPage.specialChars;
		}

		const go = async () => {
			const { data, error } = await apiRetryHandler(async () => await getCompanies());
			if (localStorage.getItem("setToken") && data?.accounts?.accounts?.length === 1) {
				localStorage.removeItem("setToken");
				const { error } = await getAccessToken(data?.accounts?.accounts?.[0]?._id);
				if (!error) {
					const redirectUrl = sessionStorage.getItem("redirectAfterCompanySelection") || "/";
					sessionStorage.removeItem("redirectAfterCompanySelection");
					return navigate(redirectUrl);
				}
			}

			setDisplayPage(true);

			const { data: inviteData, error: inviteError } = await apiRetryHandler(async () => await getInvites());

			if (error || inviteError) {
				setLoadingError(translation.companyPage.loadingIssue);
			} else {
				if (Array.isArray(inviteData)) {
					const groupedInvitations = inviteData.reduce<{[key: string]: Invitation[]}>((acc, invitation: Invitation) => {
						const group = classifyInvitation(invitation);
						if (!group) return acc;
						if (!acc[group]) acc[group] = [];
						acc[group].push(invitation);
						return acc;
					}, {});

					for (const key in groupedInvitations) {
						groupedInvitations[key].sort((a, b) =>
							(a.account?.[0].companyName || "").localeCompare(b.account?.[0].companyName || "")
						);
					}

					setGroupedInvitesList(groupedInvitations);
				}

				setCompaniesList(data?.accounts);
				setFirstName(data?.firstName);
				setInvitesList(inviteData);
				setMaxCompanies(data?.maxCompanies);
			}
			setRefetch(false);
		};

		if (refetch) {
			go();
		}
	}, [
		refetch,
		apiRetryHandler,
		translation.companyPage.loadingIssue,
		navigate,
		translation.companyPage.numbers,
		translation.companyPage.specialChars
	]);

	return (
		<>
			<ConfirmationBoxWrapper>
				{!!loadingError && <ErrorMessage error={loadingError} setError={setLoadingError} displayCloseIcon={true} />}
			</ConfirmationBoxWrapper>
			{!profileView && isSuperAdmin ? (
				<PageBody>
					{refetch ? (
						<>
							<CompanySection>
								<Skeleton count={10} height={50} />
							</CompanySection>
						</>
					) : (
						<>
							<HeadingText data-testid="companyPageWelcome" className="mb-3" style={{ textAlign: "center" }}>
								{translation.companyPage.welcome} {firstName},
							</HeadingText>
							<BodyText data-testid="companyPageText" className="mb-4" style={{ textAlign: "center" }}>
								{translation.companyPage.selectCompany}
							</BodyText>

							<Flex style={{ justifyContent: "center" }}>
								<SelectorRow>
									{translation.companyPage.alphabet.split("").map((letter) => (
										<LetterButton
											key={letter}
											onClick={() => getListByCharacter(letter, SearchType.SEARCH_PREFIX)}
											isSelected={selectedGroup === letter}
										>
											{letter}
										</LetterButton>
									))}
									<LetterButton
										onClick={() => getListByCharacter("", SearchType.SEARCH_ALL_NUMBER)}
										isSelected={selectedGroup === translation.companyPage.numbers}
									>
										{translation.companyPage.numbers}
									</LetterButton>
									<LetterButton
										onClick={() => getListByCharacter("", SearchType.SEARCH_ALL_SPECIAL)}
										isSelected={selectedGroup === translation.companyPage.specialChars}
									>
										{translation.companyPage.specialChars}
									</LetterButton>
								</SelectorRow>
							</Flex>

							{refetchList ? (
								<PageBody>
									<Skeleton count={10} height={50} />
								</PageBody>
							) : (
								allCompanies && (
									<ListRowFlex>
										{allCompanies?.map((account, index) => (
											<ListItemContainer
												key={"listIndex" + index}
												className="mb-3 ms-3"
												style={{ width: "300px", borderRadius: "10px", cursor: !profileView ? "pointer" : "initial" }}
												onClick={() => getCompany(account._id)}
											>
												<Flex>
													<ListBox style={{ width: "20%" }} clickable={false}>
														{account.companyLogo ? (
															<CompanyImage src={account.companyLogo} />
														) : (
															<InitialsBox>{account.companyName && account.companyName.charAt(0)}</InitialsBox>
														)}
													</ListBox>
													<ListBox style={{ width: "70%" }} clickable={false}>
														<ListRowItem
															style={{
																whiteSpace: "nowrap",
																width: "13rem",
																overflow: "hidden",
																textOverflow: "ellipsis"
															}}
														>
															{" "}
															{account.companyName}{" "}
														</ListRowItem>
													</ListBox>
													<ListBox style={{ width: "10%" }} clickable={false} last={true}>
														<ListRowItem>
															<AngleRight />
														</ListRowItem>
													</ListBox>
												</Flex>
											</ListItemContainer>
										))}
										{groupedInvitesList[selectedGroup]?.map((invite, index) => (
											<ListItemContainer
												key={"listIndex" + index}
												className="mb-3 ms-3"
												style={{ width: "300px", borderRadius: "10px", cursor: !profileView ? "pointer" : "initial" }}
											>
												<Flex>
													<ListBox style={{ width: "20%" }} clickable={false}>
														{invite.account?.[0]?.companyLogo ? (
															<CompanyImage src={invite.account[0].companyLogo} />
														) : (
															<InitialsBox>
																{invite.account?.[0].companyName && invite.account?.[0].companyName.charAt(0)}
															</InitialsBox>
														)}
													</ListBox>
													<ListBox style={{ width: "70%" }} clickable={false}>
														<ListRowItem style={{ paddingTop: "5px", paddingBottom: "5px" }}>
															<div
																style={{
																	whiteSpace: "nowrap",
																	width: "8rem",
																	overflow: "hidden",
																	textOverflow: "ellipsis"
																}}
															>
																{invite.account?.[0].companyName}
															</div>
															<InvitePending>{translation.general.invitePending}</InvitePending>
														</ListRowItem>
													</ListBox>
													<ListBox style={{ width: "10%" }} clickable={false} last={true}>
														<ListRowItem>
															<InviteActions>
																<span onClick={() => handleAcceptInvite(invite._id)}>
																	<AcceptInvite />
																</span>
																<span onClick={() => handleRejectInvite(invite._id)}>
																	<RejectInvite />
																</span>
															</InviteActions>
														</ListRowItem>
													</ListBox>
												</Flex>
											</ListItemContainer>
										))}
									</ListRowFlex>
								)
							)}
						</>
					)}
				</PageBody>
			) : (
				<PageBody style={{ paddingTop: `${profileView && "0"}`, display: displayPage ? "initial" : "none" }}>
					{refetch ? (
						<CompanySection>
							<Skeleton count={10} height={50} />
						</CompanySection>
					) : (
						<CompanySection style={{ margin: `${profileView && "0"}`, marginTop: `${profileView && "-1.5rem"}` }}>
							{!profileView && (
								<>
									<HeadingText data-testid="companyPageWelcome" className="mb-3">
										{translation.companyPage.welcome} {firstName},
									</HeadingText>
									<BodyText data-testid="companyPageText" className="mb-4">
										{translation.companyPage.selectCompany}
									</BodyText>
								</>
							)}

							{companiesList?.accounts?.map((account: Accounts, index: number) => (
								<ListItemContainer
									key={"listIndex" + index}
									className="mb-3"
									style={{ borderRadius: "10px", cursor: "pointer" }}
									onClick={() => getCompany(account._id)}
								>
									<Flex>
										<ListBox style={{ width: "20%" }} clickable={false}>
											{account.companyLogo ? (
												<CompanyImage src={account.companyLogo} />
											) : (
												<InitialsBox>{account.companyName && account.companyName.charAt(0)}</InitialsBox>
											)}
										</ListBox>
										<ListBox style={{ width: "70%" }} clickable={false}>
											<ListRowItem> {account.companyName} </ListRowItem>
										</ListBox>
										<ListBox style={{ width: "10%" }} clickable={false} last={true}>
											<ListRowItem>
												<AngleRight />
											</ListRowItem>
										</ListBox>
									</Flex>
								</ListItemContainer>
							))}

							{!profileView &&
								invitesList?.map((invite: Invitation, index: number) => (
									<ListItemContainer key={"InvListIndex" + index} className="mb-3" style={{ borderRadius: "10px" }}>
										<Flex>
											<ListBox style={{ width: "20%" }} clickable={false}>
												{invite.account?.[0]?.companyLogo ? (
													<CompanyImage src={invite.account[0].companyLogo} />
												) : (
													<InitialsBox>
														{invite.account?.[0].companyName && invite.account?.[0].companyName.charAt(0)}
													</InitialsBox>
												)}
											</ListBox>
											<ListBox style={{ width: "70%" }} clickable={false}>
												<ListRowItem style={{ paddingTop: "5px", paddingBottom: "5px" }}>
													<div>{invite.account?.[0].companyName}</div>
													<InvitePending>{translation.general.invitePending}</InvitePending>
												</ListRowItem>
											</ListBox>
											<ListBox style={{ width: "10%" }} clickable={false} last={true}>
												<ListRowItem>
													<InviteActions>
														<span onClick={() => handleAcceptInvite(invite._id)}>
															<AcceptInvite />
														</span>
														<span onClick={() => handleRejectInvite(invite._id)}>
															<RejectInvite />
														</span>
													</InviteActions>
												</ListRowItem>
											</ListBox>
										</Flex>
									</ListItemContainer>
								))}

							{(companiesList.accounts?.length ?? 0) < maxCompanies && (
								<BodyText data-testid="addCompany" className="mt-3">
									<LinkSpan onClick={() => navigate("/add-company")}>
										{translation.companyPage.addCompany}
									</LinkSpan>
								</BodyText>
							)}
						</CompanySection>
					)}
				</PageBody>
			)}
		</>
	);
};

export default SelectCompany;
