import React, {
	useRef,
	useEffect,
	useState,
	RefObject
} from "react";
import { useTranslation } from "../hooks/translations";
import { BaseModal } from "./BaseModal";
import {
	BodyText,
	XmarkIcon
} from "@src/styles/forms";
import { Form } from "react-bootstrap";
import {
	Flex,
	MainButton,
	LinkButton,
	CaptionsRightColumn,
	EditCaptionsArea,
	ColorBoxContainer,
	ColorBox,
	ColorInput,
	EyeDropperIcon
} from "@src/styles/components";
import { shoppableVideoData } from "@src/types/videos";
import { CaptionData } from "@src/types/caption";
import { useMediaQuery } from "react-responsive";
import { VideoPreviewProps } from "@src/types/snippetOptions";
import { HexAlphaColorPicker } from "react-colorful";
import VideoPreview from "../videos/VideoPreview";
import { handleEyeDropPick } from "@src/utils/colors";

interface Props {
	visible: boolean;
	onCancel: () => void;
	captionData?: CaptionData;
	setCaptionData: (value: CaptionData) => void;
	videoDataCopy?: shoppableVideoData;
	setVideoDataCopy?: (value: shoppableVideoData) => void;
	videoPreviewData: VideoPreviewProps;
}

// eslint-disable-next-line max-lines-per-function
export const CaptionsModal: React.FC<Props> = ({
	visible,
	onCancel,
	captionData,
	setCaptionData,
	videoDataCopy,
	setVideoDataCopy,
	videoPreviewData
}) => {
	const translation = useTranslation();
	const [videoCurrentTime, setVideoCurrentTime] = useState(0);
	const [videoDuration, setVideoDuration] = useState(0);
	const isScreenSmall = useMediaQuery({ query: "(max-width: 785px)" });
	const [isPickerVisible, setPickerVisible] = useState(false);
	const [isTextColorVisible, setIsTextColorVisible] = useState(false);
	const [activeCaptionIndex, setActiveCaptionIndex] = useState<number | null>(null);
	const captionDataOG = useRef<CaptionData | undefined>();
	const [captionDataCopy, setCaptionDataCopy] = useState<CaptionData | undefined>();
	const [captionCopied, setCaptionCopied] = useState(false);
	const [receivedDuration, setReceivedDuration] = useState(false);

	const intervalRef = useRef<NodeJS.Timeout>();
	const iframeRef = useRef<HTMLIFrameElement>();
	const setIFrameRef = (iframe: HTMLIFrameElement): void => {
		iframeRef.current = iframe;
	};

	useEffect(() => {
		const receiveTimestamp = (event: MessageEvent) => {
			if (event.origin !== process.env.APP_ENDPOINT) return;
			if (event.data && event.data.type === "current_timestamp") {
				setVideoCurrentTime(event.data.value);
			}
		};

		if (visible) {
			window.addEventListener("message", receiveTimestamp);
			getVideoDuration();

			intervalRef.current = setInterval(() => {
				iframeRef.current?.contentWindow?.postMessage(
					{ type: "get_current_timestamp" },
					"*"
				);
			}, 500);
		} else {
			clearInterval(intervalRef.current);
		}

		return () => {
			clearInterval(intervalRef.current);
			window.removeEventListener("message", receiveTimestamp);
		};
	}, [visible]);

	const secondsToMinute = (seconds: number) => {
		let s = Math.round(seconds);
		return (s - (s %= 60)) / 60 + (9 < s ? ":" : ":0") + s;
	};

	const useOutsideClick = (ref: RefObject<HTMLElement>, callback: () => void) => {
		useEffect(() => {
			function handleClickOutside(event: MouseEvent) {
				if (ref.current && !ref.current.contains(event.target as Node)) {
					callback();
				}
			}
			document.addEventListener("mousedown", handleClickOutside);
			return () => {
				document.removeEventListener("mousedown", handleClickOutside);
			};
		}, [ref, callback]);
	};

	const updateCaptions = (captionDataToSet: CaptionData | undefined) => {

		pauseVideo();

		if (videoDataCopy && setVideoDataCopy) {
			setVideoDataCopy({
				...videoDataCopy,
				shoppableVideo: {
					...videoDataCopy.shoppableVideo,
					captionData: captionDataToSet
				}
			});
		}
		if (captionDataToSet) {
			setCaptionData(captionDataToSet);
		}
	};

	const handleSaveCaptions = () => {
		updateCaptions(captionDataCopy);
		onCancel();
	};

	const handleAbandonCaptions = () => {
		updateCaptions(captionDataOG.current);
		onCancel();
	};

	const modalRef = useRef(null);
	const bgPickerRef = useRef(null);
	const textPickerRef = useRef(null);

	useOutsideClick(bgPickerRef, () => setPickerVisible(false));
	useOutsideClick(textPickerRef, () => setIsTextColorVisible(false));
	useOutsideClick(modalRef, handleAbandonCaptions);

	const deepCopyCaptionData = (data: CaptionData): CaptionData => ({
		...data,
		captionText: data.captionText.map(caption => ({ ...caption }))
	});

	useEffect(() => {
		if (visible && !captionCopied && captionData) {
			captionDataOG.current = deepCopyCaptionData(captionData);
			setCaptionDataCopy(deepCopyCaptionData(captionData));
			setCaptionCopied(true);
		}
	}, [captionData, captionCopied, visible]);

	useEffect(() => {
		if (!visible) {
			setCaptionCopied(false);
		}
		setVideoCurrentTime(0);
	}, [visible]);

	const pauseVideo = () => {
		iframeRef.current?.contentWindow?.postMessage(
			{ type: "pause_video" },
			"*"
		);
	};

	const getVideoDuration = () => {
		const receiveDuration = (event: MessageEvent) => {
			if (event.origin !== process.env.APP_ENDPOINT) return;
			if (event.data && event.data.type === "video_duration" && event.data.value > 0) {
				setVideoDuration(event.data.value);
				setReceivedDuration(true);
				window.removeEventListener("message", receiveDuration);
			} else {
				setReceivedDuration(false);
			}
		};
		window.addEventListener("message", receiveDuration);

		iframeRef.current?.contentWindow?.postMessage(
			{ type: "get_video_duration" },
			"*"
		);
	};

	useEffect(() => {
		if (!visible || receivedDuration) return;

		let count = 0;
		const interval = setInterval(() => {
			getVideoDuration();
			count++;
			if (count > 10)
				clearInterval(interval);
		}, 500);

		return () => clearInterval(interval);
	}, [visible, receivedDuration]);

	useEffect(() => {
		if (!captionDataCopy) {
			setActiveCaptionIndex(null);
			return;
		}

		const index = captionDataCopy.captionText.findIndex((caption) => videoCurrentTime >= caption.startTime && videoCurrentTime <= caption.endTime);
		setActiveCaptionIndex(index !== -1 ? index : null);
	}, [captionDataCopy, videoCurrentTime]);

	const changeCaptionsTheme = (value: string, type: string) => {
		if (!captionDataCopy || value === "") return;
		const updatedCaptions = { ...captionDataCopy };
		const safeColor = ((type === "text" || type === "background") && value.startsWith("#") && !isNaN(parseInt(value.substring(1), 16))) ? value : "#000000";

		switch (type) {
			case "text":
				updatedCaptions.textColor = safeColor;
				break;
			case "background":
				updatedCaptions.backgroundColor = safeColor;
				break;
			case "size":
				updatedCaptions.fontSize = parseFloat(value);
				break;
			default:
				return;
		}

		setCaptionDataCopy(updatedCaptions);
	};

	const updateCaptionText = (index: number, text: string) => {
		if (!captionDataCopy || !captionDataCopy.captionText[index]) return;
		const updatedCaptions = deepCopyCaptionData(captionDataCopy);
		updatedCaptions.captionText[index].text = text;
		setCaptionDataCopy(updatedCaptions);
	};

	useEffect(() => {
		updateCaptions(captionDataCopy);
	}
	// eslint-disable-next-line react-hooks/exhaustive-deps
	, [captionDataCopy]);

	return (
		<BaseModal visible={visible} wrapperRef={modalRef} wide={true} modalStyle={{ maxHeight: "100vh" }}>
			<LinkButton
				data-testid="closeButton"
				onClick={handleAbandonCaptions}
				style={{ position: "absolute", top: 0, right: "-3rem", height: "2rem", width: "2rem", borderRadius: "25px" }}
			>
				<XmarkIcon style={{ fontSize: "1.2rem", marginTop: 0, right: "10px", top: "6px" }} />
			</LinkButton>
			<Flex style={{
				flexDirection: isScreenSmall ? "column" : "row",
				gap: "50px"
			}}>
				<VideoPreview
					{...videoPreviewData}
					setIFrame={setIFrameRef}
				/>
				<Flex>
					<CaptionsRightColumn>
						<BodyText>
							<b>{secondsToMinute(videoCurrentTime)} {videoDuration > 0 ? `/ ${secondsToMinute(videoDuration)}` : ""}</b>
						</BodyText>

						{activeCaptionIndex !== null && captionDataCopy ? (
							<EditCaptionsArea
								value={captionDataCopy?.captionText[activeCaptionIndex].text}
								onFocus={pauseVideo}
								onChange={(e) => updateCaptionText(activeCaptionIndex, e.target.value)}
								rows={5}
							/>
						) : (
							<EditCaptionsArea value="" disabled={true} rows={5} />
						)}
						<>
							<div className="mt-3 mb-3" style={{ position: "relative" }}>
								{translation.modals.backgroundColor}
								<ColorBoxContainer style={{ paddingLeft: "0" }}>
									<ColorBox
										color={captionDataCopy?.backgroundColor}
										onClick={() => {
											setPickerVisible(!isPickerVisible);
										}}
									></ColorBox>
									{(window.EyeDropper) && (
										<EyeDropperIcon
											style={{ cursor: "pointer", height: "auto", marginLeft: "3px", border: "1px solid transparent" }}
											onClick={async () => {
												pauseVideo();
												changeCaptionsTheme(await handleEyeDropPick() ?? "", "background");
											}}
										/>
									)}
									<ColorInput
										type="text"
										value={captionDataCopy?.backgroundColor}
										onChange={(value) => {
											changeCaptionsTheme(value.target.value, "background");
										}}
										maxLength={9}
									/>
								</ColorBoxContainer>
								{isPickerVisible && (
									<div ref={bgPickerRef} style={{ position: "absolute", zIndex: 1 }}>
										<HexAlphaColorPicker
											color={captionDataCopy?.backgroundColor}
											onChange={(value) => {
												changeCaptionsTheme(value, "background");
											}}
											style={{ cursor: "pointer" }}
										/>
									</div>
								)}
							</div>

							<div className="mb-3" style={{ position: "relative" }}>
								{translation.modals.textColor}
								<ColorBoxContainer style={{ paddingLeft: "0" }}>
									<ColorBox
										color={captionDataCopy?.textColor}
										onClick={() => {
											setIsTextColorVisible(!isTextColorVisible);
										}}
									></ColorBox>
									{(window.EyeDropper) && (
										<EyeDropperIcon
											style={{ cursor: "pointer", height: "auto", marginLeft: "3px", border: "1px solid transparent" }}
											onClick={async () => {
												pauseVideo();
												changeCaptionsTheme(await handleEyeDropPick() ?? "", "text");
											}}
										/>
									)}
									<ColorInput
										type="text"
										value={captionDataCopy?.textColor}
										onChange={(value) => {
											changeCaptionsTheme(value.target.value, "text");
										}}
										maxLength={9}
									/>
								</ColorBoxContainer>
								{isTextColorVisible && (
									<div ref={textPickerRef} style={{ position: "absolute", zIndex: 1 }}>
										<HexAlphaColorPicker
											color={captionDataCopy?.textColor}
											onChange={(value) => {
												changeCaptionsTheme(value, "text");
											}}
											style={{ cursor: "pointer" }}
										/>
									</div>
								)}
							</div>

							<div className="mb-3">
								{translation.modals.textSize}
								<Form.Range
									value={captionDataCopy?.fontSize ?? 1}
									onChange={(e) => {
										changeCaptionsTheme(e.target.value, "size");
									}}
									min="0.7"
									max="2"
									step="0.1"
								/>
							</div>
						</>
						<MainButton
							type="button"
							onClick={() => handleSaveCaptions()}
							className="mx-auto mt-5"
							data-testid="setCaptions"
						>
							{translation.modals.setCaptions}
						</MainButton>
					</CaptionsRightColumn>
				</Flex>
			</Flex>
		</BaseModal>
	);
};
