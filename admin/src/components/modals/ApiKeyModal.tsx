import React, { useRef, useEffect, RefObject } from "react";
import { useTranslation } from "../hooks/translations";
import { BaseModal } from "./BaseModal";
import { FlexCol, MainButton, ThirdButton } from "@src/styles/components";
import { ModalText } from "@src/styles/modals";

interface Props {
	visible: boolean;
	setVisible: (value: boolean) => void;
	onCancel: () => void;
	handleSaveAPIKey: () => void;
	passwordRequired: boolean;
	setEnterPassword: (value: boolean) => void;
	loading: boolean;
}

export const ApiKeyModal: React.FC<Props> = ({ visible, setVisible, onCancel, handleSaveAPIKey, passwordRequired, setEnterPassword, loading }) => {
	const translation = useTranslation();

	const useOutsideClick = (ref: RefObject<HTMLElement>) => {
		useEffect(() => {
			function handleClickOutside(event: MouseEvent) {
				if (ref.current && !ref.current.contains(event.target as Node)) {
					onCancel();
				}
			}

			document.addEventListener("mouseup", handleClickOutside);
			return () => {
				document.removeEventListener("mouseup", handleClickOutside);
			};
		}, [ref]);
	};

	const wrapperRef = useRef(null);
	useOutsideClick(wrapperRef);

	const handleSave = () => {
		if (passwordRequired) {
			setVisible(false);
			setEnterPassword(true);
		} else {
			handleSaveAPIKey();
		}
	};

	return (
		<BaseModal visible={visible} header={translation.modals.generateApiKey} wrapperRef={wrapperRef}>
			<ModalText data-testid="ModalText">{translation.modals.generateApiKeyText}</ModalText>
			<FlexCol>
				<MainButton type="button" onClick={handleSave} className="mx-auto mt-3" data-testid="ModalSaveButton" disabled={loading}>
					{translation.general.generate}
				</MainButton>
				<ThirdButton type="button" onClick={onCancel} className="mt-3 mx-auto" data-testid="ModalGoBack">
					{translation.general.cancel}
				</ThirdButton>
			</FlexCol>
		</BaseModal>
	);
};
