import React, { useRef, useEffect, RefObject } from "react";
import { useTranslation } from "../hooks/translations";
import { BaseModal } from "./BaseModal";
import { MainButton, ThirdButton } from "@src/styles/components";
import { ModalText } from "@src/styles/modals";

interface Props {
	visible: boolean;
	onCancel: () => void;
	onContinue: () => void;
}

export const ConfirmVoiceDelete: React.FC<Props> = ({ visible, onCancel, onContinue }) => {
	const translation = useTranslation();

	const useOutsideClick = (ref: RefObject<HTMLElement>) => {
		useEffect(() => {
			function handleClickOutside(event: MouseEvent) {
				if (ref.current && !ref.current.contains(event.target as Node)) {
					onCancel();
				}
			}

			document.addEventListener("mouseup", handleClickOutside);
			return () => {
				document.removeEventListener("mouseup", handleClickOutside);
			};
		}, [ref]);
	};

	// click outside of modal
	const wrapperRef = useRef(null);
	useOutsideClick(wrapperRef);

	return (
		<BaseModal visible={visible} header={translation.videoAvatarLibrary.deleteVoice} wrapperRef={wrapperRef}>
			<ModalText data-testid="ModalText">{translation.videoAvatarLibrary.deleteVoiceText}</ModalText>
			<>
				<MainButton type="button" onClick={onContinue} className="mx-auto mt-3" data-testid="ModalGoBack">
					{translation.general.confirm}
				</MainButton>
				<ThirdButton type="button" onClick={onCancel} className="mt-3 mx-auto" data-testid="ModalOnContinue">
					{translation.general.cancel}
				</ThirdButton>
			</>
		</BaseModal>
	);
};
