/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useRef, useEffect, RefObject, useState, useCallback } from "react";
import { useTranslation } from "../hooks/translations";
import { BaseModal } from "./BaseModal";
import { BodyText, XmarkIcon, CustomInput } from "@src/styles/forms";
import ErrorMessage from "@src/components/utils/ErrorMessage";
import {
	Flex,
	TabTitle,
	TabTitleImg,
	MainButton,
	LinkButton,
	CoverSectionModal,
	UploadBox,
	VideoPageTitleText,
	LinkSectionModal,
	FlexCol,
	VideoGrid,
	VideoInfo,
	VideoPoster,
	VideoTile,
	VideoUploadTime,
	PageSection,
	ThirdButton,
	WarningIcon
} from "@src/styles/components";
import { UploadIcon, LinkIcon, VideoLibraryIcon, CorruptFileIcon } from "@src/assets";
import { TabMode } from "@src/types/videos";
import { Form } from "react-bootstrap";
import { DropzoneRootProps, DropzoneInputProps } from "react-dropzone";
import { LoadingSpinnerAnimation } from "@src/assets/lottie_animations/loading-spinner";
import { timeAgo } from "@src/utils/time";
import Lottie from "lottie-react";
import { formatVideoName } from "@src/utils/strings";
import { Video, VideoEncodeResponse, JobVideoStatus } from "@src/types/files";
import { getErrorString } from "../utils/getErrorString";
import getVideoFiles from "../utils/getVideoFiles";
import getVideoJobs from "../utils/getVideoJobs";
import getVideoFile from "../utils/getVideoFile";
import { useTokenCheck } from "../hooks/useTokenCheck";
import getVideoJobStatus from "../utils/getVideoJobStatus";
import { useMediaQuery } from "react-responsive";
import { checkVideoURL } from "@src/utils/checkVideoURL";
import { waitForNextStatusCheck } from "@src/utils/waitForNextStatusCheck";
import SVG from "../svg/SVG";
import { apTheme } from "@src/config/theme";
import { useTheme } from "styled-components";

interface Props {
	visible: boolean;
	onCancel: () => void;
	getVideoProps: <T extends DropzoneRootProps>(props?: T) => T;
	getVideoFileProps: <T extends DropzoneInputProps>(props?: T) => T;
	submitVideoURL: (url: string) => void;
	setVideoFromLibrary: (video: Video) => void;
	setEditVideoError: (value: string) => void;
}

const useOutsideClick = (ref: RefObject<HTMLElement>, callback: () => void) => {
	const handleClickOutside = useCallback(
		(event: MouseEvent) => {
			if (ref.current && !ref.current.contains(event.target as Node)) {
				callback();
			}
		},
		[ref, callback]
	);

	useEffect(() => {
		document.addEventListener("mouseup", handleClickOutside);
		return () => {
			document.removeEventListener("mouseup", handleClickOutside);
		};
	}, [handleClickOutside]);
};

// eslint-disable-next-line max-lines-per-function
export const VideoUploadModal: React.FC<Props> = ({
	visible,
	onCancel,
	getVideoProps,
	getVideoFileProps,
	submitVideoURL,
	setVideoFromLibrary,
	setEditVideoError
}) => {
	const translation = useTranslation();
	const [selectedOption, setSelectedOption] = useState(TabMode.FROM_VIDEO);
	const [linkURL, setLinkURL] = useState("");
	const [invalidUrlError, setInvalidUrlError] = useState("");
	const { apiRetryHandler } = useTokenCheck();
	const [processingList, setProcessingList] = useState<VideoEncodeResponse[]>([]);
	const [showMoreButtonVisible, setShowMoreButtonVisible] = useState(false);
	const [videos, setVideos] = useState<Video[]>([]);
	const [cursor, setCursor] = useState<string | null>(null);
	const [selectedVideoId, setSelectedVideoId] = useState<string | null>(null);
	const wrapperRef = useRef(null);
	const isMonitoring = useRef(false);
	const isScreenSmall = useMediaQuery({ query: "(max-width: 785px)" });
	const theme: apTheme = useTheme() as apTheme;

	// Close the modal if clicked outside
	useOutsideClick(wrapperRef, onCancel);

	// Get all videos in library
	const fetchVideos = async () => {
		const { data, error }: {data: {videos: Video[]}; error: any} = await apiRetryHandler(
			async () => await getVideoFiles(cursor)
		);
		if (error) {
			const errorText = getErrorString(translation, error?.response?.data?.error);
			setEditVideoError(errorText);
		} else {
			const videoFiles = data.videos;
			setVideos((prevVideos) => [...prevVideos, ...videoFiles]);

			if (data.videos.length > 0) {
				setCursor(data.videos[data.videos.length - 1]._id);
				setShowMoreButtonVisible(true);
			} else {
				setShowMoreButtonVisible(false);
			}
		}
	};

	const fetchVideoJobs = async () => {
		const { data, error }: {data: {jobs?: VideoEncodeResponse[]}; error: any} = await apiRetryHandler(
			async () => await getVideoJobs()
		);
		if (error) {
			const errorText = getErrorString(translation, error?.response?.data?.error);
			setEditVideoError(errorText);
		} else {
			if (data?.jobs !== undefined && data?.jobs.length > 0) {
				const processingVideos = data.jobs;
				setProcessingList((prev) => [...prev, ...processingVideos]);
			}
		}
	};

	useEffect(() => {
		// Trigger fetch videos on page load
		fetchVideos();
		fetchVideoJobs();
	}, []);

	// Tracks processing list
	const monitorVideoJobs = async () => {
		// Prevent re-entry if already monitoring
		if (isMonitoring.current) return;
		isMonitoring.current = true;

		for (const job of processingList) {
			await checkJobStatusSequentially(job);
		}

		isMonitoring.current = false;
	};

	const checkJobStatusSequentially = async (initialJobStatus: VideoEncodeResponse) => {
		let currentJobStatus = initialJobStatus;
		while (currentJobStatus.status !== JobVideoStatus.COMPLETE) {
			try {
				if (currentJobStatus.status === JobVideoStatus.FAILED) {
					setProcessingList((prevJobs) =>
						prevJobs.filter((jobItem) => jobItem.tempFilename !== currentJobStatus.tempFilename)
					);
					throw new Error(`Video processing failed: ${currentJobStatus.statusMessage}`);
				}

				const { data: jobStatus } = await apiRetryHandler(
					async () => await getVideoJobStatus(currentJobStatus.tempFilename)
				);

				if (jobStatus.status === JobVideoStatus.COMPLETE) {
					const { data: videoData } = await apiRetryHandler(async () => await getVideoFile(jobStatus.videoId));

					if (videoData.video) {
						setVideos((prevVideos) => [videoData.video, ...prevVideos]);
						setProcessingList((prevJobs) =>
							prevJobs.filter((jobItem) => jobItem.tempFilename !== currentJobStatus.tempFilename)
						);
					}
					break;
				} else {
					currentJobStatus = jobStatus;
					await waitForNextStatusCheck(currentJobStatus.nextStatusCheck);
				}
			} catch (error: unknown) {
				console.error("Error while checking job status:", (error as Error).message);
				break;
			}
		}
	};

	useEffect(() => {
		if (processingList.length > 0) monitorVideoJobs();
	}, [processingList]);

	const fromPublicLink = () => {
		if (linkURL) {
			const errorMessage = checkVideoURL(linkURL, translation);

			if (errorMessage) {
				setInvalidUrlError(errorMessage);
			} else {
				submitVideoURL(linkURL);
				closeModal();
			}
		}
	};

	const saveFromLibrary = () => {
		if (selectedVideoId) {
			const selectedVideo = videos.find((video) => video._id === selectedVideoId);
			if (selectedVideo) {
				setVideoFromLibrary(selectedVideo);
				closeModal();
			}
		}
	};

	const closeModal = () => {
		setLinkURL("");
		onCancel();
	};

	return (
		<BaseModal
			modalStyle={{ maxHeight: "42rem", width: "100%" }}
			visible={visible}
			header={translation.modals.createVideo}
			wrapperRef={wrapperRef}
		>
			{!isScreenSmall ? (
				<LinkButton
					data-testid="closeButton"
					onClick={closeModal}
					style={{ position: "absolute", top: 0, right: "-3rem", height: "2rem", width: "2rem", borderRadius: "25px" }}
				>
					<XmarkIcon style={{ fontSize: "1.2rem", marginTop: 0, right: "10px", top: "6px" }} />
				</LinkButton>
			) : (
				<LinkButton data-testid="closeButton" onClick={closeModal}>
					<XmarkIcon style={{ marginTop: "-80px" }} />
				</LinkButton>
			)}
			<Flex>
				<TabTitle
					show={selectedOption === TabMode.FROM_VIDEO}
					onClick={() => setSelectedOption(TabMode.FROM_VIDEO)}
					style={{ marginRight: "10px" }}
				>
					<TabTitleImg show={selectedOption === TabMode.FROM_VIDEO}>
						<SVG
							src={VideoLibraryIcon}
							fill={selectedOption === TabMode.FROM_VIDEO ? theme.colors.apButton : theme.colors.snippetModalTextColor}
							alt={translation.createVideoPage.uploadImageAltText}
						/>
					</TabTitleImg>
					{translation.modals.titleLibrary}
				</TabTitle>
				<TabTitle
					show={selectedOption === TabMode.UPLOAD}
					onClick={() => setSelectedOption(TabMode.UPLOAD)}
					style={{ marginRight: "10px" }}
				>
					<TabTitleImg show={selectedOption === TabMode.UPLOAD}>
						<SVG
							src={UploadIcon}
							fill={selectedOption === TabMode.UPLOAD ? theme.colors.apButton : theme.colors.snippetModalTextColor}
							alt={translation.createVideoPage.uploadImageAltText}
						/>
					</TabTitleImg>
					{translation.modals.titleUpload}
				</TabTitle>
				<TabTitle
					show={selectedOption === TabMode.FROM_LINK}
					onClick={() => setSelectedOption(TabMode.FROM_LINK)}
				>
					<TabTitleImg show={selectedOption === TabMode.FROM_LINK}>
						<SVG
							src={LinkIcon}
							fill={selectedOption === TabMode.FROM_LINK ? theme.colors.apButton : theme.colors.snippetModalTextColor}
							alt={translation.createVideoPage.uploadImageAltText}
						/>
					</TabTitleImg>
					{translation.modals.titleLink}
				</TabTitle>
			</Flex>

			{selectedOption === TabMode.FROM_VIDEO && (
				<>
					<div className="mt-3" style={{ height: "100%", overflowY: "auto" }}>
						<PageSection>
							<VideoGrid selectionMode={true}>
								{processingList.map((video) => (
									<FlexCol key={video.tempFilename}>
										<VideoTile selectionMode={true}>
											<FlexCol style={{ textAlign: "center" }}>
												<Lottie
													animationData={LoadingSpinnerAnimation}
													loop={true}
													style={{ width: "50px", margin: "auto" }}
												/>
												{translation.videoLibraryPage.processingStatus}
											</FlexCol>
										</VideoTile>
									</FlexCol>
								))}
								{videos.map((video) => {
									const formattedVideoName = formatVideoName(video.videoFileLocation);
									return video?.publicPosterURL ? (
										<FlexCol key={video._id}>
											<VideoTile
												selectionMode={true}
												onClick={() => {
													setSelectedVideoId(video._id);
												}}
											>
												<input
													type="radio"
													name="videoSelection"
													value={video._id}
													checked={selectedVideoId === video._id}
													onChange={() => setSelectedVideoId(video._id)}
													style={{
														position: "absolute",
														top: "10px",
														left: "10px",
														zIndex: 1,
														height: "1.2rem",
														width: "1.2rem"
													}}
												/>
												<VideoPoster selectionMode={true} src={video.publicPosterURL} alt="Video Poster" />
											</VideoTile>
											<VideoInfo selectionMode={true} title={formattedVideoName}>{formattedVideoName}</VideoInfo>
											<VideoUploadTime selectionMode={true}>{timeAgo(video.createdAt, translation)}</VideoUploadTime>
										</FlexCol>
									) : (
										<FlexCol key={video._id}>
											<VideoTile selectionMode={true}>
												<div>
													<WarningIcon src={CorruptFileIcon} />
													{translation.errors.fileCorrupt}
												</div>
											</VideoTile>
											<VideoInfo>{translation.errors.fileCorrupt}</VideoInfo>
											<VideoUploadTime>{timeAgo(video.createdAt, translation)}</VideoUploadTime>
										</FlexCol>
									);
								}
								)}
							</VideoGrid>
							{showMoreButtonVisible && (
								<ThirdButton
									style={{ margin: "auto", display: "block", marginTop: "2rem", backgroundColor: "transparent" }}
									onClick={fetchVideos}
								>
									{translation.general.showMore}
								</ThirdButton>
							)}
							{videos.length === 0 && (
								<BodyText style={{ textAlign: "center" }}>{translation.createVideoPage.noVideos}</BodyText>
							)}
						</PageSection>
					</div>
					<MainButton
						style={{ margin: "auto", display: "block", marginTop: "2rem" }}
						onClick={() => saveFromLibrary()}
					>
						{translation.general.continue}
					</MainButton>
				</>
			)}

			{selectedOption === TabMode.UPLOAD && (
				<UploadBox>
					<div {...getVideoProps()} data-testid="coverImageDiv" style={{ cursor: "pointer" }}>
						<input id="videoFile" data-testid="coverImage" {...getVideoFileProps()} />
						<CoverSectionModal className="pt-5 pb-5">
							<VideoPageTitleText className="mb-2 mt-3">{translation.modals.dragVideoFile}</VideoPageTitleText>
							<BodyText className="text-center">
								{translation.modals.dragVideoFileLine1} <br /> {translation.modals.dragVideoFileLine2}
							</BodyText>
							<MainButton type="button" className="mx-auto mb-3">
								{translation.general.browse}
							</MainButton>
						</CoverSectionModal>
					</div>
				</UploadBox>
			)}

			{selectedOption === TabMode.FROM_LINK && (
				<UploadBox>
					<LinkSectionModal className="pt-2 pb-2">
						{!!invalidUrlError && (
							<ErrorMessage error={invalidUrlError} setError={setInvalidUrlError} displayCloseIcon={true} />
						)}
						<VideoPageTitleText className="mb-2 mt-3">{translation.modals.titleLinkText}</VideoPageTitleText>
						<BodyText className="text-center">
							{translation.modals.linkBodyText1} <br /> {translation.modals.linkBodyText2}
						</BodyText>
						<Form.Group className="mb-3">
							<CustomInput
								className="form-control"
								type="text"
								required
								placeholder={translation.modals.linkPlaceholder}
								onChange={(value) => {
									setLinkURL(value.target.value);
								}}
								value={linkURL}
								id="linkURL"
								data-testid="linkURL"
							/>
						</Form.Group>
						<MainButton type="button" onClick={() => fromPublicLink()} className="mx-auto mt-3">
							{translation.general.continue}
						</MainButton>
					</LinkSectionModal>
				</UploadBox>
			)}
		</BaseModal>
	);
};
