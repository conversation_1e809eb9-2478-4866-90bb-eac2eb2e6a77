import React, { useRef, useEffect, RefObject, useState, useCallback } from "react";
import { useTranslation } from "../hooks/translations";
import { BaseModal } from "./BaseModal";
import { BodyText, XmarkIcon } from "@src/styles/forms";
import {
	Flex,
	ProgressBarCustom,
	UploadImagePreview,
	TabTitle,
	TabTitleImg,
	ProgressBarTime,
	CanvasImage,
	VideoSectionBox,
	VideoPlayer,
	MainButton,
	LinkButton,
	CoverSectionModal,
	UploadBox
} from "@src/styles/components";
import { shoppableVideoData, TabMode } from "@src/types/videos";
import { UploadIcon } from "@src/assets";
import { useDropzone } from "react-dropzone";
import { useMediaQuery } from "react-responsive";
import { DisplayFormatOptions } from "@src/types/snippetOptions";
import SVG from "../svg/SVG";
import { apTheme } from "@src/config/theme";
import { useTheme } from "styled-components";

interface Props {
	displayFormat: DisplayFormatOptions;
	visible: boolean;
	onCancel: () => void;
	setCoverImage: (value: File) => void;
	setImagePreview: (value: string) => void;
	uploadFileURL?: string;
	imagePreview?: string;
	setCoverImageURL?: (value: string) => void;
	videoDataCopy?: shoppableVideoData;
	setVideoDataCopy?: (value: shoppableVideoData) => void;
	setEditVideoError: (value: string) => void;
}

// eslint-disable-next-line max-lines-per-function
export const CoverImageModal: React.FC<Props> = ({
	displayFormat,
	visible,
	onCancel,
	uploadFileURL,
	imagePreview,
	setCoverImage,
	setCoverImageURL,
	setImagePreview,
	videoDataCopy,
	setVideoDataCopy,
	setEditVideoError
}) => {
	const translation = useTranslation();
	const [selectedOption, setSelectedOption] = useState(TabMode.FROM_VIDEO);
	const [videoCurrentTime, setVideoCurrentTime] = useState("0:00");
	const [videoDuration, setVideoDuration] = useState("0:00");
	const isScreenSmall = useMediaQuery({ query: "(max-width: 785px)" });
	const theme: apTheme = useTheme() as apTheme;

	const captureImage = useCallback(
		(value: string) => {
			const canvas = document.getElementById("canvasImage") as HTMLCanvasElement;
			const video = document.getElementById("videoForImage") as HTMLVideoElement;
			let progressPercentage = (video.currentTime / video.duration) * 100;

			if (progressPercentage < 0) {
				progressPercentage = 0;
			}
			if (progressPercentage > 100) {
				progressPercentage = 100;
			}
			canvas.style.left = `${progressPercentage}%`;

			const context = canvas.getContext("2d");
			if (!context) {
				setEditVideoError(translation.errors.canvas2D);
				return;
			}
			canvas.width = video.videoWidth;
			canvas.height = video.videoHeight;
			context.drawImage(video, 0, 0, video.videoWidth, video.videoHeight);

			if (value) {
				// Convert canvas image to a Blob
				canvas.toBlob(async (blob) => {
					if (blob) {
						const FileToUpload = new File([blob], "coverImage.jpeg", { type: "image/jpeg" });
						setCoverImage(FileToUpload);
						const imageUrl = URL.createObjectURL(blob);
						setImagePreview(imageUrl);

						if (videoDataCopy && setVideoDataCopy && setCoverImageURL) {
							setCoverImageURL("");
							setVideoDataCopy({
								...videoDataCopy,
								shoppableVideo: {
									...videoDataCopy.shoppableVideo,
									videoPosterURL: imageUrl
								}
							});
						}
						onCancel();
					}
				});
			}
		},
		[
			onCancel,
			setCoverImage,
			setCoverImageURL,
			translation.errors.canvas2D,
			setEditVideoError,
			setImagePreview,
			setVideoDataCopy,
			videoDataCopy
		]
	);

	// cover image upload
	const { getRootProps: getImageProps, getInputProps: getImageFileProps } = useDropzone({
		maxFiles: 1,
		accept: {
			"image/jpeg": [],
			"image/png": [],
			"image/jpg": []
		},
		onDrop: (acceptedFiles) => {
			setEditVideoError("");
			const reader = new FileReader();
			reader.readAsDataURL(acceptedFiles[0]);
			reader.onload = () => {
				if (reader?.result) {
					const image = new Image();
					image.src = reader.result as string;
					image.onload = () => {
						setCoverImage(acceptedFiles[0]);
						setImagePreview(reader.result as string);

						if (videoDataCopy && setVideoDataCopy && setCoverImageURL) {
							setCoverImageURL("");
							setVideoDataCopy({
								...videoDataCopy,
								shoppableVideo: {
									...videoDataCopy.shoppableVideo,
									videoPosterURL: reader.result as string
								}
							});
						}
					};
				} else {
					setEditVideoError(translation.errors.imageFormat);
				}
			};
		}
	});

	const secondsToMinute = (seconds: number) => {
		let s = Math.round(seconds);
		return (s - (s %= 60)) / 60 + (9 < s ? ":" : ":0") + s;
	};

	const seekTo = (moveX: number) => {
		const video = document.getElementById("videoForImage") as HTMLVideoElement;
		const progressBar = document.querySelector(".customProgressBar");

		if (progressBar) {
			const progressBarRect = progressBar.getBoundingClientRect();
			const clickPosition = moveX - progressBarRect.left;
			const progressBarWidth = progressBarRect.width;
			const progressPercentage = clickPosition / progressBarWidth;
			video.currentTime = video.duration * progressPercentage;

			let currentTimeTemp = video.currentTime;
			if (currentTimeTemp < 0) {
				currentTimeTemp = 0;
			}
			if (currentTimeTemp > video.duration) {
				currentTimeTemp = video.duration;
			}
			setVideoCurrentTime(secondsToMinute(currentTimeTemp));

			captureImage("");
		}
	};

	let isDragging = false;

	// touch event
	const touchSeek = (moveX: number) => {
		isDragging = true;
		// Update progress when starting to drag
		seekTo(moveX);
	};

	document.addEventListener("touchmove", (event: TouchEvent) => {
		if (isDragging) {
			seekTo(event.touches[0].clientX);
		}
	});

	document.addEventListener("touchend", () => {
		isDragging = false;
	});

	// click event
	const startSeek = (event: {clientX: number}) => {
		isDragging = true;
		// Update progress when starting to drag
		seekTo(event.clientX);
	};

	document.addEventListener("mousemove", (event: {clientX: number}) => {
		if (isDragging) {
			seekTo(event.clientX);
		}
	});

	document.addEventListener("mouseup", () => {
		isDragging = false;
	});

	const useOutsideClick = (ref: RefObject<HTMLElement>) => {
		useEffect(() => {
			function handleClickOutside(event: MouseEvent) {
				if (ref.current && !ref.current.contains(event.target as Node)) {
					onCancel();
				}
			}

			document.addEventListener("mouseup", handleClickOutside);
			return () => {
				document.removeEventListener("mouseup", handleClickOutside);
			};
		}, [ref]);
	};

	// click outside of modal
	const wrapperRef = useRef(null);
	useOutsideClick(wrapperRef);

	useEffect(() => {
		if (uploadFileURL && visible && selectedOption === TabMode.FROM_VIDEO) {
			const video = document.getElementById("videoForImage") as HTMLVideoElement;
			const canvas = document.getElementById("canvasImage") as HTMLCanvasElement;
			if (canvas) {
				// for initial canvas image and duration time
				video.addEventListener("loadedmetadata", () => {
					setVideoCurrentTime("0:00");
					video.pause();
					video.currentTime = 0;
					setVideoDuration(secondsToMinute(video.duration));

					setTimeout(() => {
						canvas.click();
					}, 500);
				});
			}
		}
	}, [uploadFileURL, visible, selectedOption, captureImage]);

	return (
		<BaseModal visible={visible} header={translation.modals.coverImageTitle} wrapperRef={wrapperRef}>
			{!isScreenSmall ? (
				<LinkButton
					data-testid="closeButton"
					onClick={onCancel}
					style={{ position: "absolute", top: 0, right: "-3rem", height: "2rem", width: "2rem", borderRadius: "25px" }}
				>
					<XmarkIcon style={{ fontSize: "1.2rem", marginTop: 0, right: "10px", top: "6px" }} />
				</LinkButton>
			) : (
				<LinkButton data-testid="closeButton" onClick={onCancel}>
					<XmarkIcon style={{ marginTop: "-80px" }} />
				</LinkButton>
			)}
			<Flex>
				<TabTitle
					show={selectedOption === TabMode.FROM_VIDEO}
					onClick={() => setSelectedOption(TabMode.FROM_VIDEO)}
				>
					{translation.modals.titleFromVideo}
				</TabTitle>
				<TabTitle
					show={selectedOption === TabMode.UPLOAD}
					onClick={() => setSelectedOption(TabMode.UPLOAD)}
					style={{ marginLeft: "10px" }}
				>
					<TabTitleImg
						show={selectedOption === TabMode.UPLOAD}
					>
						<SVG
							src={UploadIcon}
							fill={selectedOption === TabMode.UPLOAD ? theme.colors.apButton : theme.colors.snippetModalTextColor}
							alt={translation.createVideoPage.uploadImageAltText}
						/>
					</TabTitleImg>
					{translation.modals.titleUpload}
				</TabTitle>
			</Flex>
			{selectedOption === TabMode.FROM_VIDEO && (
				<>
					<CoverSectionModal>
						<VideoSectionBox landscape={displayFormat === DisplayFormatOptions.LANDSCAPE}>
							<VideoPlayer
								id="videoForImage"
								src={uploadFileURL}
								autoPlay={true}
								playsInline={true}
								loop={false}
								muted={true}
								crossOrigin="anonymous"
								webkit-playsinline=""
								x5-playsinline=""
							></VideoPlayer>
						</VideoSectionBox>
						<BodyText className="text-center">{translation.modals.thumbnailText}</BodyText>
						<Flex>
							<ProgressBarCustom
								className="customProgressBar"
								onClick={(e) => seekTo(e.clientX)}
								onMouseDown={(e) => startSeek(e)}
								onTouchStart={(e) => touchSeek(e.touches[0].clientX)}
							>
								<CanvasImage id="canvasImage" landscape={displayFormat === DisplayFormatOptions.LANDSCAPE} />
							</ProgressBarCustom>
						</Flex>
						<Flex style={{ marginTop: "10px", justifyContent: "space-between" }}>
							<ProgressBarTime>{videoCurrentTime}</ProgressBarTime>
							<ProgressBarTime>{videoDuration}</ProgressBarTime>
						</Flex>
					</CoverSectionModal>
					<MainButton
						type="button"
						onClick={() => captureImage("set")}
						className="mx-auto mt-3"
						data-testid="ModalConfirmButton"
					>
						{translation.modals.setCoverImage}
					</MainButton>
				</>
			)}

			{selectedOption === TabMode.UPLOAD && (
				<UploadBox>
					<div {...getImageProps()} data-testid="coverImageDiv" style={{ cursor: "pointer" }}>
						<input id="coverImage" data-testid="coverImage" {...getImageFileProps()} />
						<CoverSectionModal>
							<VideoSectionBox landscape={displayFormat === DisplayFormatOptions.LANDSCAPE}>
								<UploadImagePreview data-testid="uploadedCoverImage" src={imagePreview} />
							</VideoSectionBox>
							<BodyText className="text-center">{translation.modals.clickImage}</BodyText>
						</CoverSectionModal>
					</div>
					<MainButton type="button" onClick={() => onCancel()} className="mx-auto mt-3">
						{translation.modals.setCoverImage}
					</MainButton>
				</UploadBox>
			)}
		</BaseModal>
	);
};
