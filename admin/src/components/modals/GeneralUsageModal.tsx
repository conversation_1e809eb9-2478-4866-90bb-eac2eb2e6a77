import React, { useRef, useEffect, RefObject } from "react";
import { useTranslation } from "../hooks/translations";
import { BaseModal } from "./BaseModal";
import { useNavigate } from "react-router-dom";
import { LinkButton, MainButton, ModalSectionContainer, ModalSectionFullHeight } from "@src/styles/components";
import { XmarkIcon } from "@src/styles/forms";
import { useMediaQuery } from "react-responsive";
import { ModalHeader } from "@src/styles/modals";

interface Props {
	visible: boolean;
	onCancel: () => void;
}

export const GeneralUsageModal: React.FC<Props> = ({ visible, onCancel }) => {
	const translation = useTranslation();
	const navigate = useNavigate();
	const isScreenSmall = useMediaQuery({ query: "(max-width: 1075px)" });
	const showPlansPage = process.env.SHOW_PLANS_PAGE === "true";

	const useOutsideClick = (ref: RefObject<HTMLElement>) => {
		useEffect(() => {
			function handleClickOutside(event: MouseEvent) {
				if (ref.current && !ref.current.contains(event.target as Node)) {
					closeModal();
				}
			}

			document.addEventListener("mouseup", handleClickOutside);
			return () => {
				document.removeEventListener("mouseup", handleClickOutside);
			};
		}, [ref]);
	};

	// click outside of modal
	const wrapperRef = useRef(null);
	useOutsideClick(wrapperRef);

	const closeModal = () => {
		onCancel();
	};

	return (
		<BaseModal visible={visible} wrapperRef={wrapperRef} hideHeader={true} modalStyle={{ maxWidth: "77rem", padding: 0, maxHeight: "-webkit-fill-available", marginTop: "1rem", marginBottom: "1rem" }}>
			{!isScreenSmall ? (
				<LinkButton data-testid="closeButton" onClick={() => closeModal()} style={{ position: "absolute", top: 0, right: "-3rem", height: "2rem", width: "2rem", borderRadius: "25px" }}>
					<XmarkIcon style={{ fontSize: "1.2rem", marginTop: 0, right: "10px", top: "6px", zIndex: "999" }} />
				</LinkButton>
			) : (
				<LinkButton data-testid="closeButton" onClick={() => closeModal()}>
					<XmarkIcon style={{ marginTop: "20px", zIndex: "999" }} />
				</LinkButton>
			)}
			<div>
				<ModalSectionContainer>
					<ModalSectionFullHeight position={"center"} style={{ textAlign: "center" }} backgroundOpt={0}>
						<ModalHeader>{translation.modals.tonsOfPeople}</ModalHeader>
						{showPlansPage ? (
							<>
								<div className="mt-3 mb-2">
									<b>{translation.modals.reachedImpressions}</b>
									{translation.modals.reachedImpressions2}
								</div>
								<MainButton type="button" onClick={() => navigate("/plans-pricing")} className="mx-auto mt-3 mb-3">
									{translation.modals.plansPricing}
								</MainButton>
							</>
						) : (
							<>
								<div className="mt-3 mb-2">
									<b>{translation.modals.reachedImpressionsNoPlanPage}</b>
									{translation.modals.reachedImpressions2NoPlanPage}
								</div>
								<MainButton type="button" onClick={() => closeModal()} className="mx-auto mt-3 mb-3">
									{translation.modals.closeWindow}
								</MainButton>
							</>
						)}
						<br />
					</ModalSectionFullHeight>
				</ModalSectionContainer>
			</div>
		</BaseModal>
	);
};
