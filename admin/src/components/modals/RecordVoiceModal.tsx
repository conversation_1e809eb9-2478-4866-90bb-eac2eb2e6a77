import React, {
	useRef,
	RefObject,
	useState,
	useEffect
} from "react";
import { useAudioPlayback } from "@src/components/hooks/useAudioPlayback";
import { useTranslation } from "../hooks/translations";
import { BaseModal } from "./BaseModal";
import ErrorMessage from "@src/components/utils/ErrorMessage";
import {
	MainButton,
	BackButton,
	ButtonIconImg,
	CustomAvatarSection,
	FlexGrid,
	RecordingTime,
	RecordingText,
	LoadingBar,
	LoadingBarProgress,
	FlexCenter,
	ButtonIconDiv
} from "@src/styles/components";
import {
	ModalText,
	ModalTitle,
	ModalIconButton
} from "@src/styles/modals";
import {
	MicFullIcon,
	PlayIcon,
	MicStopIcon,
	StopIcon
} from "@src/assets";
import { useTheme } from "styled-components";
import { apTheme } from "@src/config/theme";
import SVG from "../svg/SVG";

interface Props {
	visible: boolean;
	onCancel: () => void;
	onSubmit?: (audioBlob: Blob) => void;
}

// eslint-disable-next-line max-lines-per-function
export const RecordVoiceModal: React.FC<Props> = ({ visible, onCancel, onSubmit }) => {
	const translation = useTranslation();
	const [recording, setRecording] = useState(false);
	const [audioUrl, setAudioUrl] = useState<string | null>(null);
	const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
	const [audioChunks, setAudioChunks] = useState<Blob[]>([]);
	const [error, setError] = useState<string | null>(null);
	const [audioDevices, setAudioDevices] = useState<MediaDeviceInfo[]>([]);
	const [selectedDeviceId, setSelectedDeviceId] = useState<string>("");
	const [recordingTime, setRecordingTime] = useState<number>(0);
	const audioRef = useRef<HTMLAudioElement | null>(null);
	const recordingTimeRef = useRef(0);
	const [recordedDuration, setRecordedDuration] = useState(0);
	const [showStartRecording, setShowStartRecording] = useState(false);
	const voiceScript = process.env.AVATAR_VOICE_SCRIPT;
	const theme: apTheme = useTheme() as apTheme;

	const {
		isPlaying,
		playTime,
		handlePlay,
		handleStop,
		handleAudioTimeUpdate,
		handleAudioEnded,
		setPlayTime,
		getProgressBarWidth,
		resetPlaybackState
	} = useAudioPlayback(audioRef, recordedDuration);

	const useOutsideClick = (ref: RefObject<HTMLElement>) => {
		useEffect(() => {
			function handleClickOutside(event: MouseEvent) {
				if (ref.current && !ref.current.contains(event.target as Node)) {
					stopRecordingRef.current();
					onCancel();
				}
				if (recording) {
					stopRecordingRef.current();
				}
			}
			document.addEventListener("mouseup", handleClickOutside);
			return () => {
				document.removeEventListener("mouseup", handleClickOutside);
			};
			// eslint-disable-next-line react-hooks/exhaustive-deps
		}, [ref, onCancel]);
	};

	const wrapperRef = useRef(null);
	useOutsideClick(wrapperRef);

	useEffect(() => {
		if (visible) {
			let permissionStream: MediaStream | null = null;
			navigator.mediaDevices.getUserMedia({ audio: true })
				.then((s) => {
					permissionStream = s;
					return navigator.mediaDevices.enumerateDevices();
				})
				.then((devices) => {
					const mics = devices.filter((d) => d.kind === "audioinput");
					if (mics.length > 0 && mics[0].deviceId) {
						setAudioDevices(mics);
						setSelectedDeviceId(mics[0].deviceId);
					} else {
						setError(translation.errors.microphoneNotFound);
					}
				})
				.catch(() => {
					setAudioDevices([]);
					setError(translation.errors.microphoneNotFound);
				})
				.finally(() => {
					if (permissionStream) {
						permissionStream.getTracks().forEach(t => t.stop());
						permissionStream = null;
					}
				});
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [visible]);

	useEffect(() => {
		let timer: NodeJS.Timeout;

		if (recording) {
			setRecordingTime(0);
			timer = setInterval(() => {
				setRecordingTime((prev) => {
					const newTime = prev + 1;
					recordingTimeRef.current = newTime;
					return newTime;
				});
			}, 1000);
		} else {
			setRecordingTime(0);
		}
		return () => {
			if (timer) clearInterval(timer);
		};
	}, [recording]);

	useEffect(() => {
		if (!audioUrl) {
			resetPlaybackState();
			setRecordedDuration(0);
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [audioUrl]);

	// Start recording
	const startRecording = async () => {
		setError(null);
		setAudioUrl(null);
		setRecordedDuration(0);
		setAudioChunks([]);
		try {
			const constraints = selectedDeviceId ? { audio: { deviceId: selectedDeviceId } } : { audio: true };
			const stream = await navigator.mediaDevices.getUserMedia(constraints);
			const recorder = new MediaRecorder(stream);
			setMediaRecorder(recorder);
			recorder.start();
			setRecording(true);

			const chunks: Blob[] = [];
			recorder.ondataavailable = (e) => {
				if (e.data.size > 0) chunks.push(e.data);
			};
			recorder.onstop = () => {
				const blob = new Blob(chunks, { type: "audio/webm" });
				setAudioUrl(URL.createObjectURL(blob));
				setAudioChunks(chunks);
				setRecording(false);
				setRecordedDuration(recordingTimeRef.current);
				setShowStartRecording(false);
			};
		} catch (err) {
			setError(translation.errors.microphoneAccessDenied);
		}
	};

	// Stop recording
	const stopRecording = React.useCallback(() => {
		if (mediaRecorder && recording) {
			mediaRecorder.stop();
			if (mediaRecorder.stream) {
				mediaRecorder.stream.getTracks().forEach(track => track.stop());
			}
		}
	}, [mediaRecorder, recording]);

	const stopRecordingRef = useRef(stopRecording);
	useEffect(() => {
		stopRecordingRef.current = stopRecording;
	}, [stopRecording]);

	// Reset audio
	const handleReset = () => {
		setAudioUrl(null);
		setAudioChunks([]);
		setRecording(false);
		setError(null);
		stopRecording();
	};

	// Submit audio
	const handleSubmit = () => {
		if (audioChunks.length && onSubmit) {
			const blob = new Blob(audioChunks, { type: "audio/webm" });
			onSubmit(blob);
			onCancel();
		}
	};

	return (
		<BaseModal visible={visible} hideHeader={true} wrapperRef={wrapperRef} modalStyle={{ width: "100%" }}>
			{error && <ErrorMessage error={error} setError={setError} displayCloseIcon={true} style={{ marginBottom: "1rem" }} />}
			<FlexGrid>
				{!recording && !audioUrl && !showStartRecording && (
					<>
						<ModalTitle>{translation.videoAvatarLibrary.startRecording}</ModalTitle>
						<ModalText>{translation.videoAvatarLibrary.voiceGuidelines}</ModalText>
						{audioDevices.length > 0 && (
							<>
								<div style={{ margin: "1rem 0", width: "100%", textAlign: "center", color: theme.colors.apTextColor }}>
									<p><b>{translation.videoAvatarLibrary.selectMicrophone}:</b></p>
									<select
										id="mic-select"
										value={selectedDeviceId}
										onChange={e => setSelectedDeviceId(e.target.value)}
										style={{ padding: "0.5rem", borderRadius: 8, backgroundColor: theme.colors.apBackgroundColor, color: "inherit" }}
									>
										{audioDevices.map((d) => (
											<option key={d.deviceId} value={d.deviceId}>{d.label || `Microphone ${d.deviceId}`}</option>
										))}
									</select>
								</div>
								<MainButton type="button" onClick={() => setShowStartRecording(true)} className="mx-auto mt-2">
									{translation.general.next}
								</MainButton>
								<BackButton onClick={onCancel} className="mt-2 mx-auto">
									{translation.general.cancel}
								</BackButton>
							</>
						)}
					</>
				)}
				{showStartRecording && (
					<>
						<ModalTitle>{translation.videoAvatarLibrary.voiceGuidelinesTitle}</ModalTitle>
						<RecordingText>{voiceScript}</RecordingText>
						<FlexCenter>
							<RecordingTime style={{ margin: "0" }}>
								{`${Math.floor(recordingTime / 60)}:${(recordingTime % 60).toString().padStart(2, "0")}`}
							</RecordingTime>
							{recording ? (
								<ModalIconButton src={MicStopIcon} onClick={stopRecording} />
							) : (
								<ModalIconButton src={MicFullIcon} onClick={startRecording} />
							)}
							<BackButton onClick={() => {
								stopRecording();
								onCancel();
							}}>
								{translation.general.cancel}
							</BackButton>
						</FlexCenter>
					</>
				)}
				{audioUrl && (
					<>
						<ModalTitle>{translation.videoAvatarLibrary.recordingComplete}</ModalTitle>
						<CustomAvatarSection border={false} center={true} style={{ padding: "1rem", width: "100%" }}>
							<div style={{ padding: "2rem 1rem", width: "100%" }}>
								<audio
									ref={audioRef}
									src={audioUrl}
									preload="auto"
									style={{ display: "none" }}
									onLoadedMetadata={() => setPlayTime(0)}
									onTimeUpdate={handleAudioTimeUpdate}
									onEnded={handleAudioEnded}
								/>
								<RecordingTime>
									{audioRef.current ? `${Math.floor(playTime / 60)}:${(Math.floor(playTime) % 60).toString().padStart(2, "0")}` : "0:00"}
								</RecordingTime>
								<FlexGrid style={{ flexDirection: "row" }}>
									<ButtonIconDiv onClick={isPlaying ? handleStop : handlePlay}>
										<SVG src={isPlaying ? StopIcon : PlayIcon} fill={theme.colors.apTextColor}/>
									</ButtonIconDiv>
									<LoadingBar>
										<LoadingBarProgress style={{ width: `${getProgressBarWidth()}%` }} />
									</LoadingBar>
								</FlexGrid>
							</div>
							<BackButton onClick={handleReset} className="mx-auto mt-2">
								<ButtonIconImg src={MicFullIcon}/>
								{translation.videoAvatarLibrary.recordAgain}
							</BackButton>
						</CustomAvatarSection>
						<MainButton type="button" onClick={handleSubmit} className="mx-auto mt-2">
							{translation.general.finish}
						</MainButton>
						<BackButton onClick={onCancel} className="mt-2 mx-auto">
							{translation.general.cancel}
						</BackButton>
					</>
				)}
			</FlexGrid>
		</BaseModal>
	);
};
