import React, { useRef, useEffect, RefObject } from "react";
import { useTranslation } from "../hooks/translations";
import { BaseModal } from "./BaseModal";
import { MainButton, ThirdButton } from "@src/styles/components";
import { ModalText } from "@src/styles/modals";

interface Props {
	visible: boolean;
	setVisible: (value: boolean) => void;
	onCancel: () => void;
	setEnterPassword: (value: boolean) => void;
	handleDelete: () => void;
	passwordRequired: boolean;
	loading: boolean;
}

export const RemoveUserModal: React.FC<Props> = ({ visible, setVisible, onCancel, setEnterPassword, handleDelete, passwordRequired, loading }) => {
	const translation = useTranslation();

	const useOutsideClick = (ref: RefObject<HTMLElement>) => {
		useEffect(() => {
			function handleClickOutside(event: MouseEvent) {
				if (ref.current && !ref.current.contains(event.target as Node)) {
					onCancel();
				}
			}

			document.addEventListener("mouseup", handleClickOutside);
			return () => {
				document.removeEventListener("mouseup", handleClickOutside);
			};
		}, [ref]);
	};

	// click outside of modal
	const wrapperRef = useRef(null);
	useOutsideClick(wrapperRef);

	const handleSave = () => {
		if (passwordRequired) {
			setVisible(false);
			setEnterPassword(true);
		} else {
			handleDelete();
		}
	};

	return (
		<BaseModal visible={visible} header={translation.modals.removeUserModalTitle} wrapperRef={wrapperRef}>
			<ModalText data-testid="ModalText">{translation.modals.removeUserModalText}</ModalText>
			<>
				<MainButton type="button" onClick={handleSave} className="mx-auto mt-3" data-testid="ModalConfirmButton" disabled={loading}>
					{translation.modals.removeUserModalButton}
				</MainButton>
				<ThirdButton type="button" onClick={onCancel} className="mt-3 mx-auto" data-testid="ModalCancelLink">
					{translation.general.cancel}
				</ThirdButton>
			</>
		</BaseModal>
	);
};
