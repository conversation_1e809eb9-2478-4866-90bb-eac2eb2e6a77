import React, { useState, useEffect } from "react";
import { useTranslation } from "../hooks/translations";
import { BaseModal } from "./BaseModal";
import { MainButton, FormText, ThirdButton, CustomSelect } from "@src/styles/components";
import { ModalText } from "@src/styles/modals";
import { Form } from "react-bootstrap";
import { CustomInput } from "@src/styles/forms";
import { useTokenCheck } from "../hooks/useTokenCheck";
import { platformOptions, teamOptions } from "@src/types/snippetOptions";
import getAccount from "../utils/getAccount";
import postSignup from "../utils/postSignup";

interface Props {
	visible: boolean;
	onCancel: () => void;
}

export const ExtraInformationModal: React.FC<Props> = ({ visible, onCancel }) => {
	const translation = useTranslation();
	const { apiRetryHandler } = useTokenCheck();
	const [loading, setLoading] = useState(false);
	const [validated, setValidated] = useState(false);
	const [refetch, setRefetch] = useState(true);
	const [companyName, setCompanyName] = useState("");
	const [firstName, setFirstName] = useState("");
	const [isAccountOwner, setIsAccountOwner] = useState(false);

	const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
		setLoading(true);
		event.preventDefault();
		const form = event.currentTarget;

		if (form.checkValidity() === false) {
			event.stopPropagation();
			setValidated(true);
			setLoading(false);
		} else {
			try {
				const firstName = (form.elements.namedItem("firstName") as HTMLInputElement)?.value;
				const companyName = (form.elements.namedItem("companyName") as HTMLInputElement)?.value;
				const teamOption = (form.elements.namedItem("teamOption") as HTMLInputElement)?.value;
				const platformOption = (form.elements.namedItem("platformOption") as HTMLInputElement)?.value;

				await apiRetryHandler(async () => await postSignup(firstName, companyName, teamOption, platformOption));
				onCancel();
				setLoading(false);
			} catch (error) {
				setValidated(false);
				setLoading(false);
			}
		}
	};

	useEffect(() => {
		const go = async () => {
			const { data, error } = await apiRetryHandler(async () => await getAccount());

			if (!error) {
				const tempName = data.email.replace(/@.*$/, "");
				setFirstName(tempName);
				setCompanyName(data.companyName);
				setIsAccountOwner(data.isAccountOwner);
			}
			setRefetch(false);
		};

		if (refetch) {
			go();
		}
	}, [refetch, apiRetryHandler]);

	return (
		<>
			{!refetch && (
				<BaseModal visible={visible} header={translation.modals.aboutYou} modalStyle={{ maxHeight: "-webkit-fill-available" }}>
					<ModalText data-testid="ModalText">{translation.modals.extraInfoText}</ModalText>
					<Form noValidate onSubmit={handleSubmit} validated={validated} data-testid="inviteUserSection" className="p-1">
						<FormText className="mb-3 mt-2">{translation.modals.yourName}</FormText>
						<CustomInput className="form-control" type="text" required defaultValue={firstName} placeholder={translation.general.firstName} onInput={(e: React.ChangeEvent<HTMLInputElement>) => e.target.value} id="firstName" data-testid="firstName" />

						<FormText className="mb-3 mt-2">{translation.modals.yourCompany}</FormText>
						<CustomInput className="form-control" type="text" required defaultValue={companyName} disabled={!isAccountOwner} placeholder={translation.general.companyName} onInput={(e: React.ChangeEvent<HTMLInputElement>) => e.target.value} id="companyName" data-testid="companyName" />

						<FormText className="mb-3 mt-2">{translation.modals.yourTeam}</FormText>

						<CustomSelect id="teamOption" data-testid="teamOption" required>
							{teamOptions.map((option, index) => (
								<option key={"teamOptions" + index} value={option.value}>
									{option.label}
								</option>
							))}
						</CustomSelect>

						<FormText className="mb-3 mt-2">{translation.modals.yourWebsite}</FormText>

						<CustomSelect id="platformOption" data-testid="platformOption" required>
							{platformOptions.map((option, index) => (
								<option key={"platformOptions" + index} value={option.value}>
									{option.label}
								</option>
							))}
						</CustomSelect>

						<MainButton type="submit" className="mx-auto mt-4" data-testid="ModalConfirmButton" disabled={loading} style={{ display: "block" }}>
							{translation.general.continue}
						</MainButton>
						<ThirdButton type="button" onClick={onCancel} className="mt-3 mx-auto" data-testid="ModalCancelLink" style={{ display: "block" }}>
							{translation.modals.doThisLater}
						</ThirdButton>
					</Form>
				</BaseModal>
			)}
		</>
	);
};
