import React, { useRef, useEffect, RefObject, useState } from "react";
import { useTranslation } from "../hooks/translations";
import { BaseModal } from "./BaseModal";
import { VideoManager, H2, LinkButton, MainButton, ModalSectionContainer, ModalSectionFullHeight, LinkText, LinkSpan } from "@src/styles/components";
import { AddConversionScript } from "@src/assets";
import Skeleton from "react-loading-skeleton";
import { XmarkIcon } from "@src/styles/forms";
import { useMediaQuery } from "react-responsive";
import { loadStripe } from "@stripe/stripe-js";
import { Elements } from "@stripe/react-stripe-js";
import { StripePaymentForm } from "@src/components/payment/StripePaymentForm";
import updateSubscription from "../utils/updateSubscription";
import { useTokenCheck } from "../hooks/useTokenCheck";
import { Account } from "@src/types/account";
import { ModalHeader } from "@src/styles/modals";
import { getErrorString } from "../utils/getErrorString";
import ErrorMessage from "@src/components/utils/ErrorMessage";

const stripePromise = loadStripe(process.env.STRIPE_PUBLISHABLE_KEY ?? "");
interface Props {
	nextPriceId?: string;
	upgradeToPro: boolean;
	backToFreePlan?: boolean;
	visible: boolean;
	onClose: () => void;
	setRefetch: (value: boolean) => void;
	accountData: Account | undefined;
}

export const ChangePlanModal: React.FC<Props> = ({ nextPriceId, upgradeToPro, backToFreePlan, visible, onClose, setRefetch, accountData }) => {
	const { apiRetryHandler } = useTokenCheck();
	const translation = useTranslation();
	const [currentScreen, setCurrentScreen] = useState(1);
	const isScreenSmall = useMediaQuery({ query: "(max-width: 1075px)" });
	const [showError, setShowError] = useState("");
	const [disableButton, setDisableButton] = useState(false);
	const [loading, setLoading] = useState(false);

	const useOutsideClick = (ref: RefObject<HTMLElement>) => {
		useEffect(() => {
			function handleClickOutside(event: MouseEvent) {
				if (ref.current && !ref.current.contains(event.target as Node)) {
					closeModal();
				}
			}

			document.addEventListener("mouseup", handleClickOutside);
			return () => {
				document.removeEventListener("mouseup", handleClickOutside);
			};
		}, [ref]);
	};

	// click outside of modal
	const wrapperRef = useRef(null);
	useOutsideClick(wrapperRef);

	const getPaymentMethod = async () => {
		setDisableButton(true);
		if (accountData?.subscription.stripeSubscriptionId && nextPriceId) {
			await apiRetryHandler(async () => await updateSubscription(nextPriceId, accountData?.subscription?.stripeSubscriptionId));
		}
		setCurrentScreen(4);
	};

	const changePlan = async () => {
		if (nextPriceId) {
			setLoading(true);
			const { error } = await apiRetryHandler(async () => await updateSubscription(nextPriceId, accountData?.subscription?.stripeSubscriptionId));
			if (!error) {
				setTimeout(() => {
					closeModal();
					setLoading(false);
				}, 1000);
			}
		}
	};

	useEffect(() => {
		const fetchData = async () => {
			setShowError("");

			if (accountData && upgradeToPro && accountData.subscription?.trialAvailable) {
				if (accountData.subscription?.hasPaymentMethod) {
					setCurrentScreen(4);
				} else {
					setCurrentScreen(2);
				}
			} else {
				if (backToFreePlan) {
					setCurrentScreen(5);
				} else {
					if (accountData?.subscription?.hasPaymentMethod && nextPriceId) {
						setCurrentScreen(6);
					} else {
						setCurrentScreen(3);
					}
				}
			}
		};

		fetchData();
	}, [accountData, nextPriceId, upgradeToPro, backToFreePlan]);

	const closeModal = () => {
		setDisableButton(false);
		setRefetch(true);
		onClose();
	};

	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	const setError = (error: any) => {
		const errorText = getErrorString(translation, error?.response?.data?.error);
		setShowError(errorText);
	};

	return (
		<BaseModal visible={visible} wrapperRef={wrapperRef} hideHeader={true} modalStyle={{ maxWidth: "77rem", padding: 0, maxHeight: "-webkit-fill-available", marginTop: "1rem", marginBottom: "1rem" }}>
			{!isScreenSmall ? (
				<LinkButton data-testid="closeButton" onClick={() => closeModal()} style={{ position: "absolute", top: 0, right: "-3rem", height: "2rem", width: "2rem", borderRadius: "25px" }}>
					<XmarkIcon style={{ fontSize: "1.2rem", marginTop: 0, right: "10px", top: "6px", zIndex: "999" }} />
				</LinkButton>
			) : (
				<LinkButton data-testid="closeButton" onClick={() => closeModal()}>
					<XmarkIcon style={{ marginTop: "20px", zIndex: "999" }} />
				</LinkButton>
			)}

			{/* Loading screen */}
			{currentScreen === 1 && (
				<ModalSectionContainer>
					<ModalSectionFullHeight position={"center"} backgroundOpt={0}>
						<div style={{ width: "300px" }}>
							<Skeleton height={35} count={10} />
						</div>
					</ModalSectionFullHeight>
				</ModalSectionContainer>
			)}

			{/* Congrats Message and Payment screen */}
			{currentScreen === 2 && (
				<div>
					<ModalSectionContainer>
						<ModalSectionFullHeight position={"center"} backgroundOpt={0}>
							<H2>{translation.modals.welcomeToPro}</H2>
							<br />
							<div>{translation.modals.welcomeToProText}</div>
							<br />
							<h5>
								<b>{translation.accountSettingsPage.enterPaymentDetails}</b>
							</h5>
							<Elements stripe={stripePromise}>
								<StripePaymentForm disableButton={disableButton} setDisableButton={setDisableButton} onFormFailure={(error: unknown) => setError(error)} onFormSuccess={() => setCurrentScreen(4)} enableDoLaterOption={true} doLaterAction={() => setCurrentScreen(4)} />
							</Elements>
						</ModalSectionFullHeight>
					</ModalSectionContainer>
				</div>
			)}

			{/* Payment only screen */}
			{currentScreen === 3 && (
				<div>
					<ModalSectionContainer>
						<ModalSectionFullHeight position={"center"} backgroundOpt={0}>
							{!!showError && <ErrorMessage error={showError} setError={setShowError} displayCloseIcon={true} />}
							<ModalHeader style={{ textAlign: "left", marginTop: "1rem", marginBottom: "2rem" }}>{translation.accountSettingsPage.enterPaymentDetails}</ModalHeader>
							<Elements stripe={stripePromise}>
								<StripePaymentForm disableButton={disableButton} setDisableButton={setDisableButton} onFormFailure={(error: unknown) => setError(error)} onFormSuccess={() => getPaymentMethod()} />
							</Elements>
						</ModalSectionFullHeight>
					</ModalSectionContainer>
				</div>
			)}

			{/* Congrats only screen */}
			{currentScreen === 4 && (
				<div>
					<ModalSectionContainer>
						<ModalSectionFullHeight position={"center"} backgroundOpt={0}>
							<H2>{translation.modals.allSet}</H2>
							<br />
							{upgradeToPro && (
								<>
									<div>{translation.modals.proTrialNowActive}</div>
									<br />
									<VideoManager style={{ display: "flex", cursor: "pointer", alignItems: "center" }} onClick={() => window.open(translation.modals.addConversionLink, "_blank")}>
										<img style={{ maxWidth: isScreenSmall ? "90px" : "150px" }} src={AddConversionScript} />
										<LinkText>{translation.modals.addConversionScript}</LinkText>
									</VideoManager>
									<br />
									<div>{translation.modals.backToDashboard}</div>
									<br />
								</>
							)}
							<MainButton style={{ width: "100%" }} onClick={() => closeModal()}>
								{translation.modals.returnToDashboard}
							</MainButton>
						</ModalSectionFullHeight>
					</ModalSectionContainer>
				</div>
			)}

			{/* Downgrade screen */}
			{currentScreen === 5 && (
				<div>
					<ModalSectionContainer>
						<ModalSectionFullHeight position={"center"} style={{ textAlign: "center" }} backgroundOpt={0}>
							<ModalHeader>{translation.modals.changePlanTitle}</ModalHeader>
							<div className="mt-3 mb-2">{translation.modals.downgradePlanText}</div>
							<MainButton type="button" onClick={() => closeModal()} className="mx-auto mt-3 mb-3">
								{translation.modals.keepCurrentPlan}
							</MainButton>
							<br />
							<LinkSpan
								data-testid="changePlan"
								disabled={loading}
								onClick={() => {
									if (!loading) {
										changePlan();
									}
								}}
							>
								{translation.general.downgrade}
							</LinkSpan>
							<br />
						</ModalSectionFullHeight>
					</ModalSectionContainer>
				</div>
			)}

			{/* Upgrade screen */}
			{currentScreen === 6 && (
				<div>
					<ModalSectionContainer>
						<ModalSectionFullHeight position={"center"} style={{ textAlign: "center" }} backgroundOpt={0}>
							<ModalHeader>{translation.modals.confirmUpgrade}</ModalHeader>
							<div className="mt-3 mb-2">{translation.modals.changePlanText}</div>
							<MainButton type="button" disabled={loading} onClick={() => changePlan()} className="mx-auto mt-3 mb-3">
								{translation.general.confirm}
							</MainButton>
							<br />
							<LinkSpan data-testid="changePlan" onClick={() => closeModal()}>
								{translation.general.cancel}
							</LinkSpan>
							<br />
						</ModalSectionFullHeight>
					</ModalSectionContainer>
				</div>
			)}
		</BaseModal>
	);
};
