import React, { useRef, useEffect, RefObject, useState } from "react";
import { useTranslation } from "../hooks/translations";
import { BaseModal } from "./BaseModal";
import { BodyText, CloneIconDiv, CloneIcon, XmarkIcon } from "@src/styles/forms";
import {
	TitleText,
	SpanColor,
	MainButton,
	LinkButton,
	VideoSectionModal,
	CodeSnippetModal,
	CustomHighlight,
	HighlightCodeModal,
	SnippetModalText,
	ModalSubtext,
	BackButton,
	MockImageLarge
} from "@src/styles/components";
import { CarouselImage, OnSiteImage, WidgetImage } from "@src/assets";
import { apTheme } from "@src/config/theme";
import { SnippetDisplayMode, SnippetOptions } from "@src/types/snippetOptions";
import { ModalHeader } from "@src/styles/modals";
import { useMediaQuery } from "react-responsive";
import ImageSelectCarousel from "../inputs/ImageSelectCarousel";
import { useTheme } from "styled-components";

interface Props {
	visible: boolean;
	onCancel: () => void;
	collectionId?: string;
}

export const SnippetOptionsModal: React.FC<Props> = ({ visible, onCancel, collectionId }) => {
	const translation = useTranslation();
	const [selectedOption, setSelectedOption] = useState(SnippetOptions.CAROUSEL);
	const [stepNumber, setStepNumber] = useState(1);
	const [embedScriptString, setEmbedScriptString] = useState("");

	const isScreenXS = useMediaQuery({ query: "(max-width: 670px)" });
	const isScreenSmall = useMediaQuery({ query: "(max-width: 930px)" });
	const isScreenMedium = useMediaQuery({ query: "(max-width: 1100px)" });
	const closeButtonExternal = useMediaQuery({ query: "(max-width: 785px)" });

	let displayMode = SnippetDisplayMode.ALL;
	let modalWidth = "54rem";
	if (isScreenXS) {
		displayMode = SnippetDisplayMode.SINGLE;
	} else if (isScreenSmall) {
		displayMode = SnippetDisplayMode.SM_DOUBLE;
		modalWidth = "34rem";
	} else if (isScreenMedium) {
		displayMode = SnippetDisplayMode.DOUBLE;
		modalWidth = "43.5rem";
	} else if (stepNumber === 2 && displayMode === SnippetDisplayMode.ALL) {
		modalWidth = "60rem";
	}

	const embedScript = `<script src='${process.env.APP_ENDPOINT}/core.js' defer></script>`;

	useEffect(() => {
		const scriptStr =
			"<div \n data-gp-collection='" + collectionId + "' \n data-gp-type='" + selectedOption + "' \n></div>";
		if (scriptStr !== embedScriptString) setEmbedScriptString(scriptStr);
	}, [collectionId, selectedOption, embedScriptString]);

	const useOutsideClick = (ref: RefObject<HTMLElement>) => {
		useEffect(() => {
			function handleClickOutside(event: MouseEvent) {
				if (ref.current && !ref.current.contains(event.target as Node)) {
					setStepNumber(1);
					onCancel();
				}
			}

			document.addEventListener("mouseup", handleClickOutside);
			return () => {
				document.removeEventListener("mouseup", handleClickOutside);
			};
		}, [ref]);
	};

	const wrapperRef = useRef(null);
	useOutsideClick(wrapperRef);

	const modalStyleMobile: React.CSSProperties = {
		maxWidth: "100%",
		width: "100%",
		position: "fixed",
		zIndex: 99999,
		display: "flex",
		height: "100%",
		borderRadius: 0,
		top: 0,
		left: 0
	};

	let closeIconStyle: React.CSSProperties = { marginTop: "-140px" };

	if (displayMode !== SnippetDisplayMode.SINGLE && stepNumber === 2) {
		closeIconStyle = { marginTop: "-75px" };
	} else if (displayMode === SnippetDisplayMode.SINGLE && stepNumber === 2) {
		closeIconStyle = { marginTop: "-120px" };
	} else if (displayMode === SnippetDisplayMode.SINGLE && stepNumber === 1) {
		closeIconStyle = { marginTop: "0", position: "absolute", top: "2rem" };
	} else if (displayMode === SnippetDisplayMode.SM_DOUBLE && stepNumber === 1) {
		closeIconStyle = { marginTop: "-200px" };
	}

	const theme: apTheme = useTheme() as apTheme;

	return (
		<BaseModal
			visible={visible}
			wrapperRef={wrapperRef}
			modalStyle={isScreenXS ? modalStyleMobile : { maxWidth: modalWidth }}
		>
			<ModalHeader
				style={{ textAlign: displayMode === SnippetDisplayMode.SINGLE ? "center" : "left", marginLeft: "1.25rem" }}
			>
				<span
					style={{
						color: theme.colors.apButton,
						display: displayMode === SnippetDisplayMode.SINGLE ? "block" : "inline"
					}}
				>
					{stepNumber === 1 ? translation.modals.snippetOptsStep1 : translation.modals.snippetOptsStep2}
				</span>
				{stepNumber === 1 ? translation.modals.snippetOptsStep1Text : translation.modals.snippetOptsStep2Text}
			</ModalHeader>
			{stepNumber === 1 && (
				<ModalSubtext style={{ textAlign: displayMode === SnippetDisplayMode.SINGLE ? "center" : "left" }}>
					{translation.modals.snippetOptsStep1Subtext}
				</ModalSubtext>
			)}
			{!closeButtonExternal ? (
				<LinkButton
					data-testid="closeButton"
					onClick={() => {
						setStepNumber(1);
						onCancel();
					}}
					style={{ position: "absolute", top: 0, right: "-3rem", height: "2rem", width: "2rem", borderRadius: "25px" }}
				>
					<XmarkIcon style={{ fontSize: "1.2rem", marginTop: 0, right: "10px", top: "6px" }} />
				</LinkButton>
			) : (
				<LinkButton
					data-testid="closeButton"
					onClick={() => {
						setStepNumber(1);
						onCancel();
					}}
				>
					<XmarkIcon style={closeIconStyle} />
				</LinkButton>
			)}
			{stepNumber === 1 && (
				<VideoSectionModal style={{ background: theme.colors.apBackgroundColor, margin: 0 }}>
					<ImageSelectCarousel
						displayTypeValue={displayMode}
						selectedOption={selectedOption}
						setSelectedOption={setSelectedOption}
					/>
				</VideoSectionModal>
			)}
			{stepNumber === 2 && (
				<div style={{ display: displayMode === SnippetDisplayMode.SINGLE ? "block" : "flex" }}>
					<VideoSectionModal style={{ background: theme.colors.apSectionBackground, margin: 0 }}>
						<SnippetModalText data-testid="SnippetModalText">
							<TitleText>
								<SpanColor>1. </SpanColor>
								{translation.modals.addStep1}
							</TitleText>
							<BodyText>{translation.modals.addStep1Text}</BodyText>
							<div style={{ position: "relative" }}>
								<CloneIconDiv style={{ width: "100%", position: "absolute" }}>
									<CloneIcon
										data-testid="CloneIcon"
										style={{ float: "right" }}
										onClick={() => {
											navigator.clipboard.writeText(embedScript);
										}}
									/>
								</CloneIconDiv>
								<CodeSnippetModal>
									<CustomHighlight data-testid="highlightBox">
										<HighlightCodeModal className="language-html">{embedScript}</HighlightCodeModal>
									</CustomHighlight>
								</CodeSnippetModal>
							</div>
							<TitleText className="mt-3">
								<SpanColor>2. </SpanColor>
								{selectedOption === SnippetOptions.CAROUSEL
									? translation.modals.addStep2
									: selectedOption === SnippetOptions.WIDGET
										? translation.modals.addStep2Widget
										: translation.modals.addStep2OnSite}
							</TitleText>
							<BodyText>
								{selectedOption === SnippetOptions.CAROUSEL
									? translation.modals.addStep2Text
									: selectedOption === SnippetOptions.WIDGET
										? translation.modals.addStep2WidgetText
										: translation.modals.addStep2OnSiteText}
							</BodyText>
							<div style={{ position: "relative" }}>
								<CloneIconDiv style={{ width: "100%", position: "absolute" }}>
									<CloneIcon
										data-testid="CloneIcon"
										style={{ float: "right" }}
										onClick={() => {
											navigator.clipboard.writeText(embedScriptString);
										}}
									/>
								</CloneIconDiv>
								<CodeSnippetModal>
									<CustomHighlight data-testid="highlightBox">
										<HighlightCodeModal className="language-html">{embedScriptString}</HighlightCodeModal>
									</CustomHighlight>
								</CodeSnippetModal>
							</div>
						</SnippetModalText>
					</VideoSectionModal>
					{displayMode === SnippetDisplayMode.ALL && (
						<MockImageLarge
							style={{ height: "100%" }}
							imageURL={
								selectedOption === SnippetOptions.CAROUSEL
									? CarouselImage
									: selectedOption === SnippetOptions.WIDGET
										? WidgetImage
										: OnSiteImage
							}
							displayTypeValue={displayMode}
						/>
					)}
				</div>
			)}
			{stepNumber === 1 && (
				<MainButton
					type="button"
					onClick={() => setStepNumber(2)}
					className="mx-auto mt-3"
					data-testid="ModalContinueButton"
				>
					{translation.general.continue}
				</MainButton>
			)}
			{stepNumber === 2 && (
				<div style={{ display: "flex", justifyContent: "center", alignItems: "center" }} className="mt-3">
					<BackButton onClick={() => setStepNumber(1)}>
						{translation.modals.goBackBtnText}
					</BackButton>
					<MainButton
						type="button"
						onClick={() => {
							setStepNumber(1);
							onCancel();
						}}
						className="ms-4"
						data-testid="ModalConfirmButton"
					>
						{translation.general.done}
					</MainButton>
				</div>
			)}
		</BaseModal>
	);
};
