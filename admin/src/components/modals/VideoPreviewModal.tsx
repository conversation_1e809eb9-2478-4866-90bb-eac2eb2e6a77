import React, { useRef, useEffect, RefObject } from "react";
import { useTranslation } from "../hooks/translations";
import { BaseModal } from "./BaseModal";
import { Flex, LinkButton, MainButton, ThirdButton } from "@src/styles/components";
import { XmarkIcon } from "@src/styles/forms";
import { useMediaQuery } from "react-responsive";

interface Props {
	visible: boolean;
	videoUrl: string;
	onContinue: () => void;
	onClose: () => void;
}

export const VideoPreviewModal: React.FC<Props> = ({ visible, videoUrl, onContinue, onClose }) => {
	const translation = useTranslation();
	const isScreenSmall = useMediaQuery({ query: "(max-width: 785px)" });
	const useOutsideClick = (ref: RefObject<HTMLElement>) => {
		useEffect(() => {
			function handleClickOutside(event: MouseEvent) {
				if (ref.current && !ref.current.contains(event.target as Node)) {
					onClose();
				}
			}

			document.addEventListener("mouseup", handleClickOutside);
			return () => {
				document.removeEventListener("mouseup", handleClickOutside);
			};
		}, [ref]);
	};

	// click outside of modal
	const wrapperRef = useRef(null);
	useOutsideClick(wrapperRef);

	return (
		<BaseModal visible={visible} wrapperRef={wrapperRef} modalStyle={{ paddingBottom: "1rem" }} hideHeader>
			{!isScreenSmall ? (
				<LinkButton data-testid="closeButton" onClick={onClose} style={{ position: "absolute", top: 0, right: "-3rem", height: "2rem", width: "2rem", borderRadius: "25px" }}>
					<XmarkIcon style={{ fontSize: "1.2rem", marginTop: 0, right: "10px", top: "6px" }} />
				</LinkButton>
			) : (
				<LinkButton data-testid="closeButton" onClick={onClose}>
					<XmarkIcon />
				</LinkButton>
			)}
			<video style={{ maxHeight: "30rem" }} src={videoUrl} controls autoPlay muted />
			<Flex style={{ gap: "1rem", alignItems: "center", flexDirection: "column", marginTop: "1rem" }}>
				<MainButton onClick={onContinue} data-testid="ModalOnContinue">
					{translation.modals.createVideo}
				</MainButton>
				<ThirdButton type="button" onClick={onClose} data-testid="ModalOnClose">
					{translation.general.close}
				</ThirdButton>
			</Flex>
		</BaseModal>
	);
};
