import React, { useRef, useEffect, RefObject } from "react";
import { useTranslation } from "../hooks/translations";
import { BaseModal } from "./BaseModal";
import { FlexCol, MainButton, ThirdButton } from "@src/styles/components";
import { ModalText } from "@src/styles/modals";

interface Props {
	visible: boolean;
	onCancel: () => void;
	onContinue: () => Promise<void>;
}

export const ConfirmChangesModal: React.FC<Props> = ({ visible, onCancel, onContinue }) => {
	const translation = useTranslation();

	const useOutsideClick = (ref: RefObject<HTMLElement>) => {
		useEffect(() => {
			function handleClickOutside(event: MouseEvent) {
				if (ref.current && !ref.current.contains(event.target as Node)) {
					onCancel();
				}
			}

			document.addEventListener("mouseup", handleClickOutside);
			return () => {
				document.removeEventListener("mouseup", handleClickOutside);
			};
		}, [ref]);
	};

	const wrapperRef = useRef(null);
	useOutsideClick(wrapperRef);

	return (
		<BaseModal
			modalStyle={{ width: "25rem" }}
			visible={visible}
			header={translation.modals.confirmChangesTitle}
			wrapperRef={wrapperRef}
		>
			<ModalText data-testid="ModalText">{translation.modals.confirmChangesText}</ModalText>
			<>
				<FlexCol>
					<MainButton type="submit" onClick={onContinue} className="mx-auto mt-3" data-testid="ModalContinue">
						{translation.general.confirm}
					</MainButton>
					<ThirdButton type="button" onClick={onCancel} className="mt-3 mx-auto" data-testid="ModalGoBack">
						{translation.general.cancel}
					</ThirdButton>
				</FlexCol>
			</>
		</BaseModal>
	);
};
