import React, { useRef, useEffect, RefObject } from "react";
import { useTranslation } from "../hooks/translations";
import { BaseModal } from "./BaseModal";
import { MainButton, ThirdButton } from "@src/styles/components";
import { ModalText } from "@src/styles/modals";
import { Form } from "react-bootstrap";
import { CustomInput } from "@src/styles/forms";

interface Props {
	invEmail: string;
	setInvEmail: (value: string) => void;
	visible: boolean;
	setVisible: (value: boolean) => void;
	validated: boolean;
	onCancel: () => void;
	setEnterPassword: (value: boolean) => void;
	handleInvite: () => void;
	passwordRequired: boolean;
	loading: boolean;
}

export const InviteUserModal: React.FC<Props> = ({ invEmail, setInvEmail, visible, setVisible, validated, onCancel, setEnterPassword, handleInvite, passwordRequired, loading }) => {
	const translation = useTranslation();
	const useOutsideClick = (ref: RefObject<HTMLElement>) => {
		useEffect(() => {
			function handleClickOutside(event: MouseEvent) {
				if (ref.current && !ref.current.contains(event.target as Node)) {
					onCancel();
				}
			}

			document.addEventListener("mouseup", handleClickOutside);
			return () => {
				document.removeEventListener("mouseup", handleClickOutside);
			};
		}, [ref]);
	};

	// click outside of modal
	const wrapperRef = useRef(null);
	useOutsideClick(wrapperRef);

	const handleSave = () => {
		if (passwordRequired) {
			setVisible(false);
			setEnterPassword(true);
		} else {
			handleInvite();
		}
	};

	return (
		<BaseModal visible={visible} header={translation.modals.inviteUserModalTitle} wrapperRef={wrapperRef}>
			<ModalText data-testid="ModalText">{translation.modals.inviteUserModalText}</ModalText>
			<>
				<Form className="mb-4 mt-4" noValidate onSubmit={(event) => event.preventDefault()} validated={validated} data-testid="inviteUserSection">
					<Form.Group className="mb-3" style={{ width: "95%", margin: "auto" }}>
						<CustomInput
							className="form-control"
							type="email"
							required
							autoFocus
							pattern="[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,20}$"
							placeholder={translation.general.email}
							value={invEmail}
							onInput={(e: React.ChangeEvent<HTMLInputElement>) => setInvEmail(("" + e.target.value).toLowerCase())}
							autoComplete="username"
							id="formEmail"
							data-testid="formEmail"
							onKeyDown={(e) => {
								if (e.key === "Enter") {
									handleSave();
								}
							}}
						/>
						<Form.Control.Feedback type="invalid" data-testid="emailTextError" style={{ textAlign: "center" }}>
							{translation.errors.emailFormat}
						</Form.Control.Feedback>
					</Form.Group>
					<MainButton type="button" onClick={handleSave} className="mx-auto mt-4" data-testid="ModalConfirmButton" disabled={loading} style={{ display: "block" }}>
						{translation.modals.inviteUserModalButton}
					</MainButton>
					<ThirdButton type="button" onClick={onCancel} className="mt-3 mx-auto" data-testid="ModalCancelLink" style={{ display: "block" }}>
						{translation.general.cancel}
					</ThirdButton>
				</Form>
			</>
		</BaseModal>
	);
};
