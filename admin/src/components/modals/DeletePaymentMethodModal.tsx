import React, { useEffect, RefObject, useRef } from "react";
import { useTranslation } from "../hooks/translations";
import { BaseModal } from "./BaseModal";
import { MainButton, ThirdButton } from "@src/styles/components";
import { ModalText } from "@src/styles/modals";
import { useTokenCheck } from "../hooks/useTokenCheck";
import deletePaymentMethod from "../utils/deletePaymentMethod";

interface Props {
	visible: boolean;
	onCancel: () => void;
	paymentMethodId?: string;
	setRefetch: (val: boolean) => void;
}

export const DeletePaymentMethodModal: React.FC<Props> = ({ visible, onCancel, paymentMethodId, setRefetch }) => {
	const translation = useTranslation();
	const { apiRetryHandler } = useTokenCheck();

	const useOutsideClick = (ref: RefObject<HTMLElement>) => {
		useEffect(() => {
			function handleClickOutside(event: MouseEvent) {
				if (ref.current && !ref.current.contains(event.target as Node)) {
					onCancel();
				}
			}

			document.addEventListener("mouseup", handleClickOutside);
			return () => {
				document.removeEventListener("mouseup", handleClickOutside);
			};
		}, [ref]);
	};

	// click outside of modal
	const wrapperRef = useRef(null);
	useOutsideClick(wrapperRef);

	const handleDelete = async () => {
		if (paymentMethodId) {
			await apiRetryHandler(async () => await deletePaymentMethod(paymentMethodId));
			setRefetch(true);
			onCancel();
		}
	};

	return (
		<BaseModal visible={visible} header={translation.modals.deletePaymentMethodTitle} wrapperRef={wrapperRef}>
			<ModalText data-testid="ModalText">{translation.modals.deletePaymentModalText}</ModalText>
			<>
				<MainButton type="button" onClick={handleDelete} className="mx-auto mt-3" data-testid="ModalConfirmButton">
					{translation.general.confirm}
				</MainButton>
				<ThirdButton type="button" onClick={onCancel} className="mt-3 mx-auto" data-testid="ModalCancelLink">
					{translation.general.cancel}
				</ThirdButton>
			</>
		</BaseModal>
	);
};
