import React, { useState, useEffect, useRef, RefObject } from "react";
import { useTranslation } from "../hooks/translations";
import { useNavigate } from "react-router-dom";
import { BaseModal } from "./BaseModal";
import { MainButton, ThirdButton, FlexCheckbox, CheckboxList, CoverImageBox, UploadImage } from "@src/styles/components";
import { ModalText } from "@src/styles/modals";
import { Form } from "react-bootstrap";
import { useTokenCheck } from "../hooks/useTokenCheck";
import getVideos from "../utils/getVideos";
import { shoppableVideo } from "@src/types/videos";
import Skeleton from "react-loading-skeleton";
import { registerEvent } from "@src/components/events/service";
import { EventNameEnum } from "@src/types/events";
import { DisplayFormatOptions } from "@src/types/snippetOptions";

interface Props {
	visible: boolean;
	onCancel: () => void;
	videosList: shoppableVideo[];
	setVideosList: (value: shoppableVideo[]) => void;
	hideCreateVideo: boolean;
}

export const VideosModal: React.FC<Props> = ({ visible, onCancel, videosList, setVideosList, hideCreateVideo }) => {
	const translation = useTranslation();
	const navigate = useNavigate();
	const { apiRetryHandler } = useTokenCheck();
	const [videos, setVideos] = useState<shoppableVideo[]>([]);
	const [videosTemp, setVideosTemp] = useState<shoppableVideo[]>(videosList);
	const [refetch, setRefetch] = useState(true);
	const showPlansPage = process.env.SHOW_PLANS_PAGE === "true";

	const checkedState = (video: shoppableVideo) => {
		return videosTemp.findIndex((item) => item._id === video._id) >= 0;
	};

	const handleOnChange = (video: shoppableVideo) => {
		const index = videosTemp.findIndex((item) => item._id === video._id);
		if (index >= 0) {
			const temp = [...videosTemp];
			temp.splice(index, 1);
			setVideosTemp(temp);
		} else {
			setVideosTemp([...videosTemp, video]);
		}
	};

	const addVideos = async (event: React.FormEvent<HTMLFormElement>) => {
		event.preventDefault();
		setVideosList(videosTemp);
		onCancel();
	};

	const handleOnCancel = () => {
		setVideosTemp(videosList);
		onCancel();
	};

	useEffect(() => {
		setVideosTemp(videosList);
	}, [videosList]);

	useEffect(() => {
		const go = async () => {
			setVideos([]);
			const { data } = await apiRetryHandler(async () => await getVideos());

			if (data?.length) {
				setVideos(data);
			}
			setRefetch(false);
		};

		if (refetch) {
			go();
		}
	}, [refetch, apiRetryHandler]);

	const useOutsideClick = (ref: RefObject<HTMLElement>) => {
		useEffect(() => {
			function handleClickOutside(event: MouseEvent) {
				if (ref.current && !ref.current.contains(event.target as Node)) {
					setVideosTemp(videosList);
					onCancel();
				}
			}

			document.addEventListener("mouseup", handleClickOutside);
			return () => {
				document.removeEventListener("mouseup", handleClickOutside);
			};
			// eslint-disable-next-line react-hooks/exhaustive-deps
		}, [ref, videosList, setVideosTemp]);
	};

	// click outside of modal
	const wrapperRef = useRef(null);
	useOutsideClick(wrapperRef);

	return (
		<BaseModal visible={visible} header={translation.modals.selectVideos} wrapperRef={wrapperRef}>
			{!videos?.length && !showPlansPage && hideCreateVideo ? (
				<ModalText data-testid="ModalText">{translation.modals.noVideos}</ModalText>
			) : (
				<ModalText data-testid="ModalText">{translation.modals.selectVideosList}</ModalText>
			)}
			<Form className="mb-4 mt-2" noValidate onSubmit={addVideos} data-testid="addVideos">
				<Form.Group className="mb-3" style={{ width: "95%", margin: "auto" }}>
					{refetch && <Skeleton className="mt-2" count={4} height={50} />}
					{videos?.length ? (
						<CheckboxList>
							{videos.map((video: shoppableVideo, index: number) => (
								<FlexCheckbox key={video._id} htmlFor={video._id}>
									<Form.Check
										type="checkbox"
										value={video._id}
										name={video._id}
										checked={checkedState(video)}
										onChange={() => handleOnChange(video)}
										id={video._id}
										style={{ marginRight: "10px" }}
										data-testid={`videCheckbox${index}`}
									/>
									<div style={{ width: "80px" }}>
										<CoverImageBox landscape={video.videoDisplayMode === DisplayFormatOptions.LANDSCAPE}>
											<UploadImage src={video.videoPosterURL} />
										</CoverImageBox>
									</div>
									{video.title}
								</FlexCheckbox>
							))}
						</CheckboxList>
					) : (
						""
					)}
				</Form.Group>
				{videos?.length ? (
					<MainButton
						type="submit"
						className="mx-auto mt-4"
						data-testid="ModalConfirmButton"
						style={{ display: "block" }}
					>
						{translation.modals.updateCollection}
					</MainButton>
				) : hideCreateVideo ? (
					showPlansPage && (
						<MainButton
							className="mx-auto mt-4"
							type="button"
							style={{ display: "block" }}
							onClick={() => {
								navigate("/plans-pricing");
							}}
						>
							{translation.modals.plansPricing}
						</MainButton>
					)
				) : (
					<MainButton
						className="mx-auto mt-4"
						type="button"
						data-testid="createVideo"
						style={{ display: "block" }}
						onClick={() => {
							registerEvent({
								eventName: EventNameEnum.CREATE_VIDEO_PRESS
							});
							navigate("/create-video");
						}}
					>
						{translation.createCollectionPage.createVideo}
					</MainButton>
				)}

				{!videos?.length && !showPlansPage && hideCreateVideo ? (
					<MainButton
						className="mx-auto mt-3"
						type="button"
						data-testid="closeWindow"
						style={{ display: "block" }}
						onClick={handleOnCancel}
					>
						{translation.modals.closeWindow}
					</MainButton>
				) : (
					<ThirdButton
						type="button"
						onClick={handleOnCancel}
						className="mt-3 mx-auto"
						data-testid="ModalCancelLink"
						style={{ display: "block" }}
					>
						{translation.general.cancel}
					</ThirdButton>
				)}
			</Form>
		</BaseModal>
	);
};
