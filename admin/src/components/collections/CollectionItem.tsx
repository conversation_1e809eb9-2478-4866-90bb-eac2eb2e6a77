import React, { <PERSON><PERSON><PERSON>, SetStateAction, useState, useEffect } from "react";
import { useTranslation } from "../hooks/translations";
import { useNavigate } from "react-router-dom";
import { TrashIcon, TrashIconGrey } from "@src/styles/forms";
import { DeleteCollectionModal } from "../modals/DeleteCollectionModal";
import { GreyButton, Flex, ListBox, ListItemContainer, ListRowItem } from "@src/styles/components";
import { ShoppableCollection } from "@src/types/collections";
import { ConfirmChangesModal } from "../modals/ConfirmChangesModal";
import { usePasswordRequired } from "../utils/getPasswordRequired";
import { useTokenCheck } from "../hooks/useTokenCheck";
import deleteCollection from "../utils/deleteCollection";
import { getErrorString } from "../utils/getErrorString";
import moment from "moment";
import { ShareModal } from "../modals/ShareModal";
import { ShareObject, ShareType } from "@src/types/share";
import getVideo from "../utils/getVideo";
import { Account } from "@src/types/account";
import ListShareButton from "../share/ListShareButton";

type Props = {
	accountData: Account | undefined;
	collectionItem: ShoppableCollection;
	refreshList: () => void;
	showDeleteError: (value: string) => void;
	showConfirmationText: (value: string) => void;
	numberId: number;
	setModalSnippet: Dispatch<SetStateAction<boolean>>;
	setCollectionId: Dispatch<SetStateAction<string>>;
};

const CollectionItem: React.FC<Props> = ({
	accountData,
	collectionItem,
	numberId,
	refreshList,
	showConfirmationText,
	showDeleteError,
	setModalSnippet,
	setCollectionId
}) => {
	const translation = useTranslation();
	const navigate = useNavigate();
	const [modalStatus, setModalStatus] = useState(false);
	const [enterPassword, setEnterPassword] = useState(false);
	const [shareModalStatus, setShareModalStatus] = useState(false);
	const [shareObject, setShareObject] = useState<ShareObject>();
	const passwordRequired = usePasswordRequired();
	const { apiRetryHandler } = useTokenCheck();
	const [loading, setLoading] = useState(false);

	const handleDelete = async () => {
		setLoading(true);
		const { error } = await apiRetryHandler(async () => await deleteCollection(collectionItem._id));
		refreshList();

		if (error) {
			const errorText = getErrorString(translation, error?.response?.data?.error);
			showDeleteError(errorText);
		} else {
			showConfirmationText(translation.modals.deleteCollectionConfirmation);
		}

		setModalStatus(false);
		setLoading(false);
	};

	useEffect(() => {
		(async () => {
			if (
				collectionItem?.shoppableVideos &&
				collectionItem?.shoppableVideos?.length > 0 &&
				collectionItem?.shoppableVideos[0] !== undefined
			) {
				// eslint-disable-next-line @typescript-eslint/no-non-null-assertion
				const { data } = await apiRetryHandler(async () => await getVideo(collectionItem.shoppableVideos![0]));
				if (data.shoppableVideo?.gifURL && data.shoppableVideo?.title) {
					setShareObject({
						videoId: data.shoppableVideo._id,
						collectionId: collectionItem._id,
						type: ShareType.SHARE_COLLECTION,
						gifURL: data.shoppableVideo.gifURL,
						videoTitle: data.shoppableVideo.title
					});
				}
			}
		})();
	}, [collectionItem, apiRetryHandler]);

	return (
		<>
			<ListItemContainer data-testid={`collectionsListRow${numberId}`}>
				{/* desktop view */}
				<div className="d-none d-lg-flex">
					<ListBox
						style={{ width: "45%" }}
						onClick={() => (numberId !== 0 ? navigate("/edit-collection/" + collectionItem._id) : navigate("/videos"))}
						clickable={true}
					>
						<ListRowItem disabled={numberId === 0 ? true : false} data-testid={`collectionsListTitle${numberId}`}>
							{numberId !== 0 ? collectionItem.title : translation.collectionsPage.allVideos}
						</ListRowItem>
					</ListBox>
					<ListBox
						style={{ width: "15%", flexShrink: 0 }}
						onClick={() => (numberId !== 0 ? navigate("/edit-collection/" + collectionItem._id) : navigate("/videos"))}
						clickable={true}
					>
						<ListRowItem disabled={numberId === 0 ? true : false} data-testid={`shoppableVideos${numberId}`}>
							{collectionItem.shoppableVideos?.length}
						</ListRowItem>
					</ListBox>
					<ListBox
						style={{ width: "17%", flexShrink: 0 }}
						onClick={() => (numberId !== 0 ? navigate("/edit-collection/" + collectionItem._id) : navigate("/videos"))}
						clickable={true}
					>
						<ListRowItem disabled={numberId === 0 ? true : false} data-testid={`listDate${numberId}`}>
							{moment(collectionItem.createdAt).format("DD/MM/YYYY")}
						</ListRowItem>
					</ListBox>
					<ListBox style={{ minWidth: "16rem", justifyContent: "flex-end" }}>
						<GreyButton
							type="button"
							onClick={() => {
								setModalSnippet(true);
								setCollectionId(collectionItem._id);
							}}
							style={{ marginRight: "10px", height: "45px" }}
							data-testid={`addToSiteButton${numberId}`}
						>
							{translation.general.addToSite}
						</GreyButton>

						<ListShareButton
							allowSharing={accountData?.subscription?.allowSharing === true}
							disabled={!shareObject}
							numberId={numberId}
							setShareModalStatus={setShareModalStatus}
						/>
					</ListBox>

					<ListBox style={{ width: "4rem" }} last={false}>
						<ListRowItem>
							{numberId !== 0 ? (
								<TrashIcon data-testid={`trashIcon${numberId}`} onClick={() => setModalStatus(true)} />
							) : (
								<TrashIconGrey data-testid={`trashIcon${numberId}`} />
							)}
						</ListRowItem>
					</ListBox>
				</div>

				{/* mobile view */}
				<div className="d-lg-none">
					<Flex>
						<ListBox
							style={{ width: "80%" }}
							onClick={() =>
								numberId !== 0 ? navigate("/edit-collection/" + collectionItem._id) : navigate("/videos")
							}
							clickable={true}
						>
							<ListRowItem disabled={numberId === 0 ? true : false} data-testid={`collectionsListTitle${numberId}`}>
								{numberId !== 0 ? collectionItem.title : translation.collectionsPage.allVideos}
							</ListRowItem>
						</ListBox>
						<ListRowItem style={{ width: "20%" }} className="text-end">
							{numberId !== 0 ? (
								<TrashIcon data-testid={`trashIcon${numberId}`} onClick={() => setModalStatus(true)} />
							) : (
								<TrashIconGrey data-testid={`trashIcon${numberId}`} />
							)}
						</ListRowItem>
					</Flex>
					<Flex>
						<ListRowItem
							style={{ width: "50%" }}
							className="text-center p-1"
							onClick={() =>
								numberId !== 0 ? navigate("/edit-collection/" + collectionItem._id) : navigate("/videos")
							}
						>
							<ListRowItem
								disabled={numberId === 0 ? true : false}
								className="p-0"
								data-testid={`shoppableVideos${numberId}`}
							>
								{collectionItem.shoppableVideos?.length} {translation.general.videos}
							</ListRowItem>
						</ListRowItem>
						<ListRowItem
							style={{ width: "50%" }}
							className="text-center p-1"
							onClick={() =>
								numberId !== 0 ? navigate("/edit-collection/" + collectionItem._id) : navigate("/videos")
							}
						>
							<ListRowItem disabled={numberId === 0 ? true : false} className="p-0" data-testid={`listDate${numberId}`}>
								{moment(collectionItem.createdAt).format("DD/MM/YYYY")}
							</ListRowItem>
						</ListRowItem>
					</Flex>
					<ListBox>
						<ListRowItem style={{ width: "50%" }} className="text-center p-0">
							<GreyButton
								type="button"
								onClick={() => {
									setModalSnippet(true);
									setCollectionId(collectionItem._id);
								}}
								style={{ height: "45px" }}
								data-testid={`addToSiteButton${numberId}`}
							>
								{translation.general.addToSite}
							</GreyButton>
						</ListRowItem>
						<ListRowItem style={{ width: "50%" }} className="text-center p-0">
							<ListShareButton
								allowSharing={accountData?.subscription?.allowSharing === true}
								disabled={!shareObject}
								numberId={numberId}
								setShareModalStatus={setShareModalStatus}
							/>
						</ListRowItem>
					</ListBox>
				</div>
			</ListItemContainer>

			<DeleteCollectionModal
				visible={modalStatus}
				setVisible={setModalStatus}
				onCancel={() => setModalStatus(false)}
				setEnterPassword={setEnterPassword}
				handleDelete={handleDelete}
				passwordRequired={passwordRequired ?? false}
				loading={loading}
			/>
			<ConfirmChangesModal visible={enterPassword} onCancel={() => setEnterPassword(false)} onContinue={handleDelete} />
			{shareObject && (
				<ShareModal
					allowSharing={accountData?.subscription?.allowSharing === true}
					visible={shareModalStatus}
					onCancel={() => setShareModalStatus(false)}
					shareObject={shareObject}
				/>
			)}
		</>
	);
};

export default CollectionItem;
