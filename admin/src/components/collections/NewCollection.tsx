import React, { useRef, useState, useEffect, RefObject } from "react";
import { Form, Row, Col } from "react-bootstrap";
import Spinner from "react-bootstrap/Spinner";
import {
	PageBody,
	PageRow,
	MainButton,
	VideosSection,
	VideoPageTitleText,
	CustomSwitch,
	FlexRow,
	FlexSwitchRow,
	FlexSwitchCol,
	ColorBoxContainer,
	BasicAccountSection,
	ListTabHeading,
	ListTabRow,
	ColorBox,
	ColorInput,
	ProLinkButton,
	ProIconHeader,
	ListTabArrow
} from "@src/styles/components";
import { HeadingText, HeadingTextH2, ConfirmationBoxWrapper, CustomInput, ReadOnlyInput } from "@src/styles/forms";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "../hooks/translations";
import { useTokenCheck } from "../hooks/useTokenCheck";
import { getErrorString } from "../utils/getErrorString";
import addCollection from "../utils/addCollection";
import { VideosModal } from "../modals/VideosModal";
import CollectionVideos from "@src/components/dashboard/CollectionVideos";
import { ConfirmLeavingModal } from "../modals/ConfirmLeavingModal";
import { ConfirmChangesModal } from "../modals/ConfirmChangesModal";
import { usePasswordRequired } from "../utils/getPasswordRequired";
import ErrorMessage from "@src/components/utils/ErrorMessage";
import { shoppableVideo, DisplayPreferencesTab } from "@src/types/videos";
import { HexColorPicker } from "react-colorful";
import { ArrowUp, ArrowDown, StarIcon, OpenLockIcon } from "@src/assets";
import { fontOptions } from "@src/types/snippetOptions";
import { Account } from "../../types/account";
import { apTheme } from "@src/config/theme";
import { useTheme } from "styled-components";
import SVG from "../svg/SVG";

interface Props {
	accountData: Account | undefined;
	saveChanges: boolean;
	setSaveChanges: (value: boolean) => void;
	navigationUrl: string;
	modalStatus: boolean;
	setModalStatus: (value: boolean) => void;
	hideCreateVideo: boolean;
}

// eslint-disable-next-line max-lines-per-function
const NewCollection: React.FC<Props> = ({
	accountData,
	saveChanges,
	setSaveChanges,
	navigationUrl,
	modalStatus,
	setModalStatus,
	hideCreateVideo
}) => {
	const translation = useTranslation();
	const { apiRetryHandler } = useTokenCheck();
	const navigate = useNavigate();
	const [collectionError, setCollectionError] = useState("");
	const [generateButton, setGenerateButton] = useState(true);
	const [loading, setLoading] = useState(false);
	const [collectionTitle, setCollectionTitle] = useState("");
	const [videos, setVideos] = useState<shoppableVideo[]>([]);
	const [videosModalStatus, setVideosModalStatus] = useState(false);
	const [goToPrevPage, setGoToPrevPage] = useState(false);
	const [enterPassword, setEnterPassword] = useState(false);
	const passwordRequired = usePasswordRequired();
	const [selectedTab, setSelectedTab] = useState<DisplayPreferencesTab>(DisplayPreferencesTab.GENERAL);
	const [isPickerVisible, setPickerVisible] = useState(false);
	const [isTextColorVisible, setIsTextColorVisible] = useState(false);
	const [buttonBackgroundColor, setButtonBackgroundColor] = useState("#061714");
	const [iconTextColor, setIconTextColor] = useState("#FFFFFF");
	const [buttonBackgroundBlur, setButtonBackgroundBlur] = useState(true);
	const [selectedFont, setSelectedFont] = useState(fontOptions[0].value);
	const [borderRadius, setBorderRadius] = useState(10);
	const [centerOnPage, setCenterOnPage] = useState(false);
	const [margin, setMargin] = useState(0);
	const [paddingBetween, setPaddingBetween] = useState(24);
	const [widgetBorderRadius, setWidgetBorderRadius] = useState(10);
	const [widgetPosition, setWidgetPosition] = useState("right");
	const [inlineBorderRadius, setInlineBorderRadius] = useState(10);
	const theme: apTheme = useTheme() as apTheme;

	const createCollection = async () => {
		setGenerateButton(true);
		setLoading(true);
		const { data, error } = await apiRetryHandler(
			async () =>
				await addCollection({
					collectionTitle,
					videos,
					buttonBackgroundColor,
					buttonBackgroundBlur,
					iconTextColor,
					selectedFont,
					borderRadius,
					centerOnPage,
					margin,
					paddingBetween,
					widgetBorderRadius,
					widgetPosition,
					inlineBorderRadius
				})
		);
		setGenerateButton(false);
		setLoading(false);
		if (error) {
			const errorText = getErrorString(translation, error?.response?.data?.error);
			setCollectionError(errorText);
		} else {
			navigate("/edit-collection/" + data?.shoppableCollection);
		}
	};

	const handleSave = () => {
		if (passwordRequired) {
			setEnterPassword(true);
		} else {
			createCollection();
		}
	};

	useEffect(() => {
		// Prevent navigation if changes are not saved
		if (saveChanges) window.history.pushState(null, "", window.location.pathname);

		// prompt before refresh page
		const handleBeforeUnload = (event: {preventDefault: () => void; returnValue: string}) => {
			if (saveChanges) {
				event.preventDefault();
				// Required for some browsers
				event.returnValue = "";
			}
		};

		const handlePopState = () => {
			if (saveChanges) {
				window.history.pushState(null, "", window.location.pathname);
				setModalStatus(true);
				setGoToPrevPage(true);
			}
		};

		window.addEventListener("beforeunload", handleBeforeUnload);
		window.addEventListener("popstate", handlePopState);

		return () => {
			window.removeEventListener("beforeunload", handleBeforeUnload);
			window.removeEventListener("popstate", handlePopState);
		};
	}, [saveChanges, setModalStatus]);

	useEffect(() => {
		if (collectionTitle) {
			setGenerateButton(false);
		} else {
			setGenerateButton(true);
		}

		if (collectionTitle || videos?.length > 0) {
			setSaveChanges(true);
		} else {
			setSaveChanges(false);
		}
	}, [collectionTitle, videos, setSaveChanges]);

	const handleTabClick = (tab: DisplayPreferencesTab) => {
		setSelectedTab((prevTab) => (prevTab === tab ? DisplayPreferencesTab.CLOSEALL : tab));
	};

	const useOutsideClick = (ref: RefObject<HTMLDivElement>) => {
		useEffect(() => {
			function handleClickOutside(event: MouseEvent) {
				if (ref.current && !ref.current.contains(event.target as Node)) {
					setPickerVisible(false);
					setIsTextColorVisible(false);
				}
			}

			document.addEventListener("mouseup", handleClickOutside);
			return () => {
				document.removeEventListener("mouseup", handleClickOutside);
			};
		}, [ref]);
	};

	const wrapperRef = useRef<HTMLDivElement>(null);
	useOutsideClick(wrapperRef);

	return (
		<>
			<ConfirmationBoxWrapper>
				{!!collectionError && (
					<ErrorMessage error={collectionError} setError={setCollectionError} displayCloseIcon={true} />
				)}
			</ConfirmationBoxWrapper>
			<PageBody>
				<PageRow className="mb-4">
					<HeadingText data-testid="editVideoPage">
						{collectionTitle ? collectionTitle : translation.createCollectionPage.untitledCollection}
					</HeadingText>
					<MainButton type="button" data-testid="updateButton" disabled={generateButton} onClick={handleSave}>
						{translation.profilePage.saveChanges}
						{loading ? (
							<Spinner as="span" size="sm" role="status" aria-hidden="true" style={{ marginLeft: "1rem" }} />
						) : (
							""
						)}
					</MainButton>
				</PageRow>

				<Row pl="0" pr="0">
					<Col sm="12" md="4" className="mb-4">
						<VideoPageTitleText className="mb-3">{translation.general.title}</VideoPageTitleText>
						<Form.Group className="mb-3">
							<CustomInput
								className="form-control"
								type="text"
								required
								placeholder={translation.createCollectionPage.placeholder}
								id="collectionTitle"
								data-testid="collectionTitle"
								onChange={(value) => {
									setCollectionTitle(value.target.value);
								}}
								value={collectionTitle}
							/>
						</Form.Group>

						<FlexRow className="mt-3 mb-2" style={{ alignItems: "start" }}>
							<VideoPageTitleText className="mb-3">
								{translation.appCustomization.displayPreferences}
							</VideoPageTitleText>
							{process.env.SHOW_PLANS_PAGE === "true" &&
								(!accountData?.subscription.allowThemes && (
									<ProLinkButton basic={true} onClick={() => navigate("/plans-pricing")}>
										<ProIconHeader data-testid="StarIcon" src={StarIcon} />
										{translation.general.unlock}
									</ProLinkButton>
								))
							}
						</FlexRow>

						{!accountData?.subscription.allowThemes ? (
							<>
								<ListTabRow className="mb-3">
									<ListTabHeading pro={false} onClick={() => handleTabClick(DisplayPreferencesTab.GENERAL)}>
										<FlexRow>
											<div style={{ marginRight: "10px", lineHeight: "0" }}>
												<SVG src={OpenLockIcon} width="16px" height="20px" fill={theme.colors.apGreyButton} />
											</div>
											{translation.appCustomization.generalTab}
										</FlexRow>
										<ListTabArrow>
											{selectedTab === DisplayPreferencesTab.GENERAL ? (
												<SVG src={ArrowUp} fill={theme.colors.apGreyButton} />
											) : (
												<SVG src={ArrowDown} fill={theme.colors.apGreyButton} />
											)}
										</ListTabArrow>
									</ListTabHeading>
									{selectedTab === DisplayPreferencesTab.GENERAL && (
										<BasicAccountSection>
											<FlexRow className="mt-3 mb-3" style={{ position: "relative" }}>
												{translation.appCustomization.buttonBackgroundColor}
												<ColorBoxContainer>
													<ColorBox color={"#061714"}></ColorBox>
													<ColorInput type="text" readOnly value={"#061714"} maxLength={7} />
												</ColorBoxContainer>
											</FlexRow>
											<FlexRow className="mb-4">
												{translation.appCustomization.buttonBackgroundBlur}
												<CustomSwitch style={{ fontSize: "1rem" }} readOnly type="switch" checked={true} />
											</FlexRow>
											<FlexRow className="mb-3" style={{ position: "relative" }}>
												{translation.appCustomization.iconTextColor}
												<ColorBoxContainer>
													<ColorBox color={"#FFFFFF"}></ColorBox>
													<ColorInput type="text" readOnly value={"#FFFFFF"} maxLength={7} />
												</ColorBoxContainer>
											</FlexRow>
											<FlexRow className="mb-1">
												{translation.appCustomization.font}
												<Form.Select
													id="font-select"
													disabled
													value={selectedFont}
													style={{
														border: "none",
														width: "200px",
														backgroundColor: theme.colors.apBackgroundColor,
														color: theme.colors.apInputColor
													}}
												>
													<option key={fontOptions[0].value} value={fontOptions[0].value}>
														{fontOptions[0].label}
													</option>
												</Form.Select>
											</FlexRow>
										</BasicAccountSection>
									)}
								</ListTabRow>

								<ListTabRow className="mb-3">
									<ListTabHeading pro={false} onClick={() => handleTabClick(DisplayPreferencesTab.CAROUSEL)}>
										<FlexRow>
											<div style={{ marginRight: "10px", lineHeight: "0" }}>
												<SVG src={OpenLockIcon} width="16px" height="20px" fill={theme.colors.apGreyButton} />
											</div>
											{translation.appCustomization.carouselTab}
										</FlexRow>
										<ListTabArrow>
											{selectedTab === DisplayPreferencesTab.CAROUSEL ? (
												<SVG src={ArrowUp} fill={theme.colors.apGreyButton} />
											) : (
												<SVG src={ArrowDown} fill={theme.colors.apGreyButton} />
											)}
										</ListTabArrow>
									</ListTabHeading>
									{selectedTab === DisplayPreferencesTab.CAROUSEL && (
										<BasicAccountSection>
											<FlexRow className="mt-3">{translation.appCustomization.borderRadius}</FlexRow>
											<FlexRow className="mb-3">
												<Form.Range value={borderRadius} min="0" max="35" step="1" readOnly />
												<ReadOnlyInput type="text" readOnly value={borderRadius} style={{ marginLeft: "10px" }} />
											</FlexRow>

											<FlexRow className="mb-4">
												<FlexRow>{translation.appCustomization.centerOnPage}</FlexRow>
												<CustomSwitch type="switch" style={{ fontSize: "1rem" }} readOnly checked={centerOnPage} />
											</FlexRow>

											<FlexRow>{translation.appCustomization.MarginLeftRight}</FlexRow>
											<FlexRow className="mb-3">
												<Form.Range value={margin} min="0" max="200" step="1" readOnly />
												<ReadOnlyInput type="text" readOnly value={margin + "px"} style={{ marginLeft: "10px" }} />
											</FlexRow>
											<FlexRow>{translation.appCustomization.paddingBetween}</FlexRow>
											<FlexRow className="mb-3">
												<Form.Range value={paddingBetween} min="0" max="100" step="1" readOnly />
												<ReadOnlyInput
													type="text"
													readOnly
													value={paddingBetween + "px"}
													style={{ marginLeft: "10px" }}
												/>
											</FlexRow>
										</BasicAccountSection>
									)}
								</ListTabRow>

								<ListTabRow className="mb-3">
									<ListTabHeading pro={false} onClick={() => handleTabClick(DisplayPreferencesTab.WIDGET)}>
										<FlexRow>
											<div style={{ marginRight: "10px", lineHeight: "0" }}>
												<SVG src={OpenLockIcon} width="16px" height="20px" fill={theme.colors.apGreyButton} />
											</div>
											{translation.appCustomization.widgetTab}
										</FlexRow>
										<ListTabArrow>
											{selectedTab === DisplayPreferencesTab.WIDGET ? (
												<SVG src={ArrowUp} fill={theme.colors.apGreyButton} />
											) : (
												<SVG src={ArrowDown} fill={theme.colors.apGreyButton} />
											)}
										</ListTabArrow>
									</ListTabHeading>
									{selectedTab === DisplayPreferencesTab.WIDGET && (
										<BasicAccountSection>
											<FlexRow className="mt-3">{translation.appCustomization.borderRadius}</FlexRow>
											<FlexRow className="mb-3">
												<Form.Range value={widgetBorderRadius} min="0" max="25" step="1" readOnly />
												<ReadOnlyInput type="text" readOnly value={widgetBorderRadius} style={{ marginLeft: "10px" }} />
											</FlexRow>
											<FlexRow className="mb-3">{translation.appCustomization.position}</FlexRow>
											<FlexSwitchRow>
												<FlexSwitchCol active={widgetPosition === "left"}>
													{translation.appCustomization.leftCorner}
												</FlexSwitchCol>
												<FlexSwitchCol active={widgetPosition === "right"}>
													{translation.appCustomization.rightCorner}
												</FlexSwitchCol>
											</FlexSwitchRow>
										</BasicAccountSection>
									)}
								</ListTabRow>

								<ListTabRow className="mb-3">
									<ListTabHeading pro={false} onClick={() => handleTabClick(DisplayPreferencesTab.INLINE)}>
										<FlexRow>
											<div style={{ marginRight: "10px", lineHeight: "0" }}>
												<SVG src={OpenLockIcon} width="16px" height="20px" fill={theme.colors.apGreyButton} />
											</div>
											{translation.appCustomization.inlineTab}
										</FlexRow>
										<ListTabArrow>
											{selectedTab === DisplayPreferencesTab.INLINE ? (
												<SVG src={ArrowUp} fill={theme.colors.apGreyButton} />
											) : (
												<SVG src={ArrowDown} fill={theme.colors.apGreyButton} />
											)}
										</ListTabArrow>
									</ListTabHeading>
									{selectedTab === DisplayPreferencesTab.INLINE && (
										<BasicAccountSection>
											<FlexRow className="mt-3">{translation.appCustomization.borderRadius}</FlexRow>
											<FlexRow className="mb-3">
												<Form.Range value={inlineBorderRadius} min="0" max="40" step="1" readOnly />
												<ReadOnlyInput type="text" readOnly value={inlineBorderRadius} style={{ marginLeft: "10px" }} />
											</FlexRow>
										</BasicAccountSection>
									)}
								</ListTabRow>
							</>
						) : (
							<>
								<ListTabRow className="mb-3">
									<ListTabHeading pro={true} onClick={() => handleTabClick(DisplayPreferencesTab.GENERAL)}>
										{translation.appCustomization.generalTab}
										<ListTabArrow>
											{selectedTab === DisplayPreferencesTab.GENERAL ? (
												<SVG src={ArrowUp} fill={theme.colors.apGreyButton} />
											) : (
												<SVG src={ArrowDown} fill={theme.colors.apGreyButton} />
											)}
										</ListTabArrow>
									</ListTabHeading>
									{selectedTab === DisplayPreferencesTab.GENERAL && (
										<>
											<FlexRow className="mt-3 mb-3" style={{ position: "relative" }}>
												{translation.appCustomization.buttonBackgroundColor}
												<ColorBoxContainer>
													<ColorBox
														color={buttonBackgroundColor}
														onClick={() => {
															setPickerVisible(!isPickerVisible);
														}}
													></ColorBox>
													<ColorInput
														type="text"
														value={buttonBackgroundColor}
														onFocus={() => {
															setPickerVisible(!isPickerVisible);
														}}
														onChange={(value) => {
															setButtonBackgroundColor(value.target.value);
														}}
														maxLength={7}
													/>
												</ColorBoxContainer>
												{isPickerVisible && (
													<div ref={wrapperRef} style={{ position: "absolute", right: "0", top: "40px", zIndex: 1 }}>
														<HexColorPicker
															color={buttonBackgroundColor}
															onChange={setButtonBackgroundColor}
															style={{ cursor: "pointer" }}
														/>
													</div>
												)}
											</FlexRow>
											<FlexRow className="mb-4">
												{translation.appCustomization.buttonBackgroundBlur}
												<CustomSwitch
													style={{ fontSize: "1rem" }}
													type="switch"
													checked={buttonBackgroundBlur}
													onChange={() => setButtonBackgroundBlur((state) => !state)}
												/>
											</FlexRow>
											<FlexRow className="mb-3" style={{ position: "relative" }}>
												{translation.appCustomization.iconTextColor}
												<ColorBoxContainer>
													<ColorBox
														color={iconTextColor}
														onClick={() => {
															setIsTextColorVisible(!isTextColorVisible);
														}}
													></ColorBox>
													<ColorInput
														type="text"
														value={iconTextColor}
														onFocus={() => {
															setIsTextColorVisible(!isTextColorVisible);
														}}
														onChange={(value) => {
															setIconTextColor(value.target.value);
														}}
														maxLength={7}
													/>
												</ColorBoxContainer>
												{isTextColorVisible && (
													<div ref={wrapperRef} style={{ position: "absolute", right: "0", top: "40px", zIndex: 1 }}>
														<HexColorPicker
															color={iconTextColor}
															onChange={setIconTextColor}
															style={{ cursor: "pointer" }}
														/>
													</div>
												)}
											</FlexRow>
											<FlexRow className="mb-1">
												{translation.appCustomization.font}
												<Form.Select
													id="font-select"
													value={selectedFont}
													onChange={(e) => setSelectedFont(e.target.value)}
													style={{
														border: "none",
														width: "200px",
														backgroundColor: theme.colors.apBackgroundColor,
														color: theme.colors.apInputColor
													}}
												>
													{fontOptions.map((font) => (
														<option key={font.value} value={font.value}>
															{font.label}
														</option>
													))}
												</Form.Select>
											</FlexRow>
										</>
									)}
								</ListTabRow>

								<ListTabRow className="mb-3">
									<ListTabHeading pro={true} onClick={() => handleTabClick(DisplayPreferencesTab.CAROUSEL)}>
										{translation.appCustomization.carouselTab}
										<ListTabArrow>
											{selectedTab === DisplayPreferencesTab.CAROUSEL ? (
												<SVG src={ArrowUp} fill={theme.colors.apGreyButton} />
											) : (
												<SVG src={ArrowDown} fill={theme.colors.apGreyButton} />
											)}
										</ListTabArrow>
									</ListTabHeading>
									{selectedTab === DisplayPreferencesTab.CAROUSEL && (
										<>
											<FlexRow className="mt-3">{translation.appCustomization.borderRadius}</FlexRow>
											<FlexRow className="mb-3">
												<Form.Range
													value={borderRadius}
													onChange={(e) => setBorderRadius(parseFloat(e.target.value))}
													min="0"
													max="35"
													step="1"
												/>
												<ReadOnlyInput type="text" readOnly value={borderRadius} style={{ marginLeft: "10px" }} />
											</FlexRow>

											<FlexRow className="mb-4">
												<FlexRow>{translation.appCustomization.centerOnPage}</FlexRow>
												<CustomSwitch
													type="switch"
													style={{ fontSize: "1rem" }}
													checked={centerOnPage}
													onChange={() => setCenterOnPage((state) => !state)}
												/>
											</FlexRow>

											<FlexRow>{translation.appCustomization.MarginLeftRight}</FlexRow>
											<FlexRow className="mb-3">
												<Form.Range
													value={margin}
													onChange={(e) => setMargin(parseInt(e.target.value))}
													min="0"
													max="200"
													step="1"
												/>
												<ReadOnlyInput type="text" readOnly value={margin + "px"} style={{ marginLeft: "10px" }} />
											</FlexRow>
											<FlexRow>{translation.appCustomization.paddingBetween}</FlexRow>
											<FlexRow className="mb-3">
												<Form.Range
													value={paddingBetween}
													onChange={(e) => setPaddingBetween(parseInt(e.target.value))}
													min="0"
													max="100"
													step="1"
												/>
												<ReadOnlyInput
													type="text"
													readOnly
													value={paddingBetween + "px"}
													style={{ marginLeft: "10px" }}
												/>
											</FlexRow>
										</>
									)}
								</ListTabRow>

								<ListTabRow className="mb-3">
									<ListTabHeading pro={true} onClick={() => handleTabClick(DisplayPreferencesTab.WIDGET)}>
										{translation.appCustomization.widgetTab}
										<ListTabArrow>
											{selectedTab === DisplayPreferencesTab.WIDGET ? (
												<SVG src={ArrowUp} fill={theme.colors.apGreyButton} />
											) : (
												<SVG src={ArrowDown} fill={theme.colors.apGreyButton} />
											)}
										</ListTabArrow>
									</ListTabHeading>
									{selectedTab === DisplayPreferencesTab.WIDGET && (
										<>
											<FlexRow className="mt-3">{translation.appCustomization.borderRadius}</FlexRow>
											<FlexRow className="mb-3">
												<Form.Range
													value={widgetBorderRadius}
													onChange={(e) => setWidgetBorderRadius(parseFloat(e.target.value))}
													min="0"
													max="25"
													step="1"
												/>
												<ReadOnlyInput type="text" readOnly value={widgetBorderRadius} style={{ marginLeft: "10px" }} />
											</FlexRow>
											<FlexRow className="mb-3">{translation.appCustomization.position}</FlexRow>
											<FlexSwitchRow>
												<FlexSwitchCol active={widgetPosition === "left"} onClick={() => setWidgetPosition("left")}>
													{translation.appCustomization.leftCorner}
												</FlexSwitchCol>
												<FlexSwitchCol active={widgetPosition === "right"} onClick={() => setWidgetPosition("right")}>
													{translation.appCustomization.rightCorner}
												</FlexSwitchCol>
											</FlexSwitchRow>
										</>
									)}
								</ListTabRow>

								<ListTabRow className="mb-3">
									<ListTabHeading pro={true} onClick={() => handleTabClick(DisplayPreferencesTab.INLINE)}>
										{translation.appCustomization.inlineTab}
										<ListTabArrow>
											{selectedTab === DisplayPreferencesTab.INLINE ? (
												<SVG src={ArrowUp} fill={theme.colors.apGreyButton} />
											) : (
												<SVG src={ArrowDown} fill={theme.colors.apGreyButton} />
											)}
										</ListTabArrow>
									</ListTabHeading>
									{selectedTab === DisplayPreferencesTab.INLINE && (
										<>
											<FlexRow className="mt-3">{translation.appCustomization.borderRadius}</FlexRow>
											<FlexRow className="mb-3">
												<Form.Range
													value={inlineBorderRadius}
													onChange={(e) => setInlineBorderRadius(parseFloat(e.target.value))}
													min="0"
													max="40"
													step="1"
												/>
												<ReadOnlyInput type="text" readOnly value={inlineBorderRadius} style={{ marginLeft: "10px" }} />
											</FlexRow>
										</>
									)}
								</ListTabRow>
							</>
						)}
					</Col>
					<Col sm="12" md="8" className="mb-4">
						<VideoPageTitleText className="mb-3">{translation.createCollectionPage.videos}</VideoPageTitleText>

						<VideosSection style={videos.length === 0 ? { padding: "15%" } : { padding: "5% 15%" }}>
							<HeadingTextH2 className="mb-3">
								{videos.length === 0
									? translation.createCollectionPage.addVideo
									: translation.createCollectionPage.addVideos}
							</HeadingTextH2>
							<MainButton
								className="mt-3"
								type="button"
								data-testid="selectVideos"
								onClick={() => {
									setVideosModalStatus(true);
								}}
							>
								{translation.createCollectionPage.selectVideos}
							</MainButton>
						</VideosSection>

						{videos?.length > 0 && <CollectionVideos videos={videos} setVideos={setVideos} />}
					</Col>
				</Row>
			</PageBody>
			<VideosModal
				visible={videosModalStatus}
				videosList={videos}
				setVideosList={setVideos}
				onCancel={() => setVideosModalStatus(false)}
				hideCreateVideo={hideCreateVideo}
			/>
			<ConfirmLeavingModal
				visible={modalStatus}
				onCancel={() => {
					setModalStatus(false);
					setGoToPrevPage(false);
				}}
				onContinue={() => {
					if (goToPrevPage) window.history.go(-2);
					else navigate(navigationUrl);
				}}
			/>
			<ConfirmChangesModal
				visible={enterPassword}
				onCancel={() => setEnterPassword(false)}
				onContinue={createCollection}
			/>
		</>
	);
};

export default NewCollection;
