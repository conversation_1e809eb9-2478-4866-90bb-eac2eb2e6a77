import React, { Dispatch, SetStateAction } from "react";
import { useTranslation } from "../hooks/translations";
import CollectionItem from "./CollectionItem";
import { Flex, ListBox, HeaderListTitle } from "@src/styles/components";
import { ShoppableCollection } from "@src/types/collections";
import { Account } from "@src/types/account";

interface Props {
	accountData: Account | undefined;
	collections: ShoppableCollection[];
	refreshList: () => void;
	showConfirmationText: (value: string) => void;
	showDeleteError: (value: string) => void;
	setModalSnippet: Dispatch<SetStateAction<boolean>>;
	setCollectionId: Dispatch<SetStateAction<string>>;
}

const CollectionsList: React.FC<Props> = ({ accountData, collections, refreshList, showConfirmationText, showDeleteError, setModalSnippet, setCollectionId }) => {
	const translation = useTranslation();

	return (
		<>
			<div className="mt-4">&nbsp;</div>
			<Flex className="d-none d-lg-flex">
				<ListBox style={{ width: "45%" }}>
					<HeaderListTitle data-testid="collectionsListTitle">{translation.collectionsPage.title}</HeaderListTitle>
				</ListBox>
				<ListBox style={{ width: "15%", flexShrink: 0 }}>
					<HeaderListTitle data-testid="collectionsListVideos">{translation.collectionsPage.videos}</HeaderListTitle>
				</ListBox>
				<ListBox style={{ width: "17%", flexShrink: 0 }}>
					<HeaderListTitle data-testid="collectionsListDate">{translation.collectionsPage.dateCreated}</HeaderListTitle>
				</ListBox>
				<ListBox style={{ minWidth: "calc(20rem - 5px)" }} last={true}>
					<HeaderListTitle data-testid="collectionsListActions">{translation.collectionsPage.actions}</HeaderListTitle>
				</ListBox>
			</Flex>
			{collections.map((collection: ShoppableCollection, index: number) => (
				<CollectionItem accountData={accountData} numberId={index} key={collection._id} collectionItem={collection} refreshList={refreshList} showConfirmationText={showConfirmationText} showDeleteError={showDeleteError} setModalSnippet={setModalSnippet} setCollectionId={setCollectionId} />
			))}
		</>
	);
};

export default CollectionsList;
