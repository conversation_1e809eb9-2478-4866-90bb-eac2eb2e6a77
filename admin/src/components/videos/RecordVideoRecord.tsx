import React, { useEffect, useRef, useState } from "react";
import { Flex, FormText, MainButton, PageSection } from "@src/styles/components";
import InputDevicesSelect from "../inputs/InputDevicesSelect";

interface Props {
	setIsOnEditPage: (val: boolean) => void;
}

/* eslint-disable @typescript-eslint/no-unused-vars */
const RecordVideoRecord: React.FC<Props> = ({ setIsOnEditPage }) => {

	const [videoStream, setVideoStream] = useState<MediaStream | undefined>();
	const [audioStream, setAudioStream] = useState<MediaStream | undefined>();
	const [screenStream, setScreenStream] = useState<MediaStream | undefined>();
	
	return (
		<>
			<FormText>
				**Record Page Placeholder
			</FormText>
			<Flex>
				<MainButton 
					onClick={() => {
						const videoElement = document.getElementById("placeholder-video") as HTMLVideoElement;
						if (videoElement && videoStream) {
							videoElement.srcObject = videoStream;
						}
					}}
					disabled = {videoStream ? false : true}
				>Show Camera</MainButton>
				<MainButton 
					onClick={() => {
						const videoElement = document.getElementById("placeholder-video") as HTMLVideoElement;
						if (videoElement && screenStream) {
							videoElement.srcObject = screenStream;
						}
					}}
					disabled={screenStream ? false : true}
				>Show Screen</MainButton>
			</Flex>
			<Flex style={{justifyContent: "space-between", gap:"20px"}}>
				<PageSection style={{ flex: 2 }}>
					<video id="placeholder-video" style={{ width: "100%" }} autoPlay playsInline muted ></video>
				</PageSection>
				<div style={{ flex: 1 }}>
					<InputDevicesSelect
						disabled={false}
						videoStream={videoStream}
						setVideoStream={setVideoStream}
						audioStream={audioStream}
						setAudioStream={setAudioStream}
						screenStream={screenStream}
						setScreenStream={setScreenStream}
					/>
				</div>
			</Flex>
		</>
	);
};

export default RecordVideoRecord;
