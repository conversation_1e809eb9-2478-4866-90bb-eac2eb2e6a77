import React, { useEffect, useState } from "react";
import { useTranslation } from "../hooks/translations";
import Header from "../header/Header";
import Sidebar from "../sidebar/Sidebar";
import VideoLibrary from "@src/components/videos/VideoLibrary";
import getAccountData from "../utils/getAccountData";
import { useTokenCheck } from "../hooks/useTokenCheck";
import { Account } from "@src/types/account";
import { GeneralUsageModal } from "../modals/GeneralUsageModal";
import { compareImpressions } from "@src/utils/compare";

const VideoLibraryPage: React.FC = () => {
	const translation = useTranslation();
	const [navigationUrl, setNavigationUrl] = useState("/");
	const [saveChanges, setSaveChanges] = useState(false);
	const [showConfirmLeavingModal, setShowConfirmLeavingModal] = useState(false);
	const { apiRetry<PERSON>and<PERSON> } = useTokenCheck();
	const [trialActive, setTrialActive] = useState(false);
	const [accountStatus, setAccountStatus] = useState(true);
	const [accountData, setAccountData] = useState<Account>();
	const [generalModalStatus, setGeneralModalStatus] = useState(false);
	const [hideCreateVideo, setHideCreateVideo] = useState(false);
	const showPlansPage = process.env.SHOW_PLANS_PAGE === "true";

	useEffect(() => {
		document.title = translation.videoLibraryPage.pageTitle;
	}, [translation.videoLibraryPage.pageTitle]);

	useEffect(() => {
		const go = async () => {
			const { data: accountData } = await apiRetryHandler(async () => await getAccountData());
			if (accountData) {
				setAccountData(accountData);
				setTrialActive(accountData.subscription?.trialActive);

				if (
					compareImpressions(
						accountData?.totalImpressionsCurrentCycle,
						accountData.subscription?.maxImpressionsPerCycle
					)
				) {
					setGeneralModalStatus(true);
					setHideCreateVideo(true);
				}
			}

			setAccountStatus(false);
		};

		if (accountStatus) {
			go();
		}
	}, [accountStatus, apiRetryHandler]);

	return (
		<>
			<Sidebar
				accountData={accountData}
				saveChanges={saveChanges}
				setNavigationUrl={setNavigationUrl}
				setModalStatus={setShowConfirmLeavingModal}
			/>
			<Header
				auth={true}
				accountData={accountData}
				saveChanges={saveChanges}
				trialActive={trialActive}
				setChangesModal={setShowConfirmLeavingModal}
				setNavigationUrl={setNavigationUrl}
			/>
			<VideoLibrary
				saveChanges={saveChanges}
				setSaveChanges={setSaveChanges}
				navigationUrl={navigationUrl}
				showConfirmLeavingModal={showConfirmLeavingModal}
				setShowConfirmLeavingModal={setShowConfirmLeavingModal}
				hideCreateVideo={hideCreateVideo}
			/>
			<GeneralUsageModal visible={showPlansPage && generalModalStatus} onCancel={() => setGeneralModalStatus(false)} />
		</>
	);
};

export default VideoLibraryPage;
