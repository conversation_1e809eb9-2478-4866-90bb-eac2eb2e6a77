import React, { useEffect, useState } from "react";
import { useTranslation } from "../hooks/translations";
import Header from "../header/Header";
import EditCollectionPage from "@src/components/collections/EditCollectionPage";
import Sidebar from "../sidebar/Sidebar";
import { SnippetOptionsModal } from "../modals/SnippetOptionsModal";
import { useParams } from "react-router-dom";
import getAccountData from "../utils/getAccountData";
import { useTokenCheck } from "../hooks/useTokenCheck";
import { Account } from "@src/types/account";
import { compareImpressions } from "@src/utils/compare";

const EditCollection: React.FC = () => {
	const translation = useTranslation();
	const [modalStatus, setModalStatus] = useState(false);
	const [saveChanges, setSaveChanges] = useState(false);
	const [navigationUrl, setNavigationUrl] = useState("/");
	const [modalSnippet, setModalSnippet] = useState(false);
	const { id } = useParams<{id: string}>();
	const { apiRetryHandler } = useTokenCheck();
	const [trialActive, setTrialActive] = useState(false);
	const [accountStatus, setAccountStatus] = useState(true);
	const [accountData, setAccountData] = useState<Account>();
	const [hideCreateVideo, setHideCreateVideo] = useState(false);

	useEffect(() => {
		document.title = translation.editCollection.pageTitle;
	}, [translation.editCollection.pageTitle]);

	useEffect(() => {
		const go = async () => {
			const { data: accountData } = await apiRetryHandler(async () => await getAccountData());
			if (accountData) {
				setAccountData(accountData);
				setTrialActive(accountData.subscription?.trialActive);

				if (compareImpressions(accountData?.totalImpressionsCurrentCycle, accountData.subscription?.maxImpressionsPerCycle)) {
					setHideCreateVideo(true);
				}
			}

			setAccountStatus(false);
		};

		if (accountStatus) {
			go();
		}
	}, [accountStatus, apiRetryHandler]);

	return (
		<>
			<Sidebar accountData={accountData} saveChanges={saveChanges} setNavigationUrl={setNavigationUrl} setModalStatus={setModalStatus} />
			<Header auth={true} accountData={accountData} saveChanges={saveChanges} trialActive={trialActive} setChangesModal={setModalStatus} setNavigationUrl={setNavigationUrl} />
			<EditCollectionPage accountData={accountData} saveChanges={saveChanges} setSaveChanges={setSaveChanges} navigationUrl={navigationUrl} setNavigationUrl={setNavigationUrl} modalStatus={modalStatus} setModalStatus={setModalStatus} setModalSnippet={setModalSnippet} hideCreateVideo={hideCreateVideo} />
			<SnippetOptionsModal visible={modalSnippet} onCancel={() => setModalSnippet(false)} collectionId={id} />
		</>
	);
};

export default EditCollection;
