import React, { useState, useEffect } from "react";
import { useTranslation } from "../hooks/translations";
import { useNavigate } from "react-router-dom";
import Header from "../header/Header";
import { Row, Col } from "react-bootstrap";
import { PageBody, OneCollection, MainButton, ImageCollections } from "@src/styles/components";
import { HeadingText, HeadingTextH2, ConfirmationBoxWrapper, ConfirmationBox } from "@src/styles/forms";
import Skeleton from "react-loading-skeleton";
import CollectionsList from "@src/components/collections/CollectionsList";
import { useTokenCheck } from "../hooks/useTokenCheck";
import { carouselMockup } from "@src/assets";
import { ShoppableCollection } from "@src/types/collections";
import getCollections from "../utils/getCollections";
import Sidebar from "../sidebar/Sidebar";
import { SnippetOptionsModal } from "../modals/SnippetOptionsModal";
import ErrorMessage from "@src/components/utils/ErrorMessage";
import getAccountData from "../utils/getAccountData";
import { Account } from "@src/types/account";
import { GeneralUsageModal } from "../modals/GeneralUsageModal";
import { compareImpressions } from "@src/utils/compare";

const Collections: React.FC = () => {
	const navigate = useNavigate();
	const translation = useTranslation();
	const [errorResponse, setErrorResponse] = useState(false);
	const [refetch, setRefetch] = useState(true);
	const [collections, setCollections] = useState<ShoppableCollection[]>([]);
	const { apiRetryHandler } = useTokenCheck();
	const [deleteError, setDeleteError] = useState("");
	const [confirmationText, setConfirmationText] = useState("");
	const [modalSnippet, setModalSnippet] = useState(false);
	const [collectionId, setCollectionId] = useState("");
	const [trialActive, setTrialActive] = useState(false);
	const [accountData, setAccountData] = useState<Account>();
	const [generalModalStatus, setGeneralModalStatus] = useState(false);
	const showPlansPage = process.env.SHOW_PLANS_PAGE === "true";

	const showConfirmationText = (value: string) => {
		setConfirmationText(value);
		setTimeout(() => {
			setConfirmationText("");
		}, 2000);
	};

	const showDeleteError = (value: string) => {
		setDeleteError(value);
		setTimeout(() => {
			setDeleteError("");
		}, 2000);
	};

	useEffect(() => {
		document.title = translation.general.collections;
	}, [translation.general.collections]);

	useEffect(() => {
		const go = async () => {
			const { data: collections, error: collectionError } = await apiRetryHandler(async () => await getCollections());

			if (collectionError) {
				setErrorResponse(true);
			} else {
				if (collections) {
					setCollections(collections);
				}
			}

			const { data: accountData } = await apiRetryHandler(async () => await getAccountData());
			if (accountData) {
				setAccountData(accountData);
				setTrialActive(accountData.subscription?.trialActive);

				if (compareImpressions(accountData?.totalImpressionsCurrentCycle, accountData.subscription?.maxImpressionsPerCycle)) {
					setGeneralModalStatus(true);
				}
			}

			setRefetch(false);
		};

		if (refetch) {
			go();
		}
	}, [setErrorResponse, refetch, apiRetryHandler]);

	return (
		<>
			<Sidebar accountData={accountData} />
			<Header auth={true} accountData={accountData} trialActive={trialActive} />
			<ConfirmationBoxWrapper>
				{!!confirmationText && (
					<ConfirmationBox className="text-center" data-testid="deleteConfirmation">
						{confirmationText}
					</ConfirmationBox>
				)}
				{!!deleteError && <ErrorMessage error={deleteError} setError={setDeleteError} displayCloseIcon={true} />}
			</ConfirmationBoxWrapper>
			<PageBody>
				<Row>
					<Col sm="12" md="12" lg="6" xl="6" className="mb-4 text-center text-lg-start ">
						<HeadingText>{translation.general.collections}</HeadingText>
					</Col>
					<Col sm="12" md="12" lg="6" xl="6" className="mb-4 text-center text-lg-end">
						<MainButton
							type="button"
							data-testid="createButton"
							onClick={() => {
								navigate("/create-collection");
							}}
							className="ms-3"
						>
							+&nbsp;{translation.collectionsPage.createCollection}
						</MainButton>
					</Col>
				</Row>

				{refetch && collections?.length < 1 && (
					<div style={{ marginTop: "3rem" }}>
						<Skeleton className="mt-2" count={8} height={50} />
					</div>
				)}

				{collections?.length > 0 && <CollectionsList accountData={accountData} collections={collections} refreshList={() => setRefetch(true)} showConfirmationText={showConfirmationText} showDeleteError={showDeleteError} setModalSnippet={setModalSnippet} setCollectionId={setCollectionId} />}

				{collections?.length === 1 && (
					<OneCollection>
						<ImageCollections className="mb-4" src={carouselMockup} />
						<HeadingTextH2 className="mb-3">{translation.collectionsPage.addMoreCollections}</HeadingTextH2>
						<MainButton
							className="mt-3"
							type="button"
							data-testid="createCollectionButton"
							onClick={() => {
								navigate("/create-collection");
							}}
						>
							{translation.collectionsPage.createCollection}
						</MainButton>
					</OneCollection>
				)}

				{errorResponse && (
					<OneCollection>
						<HeadingTextH2 className="mb-3">{translation.collectionsPage.loadingIssue}</HeadingTextH2>
						<MainButton className="mt-3" type="button" data-testid="refreshButton" onClick={() => location.reload()}>
							{translation.general.refresh}
						</MainButton>
					</OneCollection>
				)}
			</PageBody>
			<SnippetOptionsModal visible={modalSnippet} onCancel={() => setModalSnippet(false)} collectionId={collectionId} />
			<GeneralUsageModal visible={showPlansPage && generalModalStatus} onCancel={() => setGeneralModalStatus(false)} />
		</>
	);
};

export default Collections;
