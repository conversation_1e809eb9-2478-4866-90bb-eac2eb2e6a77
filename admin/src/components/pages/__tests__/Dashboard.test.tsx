import React from "react";
import Dashboard from "../Dashboard";
import { ThemeProvider } from "styled-components";
import getTheme from "@src/config/theme";
import { render, cleanup, act } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import { RecoilRoot } from "recoil";

afterEach(cleanup);

jest.mock("react-router-dom", () => ({
	useNavigate: () => ({
		push: jest.fn()
	})
}));

it("renders without crashing", async () => {
	await act(async () => {
		render(
			<ThemeProvider theme={getTheme(null)}>
				<RecoilRoot>
					<Dashboard />
				</RecoilRoot>
			</ThemeProvider>
		);
	});
});
