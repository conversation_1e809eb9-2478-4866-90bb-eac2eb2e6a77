import React from "react";
import AddCompany from "../AddCompany";
import { ThemeProvider } from "styled-components";
import getTheme from "@src/config/theme";
import { render, cleanup } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import { RecoilRoot } from "recoil";

afterEach(cleanup);

jest.mock("react-router-dom", () => ({
	useNavigate: () => ({
		push: jest.fn()
	})
}));

it("renders without crashing", () => {
	render(
		<ThemeProvider theme={getTheme(null)}>
			<RecoilRoot>
				<AddCompany />
			</RecoilRoot>
		</ThemeProvider>
	);
});
