import React from "react";
import Profile from "../Profile";
import { ThemeProvider } from "styled-components";
import getTheme from "@src/config/theme";
import { render, cleanup, act } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import { RecoilRoot } from "recoil";

afterEach(cleanup);

jest.mock("deep-equal", () => ({
	default: jest.fn().mockImplementation(() => true)
}));

jest.mock("react-router-dom", () => ({
	useParams: () => ({
		id: "123456789"
	}),
	useNavigate: () => ({
		push: jest.fn()
	})
}));

it("renders without crashing", async () => {
	await act(async () => {
		render(
			<ThemeProvider theme={getTheme(null)}>
				<RecoilRoot>
					<Profile />
				</RecoilRoot>
			</ThemeProvider>
		);
	});
});
