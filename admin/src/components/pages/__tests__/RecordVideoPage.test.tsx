/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import RecordVideoPage from "../RecordVideo";
import { ThemeProvider } from "styled-components";
import getTheme from "@src/config/theme";
import { render, cleanup, act } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import { RecoilRoot } from "recoil";
import getAccountData from "../../utils/getAccountData";

jest.mock("../../utils/getAccountData", () => ({
	__esModule: true,
	default: jest.fn()
}));

const mockedGetAccountData = getAccountData as jest.MockedFunction<typeof getAccountData>;

jest.mock("../../hooks/useTokenCheck", () => ({
	useTokenCheck: () => ({
		apiRetryHandler: jest.fn((callback) => callback())
	})
}));

const mockNavigate = jest.fn();

jest.mock("react-router-dom", () => ({
	useParams: () => ({
		id: "*********"
	}),
	useNavigate: () => mockNavigate
}));

beforeEach(() => {
	mockNavigate.mockClear();
});
afterEach(cleanup);

it("renders RecordVideoPage without crashing", async () => {
	mockedGetAccountData.mockResolvedValue({
		data: {
			subscription: {
				allowRecordVideo: true
			} as any
		} as any
	});

	await act(async () => {
		render(
			<ThemeProvider theme={getTheme(null)}>
				<RecoilRoot>
					<RecordVideoPage />
				</RecoilRoot>
			</ThemeProvider>
		);
	});
});

it("redirects to index if allowRecordVideos is false", async () => {
	mockedGetAccountData.mockResolvedValue({
		data: {
			subscription: {
				allowRecordVideo: false
			} as any
		} as any
	});

	await act(async () => {
		render(
			<ThemeProvider theme={getTheme(null)}>
				<RecoilRoot>
					<RecordVideoPage />
				</RecoilRoot>
			</ThemeProvider>
		);
	});

	expect(mockNavigate).toHaveBeenCalledWith("/");
});
