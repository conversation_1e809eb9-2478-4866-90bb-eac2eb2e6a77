/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import RecordVideoPage from "../RecordVideo";
import { ThemeProvider } from "styled-components";
import getTheme from "@src/config/theme";
import { render, cleanup, act } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import { RecoilRoot } from "recoil";
import getAccountData from "../../utils/getAccountData";

jest.mock("../../utils/getAccountData", () => ({
	__esModule: true,
	default: jest.fn()
}));

const mockedGetAccountData = getAccountData as jest.MockedFunction<typeof getAccountData>;

jest.mock("../../hooks/useTokenCheck", () => ({
	useTokenCheck: () => ({
		apiRetryHandler: jest.fn((callback) => callback())
	})
}));

class MockMediaStreamTrack {
	kind: string;
	label: string;

	constructor(kind: string, label = "") {
		this.kind = kind;
		this.label = label;
	}

	stop = jest.fn();
	addEventListener = jest.fn();
}

class MockMediaStream {
	tracks: MockMediaStreamTrack[] = [];

	constructor(tracks: MockMediaStreamTrack[] = []) {
		this.tracks = tracks;
	}

	getTracks = jest.fn(() => this.tracks);
	getVideoTracks = jest.fn(() => this.tracks.filter(t => t.kind === "video"));
}

const mockNavigate = jest.fn();

jest.mock("react-router-dom", () => ({
	useParams: () => ({
		id: "*********"
	}),
	useNavigate: () => mockNavigate
}));

beforeEach(() => {
	mockNavigate.mockClear();

	const mockGetUserMedia = jest.fn();
	const mockGetDisplayMedia = jest.fn();
	const mockEnumerateDevices = jest.fn();

	const mockVideoStream = new MockMediaStream([new MockMediaStreamTrack("video", "Camera 1")]) as any;
	const mockAudioStream = new MockMediaStream([new MockMediaStreamTrack("audio", "Microphone 1")]) as any;

	// Setup basic resolved values for getUserMedia
	mockGetUserMedia
		.mockResolvedValueOnce(mockVideoStream)
		.mockResolvedValueOnce(mockAudioStream);

	mockEnumerateDevices.mockResolvedValue([]);

	Object.defineProperty(global.navigator, "mediaDevices", {
		value: {
			getUserMedia: mockGetUserMedia,
			getDisplayMedia: mockGetDisplayMedia,
			enumerateDevices: mockEnumerateDevices
		},
		writable: true
	});
});
afterEach(cleanup);

it("renders RecordVideoPage without crashing", async () => {
	mockedGetAccountData.mockResolvedValue({
		data: {
			subscription: {
				allowRecordVideo: true
			} as any
		} as any
	});

	await act(async () => {
		render(
			<ThemeProvider theme={getTheme(null)}>
				<RecoilRoot>
					<RecordVideoPage />
				</RecoilRoot>
			</ThemeProvider>
		);
	});
});

it("redirects to index if allowRecordVideos is false", async () => {
	mockedGetAccountData.mockResolvedValue({
		data: {
			subscription: {
				allowRecordVideo: false
			} as any
		} as any
	});

	await act(async () => {
		render(
			<ThemeProvider theme={getTheme(null)}>
				<RecoilRoot>
					<RecordVideoPage />
				</RecoilRoot>
			</ThemeProvider>
		);
	});

	expect(mockNavigate).toHaveBeenCalledWith("/");
});
