import React from "react";
import SignIn from "../SignIn";
import { ThemeProvider } from "styled-components";
import getTheme from "@src/config/theme";
import { render, cleanup } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import { RecoilRoot } from "recoil";

afterEach(cleanup);

jest.mock("react-router-dom", () => ({
	useNavigate: () => ({
		push: jest.fn()
	})
}));

it("renders without crashing", () => {
	render(
		<ThemeProvider theme={getTheme(null)}>
			<RecoilRoot>
				<SignIn />
			</RecoilRoot>
		</ThemeProvider>
	);
});
