import React, { useState, useEffect } from "react";
import { useTranslation } from "../hooks/translations";
import { useNavigate } from "react-router-dom";
import Header from "../header/Header";
import { PageBody, PageRow, EmptySection, MainButton } from "@src/styles/components";
import { HeadingText, HeadingTextH2, ConfirmationBoxWrapper, ConfirmationBox } from "@src/styles/forms";
import Skeleton from "react-loading-skeleton";
import ShoppableVideos from "@src/components/dashboard/ShoppableVideos";
import { useTokenCheck } from "../hooks/useTokenCheck";
import getVideos from "../utils/getVideos";
import { shoppableVideo } from "@src/types/videos";
import { EventNameEnum } from "@src/types/events";
import { registerEvent } from "@src/components/events/service";
import { ShoppableCollection } from "@src/types/collections";
import getCollection from "../utils/getCollection";
import updateCollection from "../utils/updateCollection";
import Sidebar from "../sidebar/Sidebar";
import getAccountData from "../utils/getAccountData";
import ErrorMessage from "@src/components/utils/ErrorMessage";
import { Account } from "@src/types/account";
import { VideoUsageModal } from "../modals/VideoUsageModal";
import { GeneralUsageModal } from "../modals/GeneralUsageModal";
import { compareImpressions } from "@src/utils/compare";

const Videos: React.FC = () => {
	const navigate = useNavigate();
	const translation = useTranslation();
	const [errorResponse, setErrorResponse] = useState(false);
	const [emptyVideoList, setEmptyVideoList] = useState(false);
	const [refetch, setRefetch] = useState(true);
	const [videos, setVideos] = useState<shoppableVideo[]>([]);
	const [collection, setCollection] = useState<ShoppableCollection>();
	const { apiRetryHandler } = useTokenCheck();
	const [deleteError, setDeleteError] = useState("");
	const [confirmationText, setConfirmationText] = useState("");
	const [trialActive, setTrialActive] = useState(false);
	const [accountData, setAccountData] = useState<Account>();
	const [videoModalStatus, setVideoModalStatus] = useState(false);
	const [generalModalStatus, setGeneralModalStatus] = useState(false);
	const [hideCreateVideo, setHideCreateVideo] = useState(false);
	const showPlansPage = process.env.SHOW_PLANS_PAGE === "true";

	const showConfirmationText = (value: string) => {
		setConfirmationText(value);
		setTimeout(() => {
			setConfirmationText("");
		}, 2000);
	};

	const showDeleteError = (value: string) => {
		setDeleteError(value);
		setTimeout(() => {
			setDeleteError("");
		}, 2000);
	};

	const navigateToCreateVideo = () => {
		registerEvent({
			eventName: EventNameEnum.CREATE_VIDEO_PRESS
		});
		navigate("/create-video");
	};

	useEffect(() => {
		document.title = translation.general.videos;
	}, [translation.general.videos]);

	useEffect(() => {
		const go = async () => {
			const { data: collection, error: collectionError } = await apiRetryHandler(async () => await getCollection());

			if (collectionError) {
				setErrorResponse(true);
			} else {
				if (collection) {
					setCollection(collection);
				}
			}

			setVideos([]);
			const { data, error } = await apiRetryHandler(async () => await getVideos());

			if (error) {
				setErrorResponse(true);
			} else {
				if (data?.length) {
					if (collection?.shoppableVideos?.length) {
						data.sort((a: shoppableVideo, b: shoppableVideo) => {
							return collection.shoppableVideos.findIndex((item: string) => item === a._id) - collection.shoppableVideos.findIndex((item: string) => item === b._id);
						});
					}
					setVideos(data);
				} else {
					setEmptyVideoList(true);
				}
			}

			const { data: accountData } = await apiRetryHandler(async () => await getAccountData());
			if (accountData) {
				setAccountData(accountData);
				setTrialActive(accountData.subscription?.trialActive);

				if (compareImpressions(accountData?.totalImpressionsCurrentCycle, accountData.subscription?.maxImpressionsPerCycle)) {
					setGeneralModalStatus(true);
					setHideCreateVideo(true);
				}
			}

			setRefetch(false);
		};

		if (refetch) {
			go();
		}
	}, [setErrorResponse, setEmptyVideoList, setVideos, refetch, apiRetryHandler]);

	const updateList = async (videos: shoppableVideo[]) => {
		if (!collection) return;
		const { error } = await apiRetryHandler(
			async () =>
				await updateCollection(
					collection._id,
					videos.map((video) => video._id)
				)
		);
		if (error) {
			setErrorResponse(true);
		}
	};

	return (
		<>
			<Sidebar accountData={accountData} />
			<Header auth={true} accountData={accountData} trialActive={trialActive} />
			<ConfirmationBoxWrapper>
				{!!confirmationText && (
					<ConfirmationBox className="text-center" data-testid="deleteConfirmation">
						{confirmationText}
					</ConfirmationBox>
				)}
				{!!deleteError && <ErrorMessage error={deleteError} setError={setDeleteError} displayCloseIcon={true} />}
			</ConfirmationBoxWrapper>
			<PageBody>
				<PageRow>
					<HeadingText>{translation.dashboardPage.videoManager}</HeadingText>
					<MainButton
						type="button"
						data-testid="createButton"
						onClick={() => {
							if (hideCreateVideo) {
								setVideoModalStatus(true);
							} else {
								navigateToCreateVideo();
							}
						}}
					>
						+&nbsp;{translation.general.create}
					</MainButton>
				</PageRow>

				{refetch && videos?.length < 1 && (
					<div style={{ marginTop: "3rem" }}>
						<Skeleton className="mt-2" count={8} height={50} />
					</div>
				)}

				{videos?.length > 0 && <ShoppableVideos videos={videos} setVideos={setVideos} updateList={updateList} refreshList={() => setRefetch(true)} showConfirmationText={showConfirmationText} showDeleteError={showDeleteError} accountData={accountData} />}

				{emptyVideoList && (
					<EmptySection>
						<HeadingTextH2 className="mb-3">{translation.dashboardPage.noShoppableVideos}</HeadingTextH2>
						<MainButton
							className="mt-3"
							type="button"
							data-testid="createVideosButton"
							onClick={() => {
								if (hideCreateVideo) {
									setVideoModalStatus(true);
								} else {
									navigateToCreateVideo();
								}
							}}
						>
							{translation.dashboardPage.createOneNow}
						</MainButton>
					</EmptySection>
				)}

				{errorResponse && (
					<EmptySection>
						<HeadingTextH2 className="mb-3">{translation.dashboardPage.loadingIssue}</HeadingTextH2>
						<MainButton className="mt-3" type="button" data-testid="refreshVideosButton" onClick={() => location.reload()}>
							{translation.general.refresh}
						</MainButton>
					</EmptySection>
				)}
			</PageBody>
			<VideoUsageModal visible={videoModalStatus} onCancel={() => setVideoModalStatus(false)} />
			<GeneralUsageModal visible={showPlansPage && generalModalStatus} onCancel={() => setGeneralModalStatus(false)} />
		</>
	);
};

export default Videos;
