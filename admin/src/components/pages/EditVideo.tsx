import React, { useEffect, useState } from "react";
import { useTranslation } from "../hooks/translations";
import Header from "../header/Header";
import EditVideoDetails from "@src/components/videos/EditVideoDetails";
import Sidebar from "../sidebar/Sidebar";
import getAccountData from "../utils/getAccountData";
import { useTokenCheck } from "../hooks/useTokenCheck";
import getUser from "../utils/getUser";
import { Account } from "@src/types/account";
import { compareImpressions } from "@src/utils/compare";
import { companyDetails } from "@src/types/videos";

const EditVideo: React.FC = () => {
	const translation = useTranslation();
	const [modalStatus, setModalStatus] = useState(false);
	const [saveChanges, setSaveChanges] = useState(false);
	const [navigationUrl, setNavigationUrl] = useState("/");
	const { apiRetry<PERSON>and<PERSON> } = useTokenCheck();
	const [trialActive, setTrialActive] = useState(false);
	const [accountStatus, setAccountStatus] = useState(true);
	const [accountData, setAccountData] = useState<Account>();
	const [hideCreateVideo, setHideCreateVideo] = useState(false);
	const [userData, setUserData] = useState<companyDetails>();

	useEffect(() => {
		document.title = translation.editVideoPage.pageTitle;
	}, [translation.editVideoPage.pageTitle]);

	useEffect(() => {
		const go = async () => {
			const { data: accountData } = await apiRetryHandler(async () => await getAccountData());
			if (accountData) {
				setAccountData(accountData);
				setTrialActive(accountData.subscription?.trialActive);

				if (compareImpressions(accountData?.totalImpressionsCurrentCycle, accountData.subscription?.maxImpressionsPerCycle)) {
					setHideCreateVideo(true);
				}
			}

			const { data: userData, error } = await apiRetryHandler(async () => await getUser());
			if (!error) {
				setUserData(userData);
			}

			setAccountStatus(false);
		};

		if (accountStatus) {
			go();
		}
	}, [accountStatus, apiRetryHandler]);

	return (
		<>
			<Sidebar accountData={accountData} saveChanges={saveChanges} setNavigationUrl={setNavigationUrl} setModalStatus={setModalStatus} />
			<Header auth={true} accountData={accountData} saveChanges={saveChanges} trialActive={trialActive} setChangesModal={setModalStatus} setNavigationUrl={setNavigationUrl} />
			<EditVideoDetails
				userData={userData}
				subscription={accountData?.subscription}
				saveChanges={saveChanges}
				setSaveChanges={setSaveChanges}
				navigationUrl={navigationUrl}
				setNavigationUrl={setNavigationUrl}
				modalStatus={modalStatus}
				setModalStatus={setModalStatus}
				hideCreateVideo={hideCreateVideo}
			/>
		</>
	);
};

export default EditVideo;
