import React, { useState, useEffect } from "react";
import { useTranslation } from "../hooks/translations";
import { useNavigate } from "react-router-dom";
import { Row, Col } from "react-bootstrap";
import Header from "../header/Header";
import { PageBody, PageRow, MainButton, VideoManager, LearnMoreDiv, ArrowIcon, HomePageTitle, StarIcon, WhiteTextLink, BlueTextLink, DivWithImage } from "@src/styles/components";
import { HeadingText } from "@src/styles/forms";
import { useTokenCheck } from "../hooks/useTokenCheck";
import getCompanies from "../utils/getCompanies";
import { useMediaQuery } from "react-responsive";
import Sidebar from "../sidebar/Sidebar";
import { registerEvent } from "@src/components/events/service";
import { EventNameEnum } from "@src/types/events";
import { ExtraInformationModal } from "../modals/ExtraInformationModal";
import { VideoUsageModal } from "../modals/VideoUsageModal";
import { GeneralUsageModal } from "../modals/GeneralUsageModal";
import { ProductTypeEnum } from "@src/types/product";
import getAccountData from "../utils/getAccountData";
import PerformanceMetrics from "@src/components/performance/PerformanceMetrics";
import { Account } from "@src/types/account";
import { compareImpressions } from "@src/utils/compare";
import ImpressionsProgressBar from "@src/components/dashboard/ImpressionsProgressBar";

const Dashboard: React.FC = () => {
	const navigate = useNavigate();
	const translation = useTranslation();
	const [firstName, setFirstName] = useState("");
	const [refetch, setRefetch] = useState(true);
	const { apiRetryHandler } = useTokenCheck();
	const [modalStatus, setModalStatus] = useState(false);
	const [videoModalStatus, setVideoModalStatus] = useState(false);
	const [generalModalStatus, setGeneralModalStatus] = useState(false);
	const [hideCreateVideo, setHideCreateVideo] = useState(false);
	const postSignup = localStorage.getItem("postSignup");
	const isScreenSmall = useMediaQuery({ query: "(max-width: 930px)" });
	const [trialAvailable, setTrialAvailable] = useState(false);
	const [trialActive, setTrialActive] = useState(false);
	const [accountData, setAccountData] = useState<Account>();
	const showPlansPage = process.env.SHOW_PLANS_PAGE === "true";

	useEffect(() => {
		document.title = translation.general.dashboard;
	}, [translation.general.dashboard, setModalStatus]);

	useEffect(() => {
		const go = async () => {
			const { data } = await apiRetryHandler(async () => await getCompanies());

			if (data) {
				setFirstName(data?.firstName);

				if (!data?.postSignupCompleted && postSignup) {
					setModalStatus(true);
					localStorage.removeItem("postSignup");
				}
			}

			const { data: accountData } = await apiRetryHandler(async () => await getAccountData());
			if (accountData) {
				setAccountData(accountData);
				setTrialAvailable(accountData.subscription?.trialAvailable);
				setTrialActive(accountData.subscription?.trialActive);

				if (compareImpressions(accountData?.totalImpressionsCurrentCycle, accountData.subscription?.maxImpressionsPerCycle)) {
					setGeneralModalStatus(true);
					setHideCreateVideo(true);
				}
			}

			setRefetch(false);
		};

		if (refetch) {
			go();
		}
	}, [refetch, postSignup, apiRetryHandler]);

	return (
		<>
			{!refetch && (
				<>
					<Sidebar accountData={accountData} />
					<Header auth={true} accountData={accountData} trialActive={trialActive} />
					<PageBody>
						<PageRow>
							<HeadingText>
								{translation.companyPage.welcome} {firstName}!
							</HeadingText>
						</PageRow>
						{showPlansPage &&
							(accountData?.subscription.type === ProductTypeEnum.BASIC && (
								<LearnMoreDiv>
									<Row pl="0" pr="0">
										<Col sm="12" md="6">
											<HomePageTitle whiteColor={true} className="mt-3 mb-3">
												{translation.general.accessMore} <b>{translation.general.features}</b> {translation.general.and} <b>{translation.general.metrics}</b> {translation.general.with} <b>{translation.general.proPlan}</b>
											</HomePageTitle>
										</Col>
										<Col sm="12" md="6" style={{ textAlign: isScreenSmall ? "left" : "right" }}>
											<WhiteTextLink
												onClick={() => {
													navigate("/plans-pricing");
												}}
												style={{ marginRight: "10%" }}
											>
												{translation.general.learnMore}
											</WhiteTextLink>
											<MainButton
												className="mt-2 mb-2"
												type="button"
												onClick={() => {
													navigate("/plans-pricing");
												}}
											>
												<StarIcon /> &nbsp;
												{trialAvailable ? translation.modals.apProSelectPro : translation.general.unlockProPlan}
											</MainButton>
										</Col>
									</Row>
								</LearnMoreDiv>
							))
						}
						<Row pl="0" pr="0">
							<Col sm="12" md="12" lg="12" xl="6">
								<DivWithImage>
									<HomePageTitle whiteColor={true} className="mt-3 mb-3">
										{translation.dashboardPage.interactiveVideo}
									</HomePageTitle>
									<MainButton
										className="mt-2 mb-2"
										type="button"
										data-testid="newVideo"
										reverse={true}
										onClick={() => {
											if (hideCreateVideo) {
												setVideoModalStatus(true);
											} else {
												registerEvent({
													eventName: EventNameEnum.CREATE_VIDEO_PRESS
												});
												navigate("/create-video");
											}
										}}
									>
										{translation.dashboardPage.newVideo}
									</MainButton>
								</DivWithImage>
							</Col>
							<Col sm="12" md="6" lg="6" xl="3">
								<VideoManager>
									<HomePageTitle className="mt-3 mb-3">{translation.dashboardPage.groupVideos}</HomePageTitle>
									<BlueTextLink className="mt-2 mb-2" type="button" data-testid="collectionsList" onClick={() => navigate("/collections")}>
										{translation.general.collections} <ArrowIcon />
									</BlueTextLink>
								</VideoManager>
							</Col>
							<Col sm="12" md="6" lg="6" xl="3">
								<ImpressionsProgressBar accountData={accountData} />
							</Col>
						</Row>
						<PerformanceMetrics accountData={accountData} trialAvailable={trialAvailable} hideCreateVideo={hideCreateVideo} setVideoModalStatus={setVideoModalStatus} />
					</PageBody>
					<ExtraInformationModal visible={modalStatus} onCancel={() => setModalStatus(false)} />
					<VideoUsageModal visible={videoModalStatus} onCancel={() => setVideoModalStatus(false)} />
					<GeneralUsageModal visible={generalModalStatus} onCancel={() => setGeneralModalStatus(false)} />
				</>
			)}
		</>
	);
};

export default Dashboard;
