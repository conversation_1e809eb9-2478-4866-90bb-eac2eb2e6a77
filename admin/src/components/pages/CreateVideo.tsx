import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "../hooks/translations";
import Header from "../header/Header";
import AddNewVideo from "@src/components/videos/AddNewVideo";
import Sidebar from "../sidebar/Sidebar";
import getAccountData from "../utils/getAccountData";
import getUser from "../utils/getUser";
import { useTokenCheck } from "../hooks/useTokenCheck";
import { Account } from "@src/types/account";
import { compareImpressions } from "@src/utils/compare";
import { companyDetails } from "@src/types/videos";

const CreateVideo: React.FC = () => {
	const translation = useTranslation();
	const navigate = useNavigate();
	const [modalStatus, setModalStatus] = useState(false);
	const [saveChanges, setSaveChanges] = useState(false);
	const [navigationUrl, setNavigationUrl] = useState("/");
	const { apiRetryHandler } = useTokenCheck();
	const [trialActive, setTrialActive] = useState(false);
	const [accountStatus, setAccountStatus] = useState(true);
	const [accountData, setAccountData] = useState<Account>();
	const [userData, setUserData] = useState<companyDetails>();

	useEffect(() => {
		document.title = translation.createVideoPage.pageTitle;
	}, [translation.createVideoPage.pageTitle]);

	useEffect(() => {
		const go = async () => {
			const { data: accountData } = await apiRetryHandler(async () => await getAccountData());
			if (accountData) {
				setAccountData(accountData);
				setTrialActive(accountData.subscription?.trialActive);

				if (compareImpressions(accountData?.totalImpressionsCurrentCycle, accountData.subscription?.maxImpressionsPerCycle)) {
					navigate("/");
				}
			}

			const { data: userData, error } = await apiRetryHandler(async () => await getUser());
			if (!error) {
				setUserData(userData);
			}

			setAccountStatus(false);
		};

		if (accountStatus) {
			go();
		}
	}, [accountStatus, apiRetryHandler, navigate]);

	return (
		<>
			<Sidebar accountData={accountData} saveChanges={saveChanges} setNavigationUrl={setNavigationUrl} setModalStatus={setModalStatus} />
			<Header auth={true} accountData={accountData} saveChanges={saveChanges} trialActive={trialActive} setChangesModal={setModalStatus} setNavigationUrl={setNavigationUrl} />
			<AddNewVideo userData={userData} subscription={accountData?.subscription} saveChanges={saveChanges} setSaveChanges={setSaveChanges} navigationUrl={navigationUrl} setNavigationUrl={setNavigationUrl} modalStatus={modalStatus} setModalStatus={setModalStatus} />
		</>
	);
};

export default CreateVideo;
