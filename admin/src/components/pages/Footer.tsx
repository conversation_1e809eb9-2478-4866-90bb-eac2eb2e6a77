import React from "react";
import { useTranslation, FormatString } from "../hooks/translations";
import { FooterText } from "@src/styles/forms";

const Footer: React.FC<{auth?: boolean}> = ({ auth = false }) => {
	const translation = useTranslation();

	return (
		<FooterText data-testid="copyright" auth={auth}>
			{FormatString(translation.general.copyright, new Date().getFullYear().toString())}

			<span className="link">
				<a href={translation.general.tosLink} data-testid="copyrightTOS" target="_blank" rel="noreferrer">
					{translation.general.tosText}
				</a>
			</span>
			<span className="link">
				<a href={translation.general.privacyPolicyLink} data-testid="copyrightPrivacyText" target="_blank" rel="noreferrer">
					{translation.general.privacyPolicyText}
				</a>
			</span>
		</FooterText>
	);
};

export default Footer;
