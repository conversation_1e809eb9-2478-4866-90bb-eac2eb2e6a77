import React, { useEffect, useState } from "react";
import { useTranslation } from "../hooks/translations";
import Header from "../header/Header";
import NewCollection from "@src/components/collections/NewCollection";
import Sidebar from "../sidebar/Sidebar";
import getAccountData from "../utils/getAccountData";
import { useTokenCheck } from "../hooks/useTokenCheck";
import { Account } from "@src/types/account";
import { compareImpressions } from "@src/utils/compare";

const CreateCollection: React.FC = () => {
	const translation = useTranslation();
	const [modalStatus, setModalStatus] = useState(false);
	const [saveChanges, setSaveChanges] = useState(false);
	const [navigationUrl, setNavigationUrl] = useState("/");
	const { apiRetryHandler } = useTokenCheck();
	const [trialActive, setTrialActive] = useState(false);
	const [accountStatus, setAccountStatus] = useState(true);
	const [accountData, setAccountData] = useState<Account>();
	const [hideCreateVideo, setHideCreateVideo] = useState(false);

	useEffect(() => {
		document.title = translation.createCollectionPage.pageTitle;
	}, [translation.createCollectionPage.pageTitle]);

	useEffect(() => {
		const go = async () => {
			const { data: accountData } = await apiRetryHandler(async () => await getAccountData());
			if (accountData) {
				setAccountData(accountData);
				setTrialActive(accountData.subscription?.trialActive);

				if (compareImpressions(accountData?.totalImpressionsCurrentCycle, accountData.subscription?.maxImpressionsPerCycle)) {
					setHideCreateVideo(true);
				}
			}

			setAccountStatus(false);
		};

		if (accountStatus) {
			go();
		}
	}, [accountStatus, apiRetryHandler]);

	return (
		<>
			<Sidebar accountData={accountData} saveChanges={saveChanges} setNavigationUrl={setNavigationUrl} setModalStatus={setModalStatus} />
			<Header auth={true} accountData={accountData} saveChanges={saveChanges} trialActive={trialActive} setChangesModal={setModalStatus} setNavigationUrl={setNavigationUrl} />
			<NewCollection accountData={accountData} saveChanges={saveChanges} setSaveChanges={setSaveChanges} navigationUrl={navigationUrl} modalStatus={modalStatus} setModalStatus={setModalStatus} hideCreateVideo={hideCreateVideo} />
		</>
	);
};

export default CreateCollection;
