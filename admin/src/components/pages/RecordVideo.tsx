import React,
{
	useEffect,
	useState
} from "react";
import { useTranslation } from "../hooks/translations";
import Header from "../header/Header";
import Sidebar from "../sidebar/Sidebar";
import getAccountData from "../utils/getAccountData";
import { useTokenCheck } from "../hooks/useTokenCheck";
import { Account } from "@src/types/account";
import { GeneralUsageModal } from "../modals/GeneralUsageModal";
import { compareImpressions } from "@src/utils/compare";
import { useNavigate } from "react-router-dom";
import { HeadingText } from "@src/styles/forms";
import {
	MainButton,
	PageBody,
	PageRow
} from "@src/styles/components";
import RecordVideoRecord from "../videos/RecordVideoRecord";
import RecordVideoEdit from "../videos/RecordVideoEdit";

const RecordVideoPage: React.FC = () => {
	const translation = useTranslation();
	const { api<PERSON>etry<PERSON>and<PERSON> } = useTokenCheck();
	const [trialActive, setTrialActive] = useState(false);
	const [accountStatus, setAccountStatus] = useState(true);
	const [accountData, setAccountData] = useState<Account>();
	const [generalModalStatus, setGeneralModalStatus] = useState(false);
	const navigate = useNavigate();
	const [isOnEditPage, setIsOnEditPage] = useState(false);

	useEffect(() => {
		document.title = translation.recordVideoPage.pageTitle;
	}, [translation.recordVideoPage.pageTitle]);

	useEffect(() => {
		const go = async () => {
			const { data: accountData } = await apiRetryHandler(async () => await getAccountData());
			if (accountData) {
				setAccountData(accountData);
				setTrialActive(accountData.subscription?.trialActive);
			}

			if (compareImpressions(accountData?.totalImpressionsCurrentCycle, accountData.subscription?.maxImpressionsPerCycle)) {
				setGeneralModalStatus(true);
			}

			if (!accountData?.subscription?.allowRecordVideo) {
				navigate("/");
			}

			setAccountStatus(false);
		};

		if (accountStatus) {
			go();
		}
	}, [accountStatus, apiRetryHandler, navigate]);

	return (
		<>
			<Sidebar accountData={accountData} />
			<Header auth={true} accountData={accountData} trialActive={trialActive} />

			<PageBody>
				<PageRow>
					<HeadingText data-testid="recordVideoPage">{translation.recordVideoPage.pageTitle}</HeadingText>
					<MainButton onClick={() => setIsOnEditPage(!isOnEditPage)}>Switch to {isOnEditPage ? "Record" : "Edit"} Page</MainButton>
				</PageRow>

				{!isOnEditPage
					? <RecordVideoRecord setIsOnEditPage={setIsOnEditPage}></RecordVideoRecord>
					: <RecordVideoEdit setIsOnEditPage={setIsOnEditPage}></RecordVideoEdit>
				}
			</PageBody>

			<GeneralUsageModal visible={generalModalStatus} onCancel={() => setGeneralModalStatus(false)} />
		</>
	);
};

export default RecordVideoPage;
