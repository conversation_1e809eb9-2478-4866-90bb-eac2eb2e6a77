import React, { useEffect, useState } from "react";
import { useTranslation } from "../hooks/translations";
import Header from "../header/Header";
import PlansPricing from "@src/components/settings/PlansPricing";
import Sidebar from "../sidebar/Sidebar";
import { Account } from "@src/types/account";
import getAccountData from "../utils/getAccountData";
import { useTokenCheck } from "../hooks/useTokenCheck";
import { Product } from "@src/types/product";
import getStripeProducts from "../utils/getStripeProducts";

const Plans: React.FC = () => {
	const translation = useTranslation();
	const { apiRetryHandler } = useTokenCheck();
	const [trialAvailable, setTrialAvailable] = useState(false);
	const [trialActive, setTrialActive] = useState(false);
	const [accountData, setAccountData] = useState<Account>();
	const [accountStatus, setAccountStatus] = useState(true);
	const [pendingChangeDate, setPendingChangeDate] = useState();
	const [clockTime, setClockTime] = useState();
	const [priceId, setPriceId] = useState("");
	const [productsList, setProductsList] = useState<Product[] | null>(null);

	useEffect(() => {
		document.title = translation.plansPage.pageTitle;
	}, [translation.plansPage.pageTitle]);

	useEffect(() => {
		const go = async () => {
			const { data: accountData } = await apiRetryHandler(async () => await getAccountData());
			if (accountData) {
				setAccountData(accountData);
				setTrialAvailable(accountData.subscription?.trialAvailable);
				setTrialActive(accountData.subscription?.trialActive);
				setPriceId(accountData.subscription?.stripePriceId);
				setPendingChangeDate(accountData.subscription?.pendingChangeDate);
				setClockTime(accountData.subscription?.clockTime);

				const { data } = await apiRetryHandler(async () => await getStripeProducts());
				if (data) {
					setProductsList(data);
				}
			}

			setAccountStatus(false);
		};

		if (accountStatus) {
			go();
		}
	}, [accountStatus, apiRetryHandler]);

	return (
		<>
			<Sidebar accountData={accountData} />
			<Header auth={true} accountData={accountData} trialActive={trialActive} />
			<PlansPricing
				accountData={accountData}
				priceId={priceId}
				productsList={productsList}
				trialAvailable={trialAvailable}
				refetch={accountStatus}
				setRefetch={setAccountStatus}
				pendingChangeDate={pendingChangeDate}
				clockTime={clockTime}
			/>
		</>
	);
};

export default Plans;
