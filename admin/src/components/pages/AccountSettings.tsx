import React, { useEffect, useState } from "react";
import { useTranslation } from "../hooks/translations";
import Header from "../header/Header";
import EditSettings from "@src/components/settings/EditSettings";
import Sidebar from "../sidebar/Sidebar";
import getAccountData from "../utils/getAccountData";
import { useTokenCheck } from "../hooks/useTokenCheck";
import { Account } from "@src/types/account";
import { GeneralUsageModal } from "../modals/GeneralUsageModal";
import { compareImpressions } from "@src/utils/compare";

const AccountSettings: React.FC = () => {
	const translation = useTranslation();
	const [refetch, setRefetch] = useState(true);
	const { apiRetryHandler } = useTokenCheck();
	const [trialActive, setTrialActive] = useState(false);
	const [accountData, setAccountData] = useState<Account>();
	const [clockTime, setClockTime] = useState();
	const [generalModalStatus, setGeneralModalStatus] = useState(false);
	const showPlansPage = process.env.SHOW_PLANS_PAGE === "true";

	useEffect(() => {
		document.title = translation.accountSettingsPage.pageTitle;
	}, [translation.accountSettingsPage.pageTitle]);

	useEffect(() => {
		const go = async () => {
			const { data: accountData } = await apiRetryHandler(async () => await getAccountData());
			if (accountData) {
				setAccountData(accountData);
				setTrialActive(accountData.subscription?.trialActive);
				setClockTime(accountData.subscription?.clockTime);

				if (
					compareImpressions(
						accountData?.totalImpressionsCurrentCycle,
						accountData.subscription?.maxImpressionsPerCycle
					)
				) {
					setGeneralModalStatus(true);
				}
			}
		};

		if (refetch) {
			go();
		}
	}, [refetch, apiRetryHandler]);

	return (
		<>
			<Sidebar accountData={accountData} />
			<Header auth={true} accountData={accountData} refetch={refetch} trialActive={trialActive} />
			<EditSettings accountData={accountData} refetch={refetch} setRefetch={setRefetch} clockTime={clockTime} />
			<GeneralUsageModal visible={showPlansPage && generalModalStatus} onCancel={() => setGeneralModalStatus(false)} />
		</>
	);
};

export default AccountSettings;
