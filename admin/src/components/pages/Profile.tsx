import React, { useEffect, useState } from "react";
import { useTranslation } from "../hooks/translations";
import Header from "../header/Header";
import ProfileSettings from "@src/components/settings/ProfileSettings";
import Sidebar from "../sidebar/Sidebar";
import getAccountData from "../utils/getAccountData";
import { useTokenCheck } from "../hooks/useTokenCheck";
import { Account } from "@src/types/account";
import { GeneralUsageModal } from "../modals/GeneralUsageModal";
import { compareImpressions } from "@src/utils/compare";

const Profile: React.FC = () => {
	const translation = useTranslation();
	const { apiRetryHandler } = useTokenCheck();
	const [trialActive, setTrialActive] = useState(false);
	const [accountStatus, setAccountStatus] = useState(true);
	const [accountData, setAccountData] = useState<Account>();
	const [generalModalStatus, setGeneralModalStatus] = useState(false);
	const showPlansPage = process.env.SHOW_PLANS_PAGE === "true";

	useEffect(() => {
		document.title = translation.profilePage.pageTitle;
	}, [translation.profilePage.pageTitle]);

	useEffect(() => {
		const go = async () => {
			const { data: accountData } = await apiRetryHandler(async () => await getAccountData());
			if (accountData) {
				setAccountData(accountData);
				setTrialActive(accountData.subscription?.trialActive);

				if (
					compareImpressions(
						accountData?.totalImpressionsCurrentCycle,
						accountData.subscription?.maxImpressionsPerCycle
					)
				) {
					setGeneralModalStatus(true);
				}
			}

			setAccountStatus(false);
		};

		if (accountStatus) {
			go();
		}
	}, [accountStatus, apiRetryHandler]);

	return (
		<>
			<Sidebar accountData={accountData} />
			<Header auth={true} accountData={accountData} trialActive={trialActive} />
			<ProfileSettings />
			<GeneralUsageModal visible={showPlansPage && generalModalStatus} onCancel={() => setGeneralModalStatus(false)} />
		</>
	);
};

export default Profile;
