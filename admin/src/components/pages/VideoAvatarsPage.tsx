import React, { useEffect, useState } from "react";
import { useTranslation } from "../hooks/translations";
import Header from "../header/Header";
import Sidebar from "../sidebar/Sidebar";
import VideoAvatarLibrary from "@src/components/videos/VideoAvatarLibrary";
import getAccountData from "../utils/getAccountData";
import { useTokenCheck } from "../hooks/useTokenCheck";
import { Account } from "@src/types/account";
import { GeneralUsageModal } from "../modals/GeneralUsageModal";
import { compareImpressions } from "@src/utils/compare";
import { useNavigate } from "react-router-dom";

const VideoAvatarsPage: React.FC = () => {
	const translation = useTranslation();
	const { apiRetryHandler } = useTokenCheck();
	const [trialActive, setTrialActive] = useState(false);
	const [accountStatus, setAccountStatus] = useState(true);
	const [accountData, setAccountData] = useState<Account>();
	const [generalModalStatus, setGeneralModalStatus] = useState(false);
	const navigate = useNavigate();

	useEffect(() => {
		document.title = translation.videoLibraryPage.pageTitle;
	}, [translation.videoLibraryPage.pageTitle]);

	useEffect(() => {
		const go = async () => {
			const { data: accountData } = await apiRetryHandler(async () => await getAccountData());
			if (accountData) {
				setAccountData(accountData);
				setTrialActive(accountData.subscription?.trialActive);
			}

			if (compareImpressions(accountData?.totalImpressionsCurrentCycle, accountData.subscription?.maxImpressionsPerCycle)) {
				setGeneralModalStatus(true);
			}

			if (!accountData?.subscription?.allowAvatars) {
				navigate("/");
			}

			setAccountStatus(false);
		};

		if (accountStatus) {
			go();
		}
	}, [accountStatus, apiRetryHandler, navigate]);

	return (
		<>
			<Sidebar accountData={accountData} />
			<Header auth={true} accountData={accountData} trialActive={trialActive} />
			<VideoAvatarLibrary />
			<GeneralUsageModal visible={generalModalStatus} onCancel={() => setGeneralModalStatus(false)} />
		</>
	);
};

export default VideoAvatarsPage;
