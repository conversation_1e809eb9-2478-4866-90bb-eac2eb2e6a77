import React, { useState, useEffect } from "react";
import { Row, Col } from "react-bootstrap";
import Skeleton from "react-loading-skeleton";
import { HeadingText, SubheadingText, ConfirmationBoxWrapper } from "@src/styles/forms";
import { useTranslation } from "../hooks/translations";
import ErrorMessage from "@src/components/utils/ErrorMessage";
import { PageBody } from "@src/styles/components";
import { ProductTypeEnum, Product } from "@src/types/product";
import { Account } from "@src/types/account";
import updateSubscription from "../utils/updateSubscription";
import { getErrorString } from "../utils/getErrorString";
import { useTokenCheck } from "../hooks/useTokenCheck";
import { ChangePlanModal } from "../modals/ChangePlanModal";
import { PlansPricingBasic } from "./PlansPricingBasic";
import { PlansPricingPro } from "./PlansPricingPro";
import { PlansPricingEnterprise } from "./PlansPricingEnterprise";
import usePlansPricing from "../hooks/usePlansPricing";

interface Props {
	accountData: Account | undefined;
	priceId: string;
	productsList: Product[] | null;
	trialAvailable: boolean;
	refetch: boolean;
	setRefetch: (value: boolean) => void;
	pendingChangeDate?: number | null;
	clockTime?: number | undefined;
}

const PlansPricing: React.FC<Props> = ({
	accountData,
	priceId,
	productsList,
	trialAvailable,
	refetch,
	setRefetch,
	pendingChangeDate,
	clockTime
}) => {
	const translation = useTranslation();
	const { apiRetryHandler } = useTokenCheck();
	const [settingsError, setSettingsError] = useState("");
	const [disableTrialButton, setDisableTrialButton] = useState(false);
	const [basicPlanID, setBasicPlanID] = useState("");
	const [proPlanID, setProPlanID] = useState("");
	const [enterprisePlanID, setEnterprisePlanID] = useState("");
	const [nextPriceId, setNextPriceId] = useState("");
	const [basicPrice, setBasicPrice] = useState(translation.general.free);
	const [proPrice, setProPrice] = useState("");
	const [enterprisePrice, setEnterprisePrice] = useState("");
	const [displayChangePlan, setDisplayChangePlan] = useState(false);
	const [backToFreePlan, setBackToFreePlan] = useState(false);
	const [upgradeToPro, setUpgradeToPro] = useState(false);
	const [basicProduct, setBasicProduct] = useState<Product | null>(null);
	const [proProduct, setProProduct] = useState<Product | null>(null);
	const [enterpriseProduct, setEnterpriseProduct] = useState<Product | null>(null);
	const { formatPrice, sortPrices } = usePlansPricing();

	const changePlan = async (
		priceId: string,
		trialAvailable: boolean,
		productType: ProductTypeEnum,
		subscriptionId?: string
	) => {
		setDisableTrialButton(true);

		let newProduct;
		if (productType === ProductTypeEnum.PRO) {
			newProduct = proProduct?.prices.find((price) => price.id === priceId);
		} else {
			newProduct = basicProduct?.prices.find((price) => price.id === priceId);
		}

		setNextPriceId(priceId);

		if (newProduct && newProduct.unitAmount === 0) {
			setBackToFreePlan(true);
		} else {
			setBackToFreePlan(false);
		}

		if (trialAvailable && productType === ProductTypeEnum.PRO) {
			setUpgradeToPro(true);
			const { error: subscriptionError } = await apiRetryHandler(
				async () => await updateSubscription(priceId, subscriptionId)
			);
			if (subscriptionError) {
				setError(subscriptionError);
				return;
			}
		}

		setDisableTrialButton(false);
		setDisplayChangePlan(true);
	};

	//eslint-disable-next-line @typescript-eslint/no-explicit-any
	const setError = (error: any) => {
		const errorText = getErrorString(translation, error?.response?.data?.error);
		setSettingsError(errorText);
	};

	useEffect(() => {
		if (!refetch && productsList) {
			const basicProductTemp = productsList?.find((product) => product.type === ProductTypeEnum.BASIC);
			if (basicProductTemp) {
				setBasicProduct(basicProductTemp);

				const currentBasicProduct = basicProductTemp?.prices.find((price) => price.id === priceId);
				if (currentBasicProduct?.id && currentBasicProduct?.unitAmount) {
					setBasicPrice(formatPrice(currentBasicProduct));
					setBasicPlanID(currentBasicProduct?.id);
				} else {
					const tempList = sortPrices(basicProductTemp.prices);
					if (tempList[0].id) {
						setBasicPlanID(tempList[0].id);
						setBasicPrice(formatPrice(tempList[0]));
					}
				}
			}

			const proProductTemp = productsList?.find((product) => product.type === ProductTypeEnum.PRO);
			if (proProductTemp) {
				setProProduct(proProductTemp);
				const currentProProduct = proProductTemp?.prices.find((price) => price.id === priceId);
				if (currentProProduct?.id && currentProProduct?.unitAmount) {
					setProPrice(formatPrice(currentProProduct));
					setProPlanID(currentProProduct.id);
				} else {
					const proTempList = sortPrices(proProductTemp.prices);
					if (proTempList[0].id) {
						setProPlanID(proTempList[0].id);
						setProPrice(formatPrice(proTempList[0]));
					}
				}
			}

			const enterpriseProductTemp = productsList?.find((product) => product.type === ProductTypeEnum.ENTERPRISE);
			if (enterpriseProductTemp) {
				setEnterpriseProduct(enterpriseProductTemp);
				const currentEnterpriseProduct = enterpriseProductTemp?.prices.find((price) => price.id === priceId);
				if (currentEnterpriseProduct) {
					if (currentEnterpriseProduct && currentEnterpriseProduct?.id && currentEnterpriseProduct?.unitAmount) {
						setEnterprisePrice(formatPrice(currentEnterpriseProduct));
						setEnterprisePlanID(currentEnterpriseProduct?.id);
					} else {
						const tempList = sortPrices(enterpriseProductTemp.prices);
						if (tempList[0].id) {
							setEnterprisePlanID(tempList[0].id);
							setEnterprisePrice(formatPrice(tempList[0]));
						}
					}
				}
			}
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [refetch, productsList]);

	return (
		<>
			<ConfirmationBoxWrapper>
				{!!settingsError && <ErrorMessage error={settingsError} setError={setSettingsError} displayCloseIcon={true} />}
			</ConfirmationBoxWrapper>
			<PageBody>
				<Row className="mb-4">
					<HeadingText>{translation.plansPage.pageTitle}</HeadingText>
					<SubheadingText className="mt-3">{translation.plansPage.pageSubtitle}</SubheadingText>
				</Row>
				<Row pl="0" pr="0">
					{accountData?.subscription.type !== ProductTypeEnum.ENTERPRISE && (
						<Col sm="12" md="6" lg="5" xl="4" className={refetch ? "mb-4" : "mb-4 d-flex"}>
							{refetch ? (
								<Skeleton height={535} />
							) : (
								<PlansPricingBasic
									accountData={accountData}
									planID={basicPlanID}
									priceId={priceId}
									setPlanID={setBasicPlanID}
									price={basicPrice}
									setPrice={setBasicPrice}
									pendingChangeDate={pendingChangeDate}
									trialAvailable={trialAvailable}
									clockTime={clockTime}
									changePlan={changePlan}
									product={basicProduct}
								/>
							)}
						</Col>
					)}
					{accountData?.subscription.type !== ProductTypeEnum.ENTERPRISE && (
						<Col sm="12" md="6" lg="5" xl="4" className={refetch ? "mb-4" : "mb-4 d-flex"}>
							{refetch ? (
								<Skeleton height={535} />
							) : (
								<PlansPricingPro
									accountData={accountData}
									planID={proPlanID}
									priceId={priceId}
									setPlanID={setProPlanID}
									price={proPrice}
									setPrice={setProPrice}
									pendingChangeDate={pendingChangeDate}
									trialAvailable={trialAvailable}
									clockTime={clockTime}
									changePlan={changePlan}
									product={proProduct}
									disableTrialButton={disableTrialButton}
								/>
							)}
						</Col>
					)}
					<Col sm="12" md="6" lg="5" xl="4" className={refetch ? "mb-4" : "mb-4 d-flex"}>
						{refetch ? (
							<Skeleton height={535} />
						) : (
							<PlansPricingEnterprise
								accountData={accountData}
								planID={enterprisePlanID}
								priceId={priceId}
								setPlanID={setEnterprisePlanID}
								price={enterprisePrice}
								setPrice={setEnterprisePrice}
								pendingChangeDate={pendingChangeDate}
								trialAvailable={trialAvailable}
								clockTime={clockTime}
								changePlan={changePlan}
								product={enterpriseProduct}
								disableTrialButton={disableTrialButton}
							/>
						)}
					</Col>
				</Row>
			</PageBody>
			<ChangePlanModal
				nextPriceId={nextPriceId}
				upgradeToPro={upgradeToPro}
				backToFreePlan={backToFreePlan}
				setRefetch={setRefetch}
				accountData={accountData}
				visible={displayChangePlan}
				onClose={() => {
					setDisplayChangePlan(false);
				}}
			/>
		</>
	);
};

export default PlansPricing;
