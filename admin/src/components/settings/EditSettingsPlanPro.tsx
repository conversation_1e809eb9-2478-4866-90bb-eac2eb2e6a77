import React from "react";
import { useNavigate } from "react-router-dom";
import { BtnIcon, CancelPlan, PlanItemBtn, TextLink } from "../../styles/components";
import { Account } from "../../types/account";
import { useTranslation } from "../hooks/translations";
import { StarIcon } from "@src/assets";
import { daysBetweenTimestampAndNow } from "../../utils/time";
import { PaymentMethod } from "@src/types/payment";

interface EditSettingsPlanProProps {
	paymentMethod: PaymentMethod | null;
	accountData: Account | undefined;
	clockTime: number | undefined;
	setDisplayApPro: (value: boolean) => void;
}

export const EditSettingsPlanPro: React.FC<EditSettingsPlanProProps> = (props: EditSettingsPlanProProps) => {
	const translation = useTranslation();
	const navigate = useNavigate();

	const pendingChangeDate = props.accountData?.subscription.pendingChangeDate;
	const trialEndDate = props.accountData?.subscription.trialEndDate;
	const nextBillingDate = props.accountData?.subscription.nextBillingDate;
	const trialActive = props.accountData?.subscription.trialActive;

	return (
		<>
			<PlanItemBtn pro={true} noHover={true} className="mb-2">
				<BtnIcon src={StarIcon} alt={translation.general.proPlan} />
				<span>{translation.general.proPlan}</span>
			</PlanItemBtn>
			{pendingChangeDate ? (
				<>
					<div className="mt-3 mb-4">
						{translation.plansPage.proExpiryText1}
						<span style={{ fontWeight: "bold" }}>
							{" " + daysBetweenTimestampAndNow(pendingChangeDate, props.clockTime) + " "}
						</span>
						{translation.plansPage.proExpiryText2}
					</div>
				</>
			) : (process.env.SHOW_PLANS_PAGE === "true" && props.paymentMethod) ? (
				<>
					{trialActive && (
						<div className="mt-3">
							{translation.accountSettingsPage.subscriptionText1}
							<span style={{ fontWeight: "bold" }}>
								{nextBillingDate && daysBetweenTimestampAndNow(nextBillingDate, props.clockTime)}{" "}
								{translation.accountSettingsPage.subscriptionText2 + " "}
							</span>
							{translation.accountSettingsPage.subscriptionText3}
						</div>
					)}
					<CancelPlan data-testid="changeSubscription" onClick={() => navigate("/plans-pricing")}>
						{translation.general.changeSubscription}
					</CancelPlan>
				</>
			) : (
				<>
					<div className="mt-3 mb-4">
						{translation.accountSettingsPage.trialExpiryText1 + " "}
						{translation.accountSettingsPage.trialExpiryText2 + " "}
						<span style={{ fontWeight: "bold" }}>
							{trialEndDate && daysBetweenTimestampAndNow(trialEndDate, props.clockTime)}{" "}
							{translation.accountSettingsPage.trialExpiryText3 + " "}
						</span>
						<TextLink onClick={() => props.setDisplayApPro(true)}>
							{translation.accountSettingsPage.addPaymentMethod}
						</TextLink>
					</div>
				</>
			)}
		</>
	);
};
