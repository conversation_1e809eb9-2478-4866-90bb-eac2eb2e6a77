import React, { useState } from "react";
import { RemoveButton } from "@src/styles/forms";
import { Flex, ListBox, ListItemContainer, ListRowItem } from "@src/styles/components";
import { ConfirmChangesModal } from "../modals/ConfirmChangesModal";
import { usePasswordRequired } from "../utils/getPasswordRequired";
import { useTokenCheck } from "../hooks/useTokenCheck";
import { getErrorString } from "../utils/getErrorString";
import { useTranslation } from "../hooks/translations";
import { Key } from "@src/types/keys";
import deleteAPIKey from "../utils/deleteAPIKey";
import { DeleteKeyModal } from "../modals/DeleteKeyModal";

type Props = {
	keyItem: Key;
	refreshList: () => void;
	showDeleteError: (value: string) => void;
	showConfirmationText: (value: string) => void;
	numberId: number;
};

const ListItem: React.FC<Props> = ({ keyItem, numberId, refreshList, showConfirmationText, showDeleteError }) => {
	const translation = useTranslation();
	const [modalStatus, setModalStatus] = useState(false);
	const [enterPassword, setEnterPassword] = useState(false);
	const passwordRequired = usePasswordRequired();
	const { apiRetryHandler } = useTokenCheck();
	const [loading, setLoading] = useState(false);

	const handleDelete = async () => {
		setLoading(true);
		const { error } = await apiRetryHandler(async () => await deleteAPIKey(keyItem._id));

		if (error) {
			const errorText = getErrorString(translation, error?.response?.data?.error);
			showDeleteError(errorText);
		} else {
			showConfirmationText(translation.modals.removeKeyConfirmation);
			refreshList();
			showDeleteError("");
		}

		setModalStatus(false);
		setLoading(false);
	};

	return (
		<>
			<ListItemContainer data-testid={`keyListRow${numberId}`}>
				<Flex>
					<ListBox style={{ width: "90%" }}>
						<ListRowItem data-testid={`key${numberId}`}>{`********************************${keyItem.keyLast4}`}</ListRowItem>
					</ListBox>
					<ListBox style={{ width: "10%" }} last={true}>
						<ListRowItem>
							<RemoveButton data-testid={`removeButton${numberId}`} onClick={() => setModalStatus(true)} />
						</ListRowItem>
					</ListBox>
				</Flex>
			</ListItemContainer>
			<DeleteKeyModal
				visible={modalStatus}
				setVisible={setModalStatus}
				onCancel={() => setModalStatus(false)}
				setEnterPassword={setEnterPassword}
				handleDelete={handleDelete}
				passwordRequired={passwordRequired ?? false}
				loading={loading}
				hiddenKey={`********************************${keyItem.keyLast4}`}
			/>
			<ConfirmChangesModal visible={enterPassword} onCancel={() => setEnterPassword(false)} onContinue={handleDelete} />
		</>
	);
};

export default ListItem;
