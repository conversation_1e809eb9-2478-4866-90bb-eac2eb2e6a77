import React from "react";
import { useNavigate } from "react-router-dom";
import { BtnIcon, PlanItemBtn } from "../../styles/components";
import { useTranslation } from "../hooks/translations";
import { CheckIcon, StarIcon } from "@src/assets";

export const EditSettingsPlanBasic: React.FC = () => {
	const translation = useTranslation();
	const navigate = useNavigate();

	return (
		<>
			<PlanItemBtn className="mb-3" noHover={true}>
				<BtnIcon src={CheckIcon} alt={translation.general.freePlan} />
				<span>{translation.general.freePlan}</span>
			</PlanItemBtn>

			{process.env.SHOW_PLANS_PAGE === "true" &&
				(<PlanItemBtn
					pro={true}
					onClick={() => {
						navigate("/plans-pricing");
					}}
					className="mb-2"
				>
					<BtnIcon src={StarIcon} alt={translation.general.unlockProPlan} />
					<span>{translation.general.unlockProPlan}</span>
				</PlanItemBtn>)
			}
		</>
	);
};
