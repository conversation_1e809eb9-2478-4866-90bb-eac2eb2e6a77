import React from "react";
import { useNavigate } from "react-router-dom";
import { BtnIcon, CancelPlan, PlanItemBtn } from "../../styles/components";
import { Account } from "../../types/account";
import { useTranslation } from "../hooks/translations";
import { StarIcon } from "@src/assets";
import { ProductTypeEnum } from "../../types/product";

interface EditSettingsPlanEnterpriseProps {
	accountData: Account | undefined;
}

export const EditSettingsPlanEnterprise: React.FC<EditSettingsPlanEnterpriseProps> = (
	props: EditSettingsPlanEnterpriseProps
) => {
	const translation = useTranslation();
	const navigate = useNavigate();

	return (
		<>
			<PlanItemBtn pro={props.accountData?.subscription.type !== ProductTypeEnum.BASIC} noHover={true} className="mb-2">
				<BtnIcon src={StarIcon} alt={translation.general.enterprise} />
				<span>{translation.general.enterprise}</span>
			</PlanItemBtn>

			{process.env.SHOW_PLANS_PAGE === "true" && props.accountData?.subscription.hasPaymentMethod && (
				<CancelPlan data-testid="changeSubscription" onClick={() => navigate("/plans-pricing")}>
					{translation.general.changeSubscription}
				</CancelPlan>
			)}
		</>
	);
};
