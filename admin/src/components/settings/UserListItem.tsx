import React, { useState } from "react";
import { RemoveButton } from "@src/styles/forms";
import { RemoveUserModal } from "../modals/RemoveUserModal";
import { Flex, ListBox, ListItemContainer, ListRowItem, UserListStatus } from "@src/styles/components";
import { UserWithInvite } from "@src/types/users";
import { ConfirmChangesModal } from "../modals/ConfirmChangesModal";
import { usePasswordRequired } from "../utils/getPasswordRequired";
import { useTokenCheck } from "../hooks/useTokenCheck";
import removeUser from "../utils/removeUser";
import { getErrorString } from "../utils/getErrorString";
import { useTranslation } from "../hooks/translations";

type Props = {
	userItem: UserWithInvite;
	refreshList: () => void;
	showDeleteError: (value: string) => void;
	showConfirmationText: (value: string) => void;
	numberId: number;
};

const ListItem: React.FC<Props> = ({ userItem, numberId, refreshList, showConfirmationText, showDeleteError }) => {
	const translation = useTranslation();
	const [modalStatus, setModalStatus] = useState(false);
	const [enterPassword, setEnterPassword] = useState(false);
	const passwordRequired = usePasswordRequired();
	const { apiRetryHandler } = useTokenCheck();
	const [loading, setLoading] = useState(false);

	const handleDelete = async () => {
		setLoading(true);
		const { error } = await apiRetryHandler(async () => await removeUser(userItem._id, userItem.isInvite));

		if (error) {
			const errorText = getErrorString(translation, error?.response?.data?.error);
			showDeleteError(errorText);
		} else {
			showConfirmationText(translation.modals.removeUserConfirmation);
			refreshList();
		}

		setModalStatus(false);
		setLoading(false);
	};

	return (
		<>
			<ListItemContainer data-testid={`userListRow${numberId}`}>
				<Flex>
					<ListBox style={{ width: "30%" }}>
						<ListRowItem data-testid={`userListName${numberId}`}>{`${userItem.firstName ?? ""} ${
							userItem.lastName ?? ""
						}`}</ListRowItem>
					</ListBox>
					<ListBox style={{ width: "30%" }}>
						<ListRowItem data-testid={`userListEmail${numberId}`}>{userItem.email}</ListRowItem>
					</ListBox>
					<ListBox style={{ width: "30%" }}>
						<UserListStatus status={userItem.status}>{userItem.status}</UserListStatus>
					</ListBox>
					<ListBox style={{ width: "10%" }} last={true}>
						{!userItem.isOwner && !userItem.isCurrentUser && (
							<ListRowItem>
								<RemoveButton data-testid={`removeButton${numberId}`} onClick={() => setModalStatus(true)} />
							</ListRowItem>
						)}
					</ListBox>
				</Flex>
			</ListItemContainer>
			<RemoveUserModal
				visible={modalStatus}
				setVisible={setModalStatus}
				onCancel={() => setModalStatus(false)}
				setEnterPassword={setEnterPassword}
				handleDelete={handleDelete}
				passwordRequired={passwordRequired ?? false}
				loading={loading}
			/>
			<ConfirmChangesModal visible={enterPassword} onCancel={() => setEnterPassword(false)} onContinue={handleDelete} />
		</>
	);
};

export default ListItem;
