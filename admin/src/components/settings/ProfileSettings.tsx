import React, { useState, useEffect } from "react";
import { Form, Row, Col } from "react-bootstrap";
import Skeleton from "react-loading-skeleton";
import Spinner from "react-bootstrap/Spinner";
import { PageBody, PageRow, MainButton, VideoPageTitleText, LinkButton } from "@src/styles/components";
import { HeadingText, ConfirmationBox, ConfirmationBoxWrapper, CustomInput, BodyText } from "@src/styles/forms";
import { useTranslation } from "../hooks/translations";
import { getErrorString } from "../utils/getErrorString";
import getAccount from "../utils/getAccount";
import updateAccount from "../utils/updateAccount";
import { useTokenCheck } from "../hooks/useTokenCheck";
import { companyDetails } from "@src/types/videos";
import SelectCompany from "../company/SelectCompany";
import sendPasswordResetEmail from "../utils/sendPasswordResetEmail";
import { CheckEmailModal } from "../modals/CheckEmailModal";
import ErrorMessage from "@src/components/utils/ErrorMessage";
import deepEqual from "deep-equal";

const ProfileSettings: React.FC = () => {
	const translation = useTranslation();
	const { apiRetryHandler } = useTokenCheck();
	const [settingsError, setSettingsError] = useState("");
	const [passwordError, setPasswordError] = useState("");
	const [confirmationText, setConfirmationText] = useState("");
	const [settingsData, setSettingsData] = useState<companyDetails>();
	const [settingsDataCopy, setSettingsDataCopy] = useState<companyDetails>();
	const [refetch, setRefetch] = useState(true);
	const [generateButton, setGenerateButton] = useState(true);
	const [loading, setLoading] = useState(false);
	const [companyLogo] = useState<File>();
	const [, setLogoPreview] = useState("");
	const [, setCompanyName] = useState("");
	const [companyURL, setCompanyURL] = useState("");
	const [email, setEmail] = useState("");
	const [firstName, setFirstName] = useState("");
	const [lastName, setLastName] = useState("");
	const [isPasswordSet, setIsPasswordSet] = useState(true);
	const [showCheckEmailModal, setShowCheckEmailModal] = useState(false);

	const updateVideo = async () => {
		setGenerateButton(true);
		setLoading(true);
		const { error } = await apiRetryHandler(async () => await updateAccount({ logo: companyLogo as File, firstName, lastName, companyURL }));

		if (error) {
			setGenerateButton(false);
			setLoading(false);
			const errorText = getErrorString(translation, error?.response?.data?.error);
			setSettingsError(errorText);
		} else {
			setSettingsError("");
			setConfirmationText(translation.profilePage.dataUpdated);
			setTimeout(() => {
				setConfirmationText("");
				setRefetch(true);
				setLoading(false);
			}, 1000);
		}
	};

	const dataChanged = !deepEqual(settingsData, settingsDataCopy);

	useEffect(() => {
		if (dataChanged) {
			setGenerateButton(false);
		} else {
			setGenerateButton(true);
		}
	}, [dataChanged]);

	useEffect(() => {
		const go = async () => {
			const { data, error } = await apiRetryHandler(async () => await getAccount());

			if (error) {
				setSettingsError(translation.profilePage.loadingIssue);
			} else {
				setSettingsData(data as companyDetails);
				setSettingsDataCopy(data as companyDetails);

				setLogoPreview(data?.companyLogo);
				setCompanyName(data?.companyName);
				setCompanyURL(data?.companyURL);
				setEmail(data?.email);
				setFirstName(data?.firstName);
				setLastName(data?.lastName);
				setIsPasswordSet(data?.isPasswordSet);
			}
			setRefetch(false);
		};

		if (refetch) {
			go();
		}
	}, [refetch, apiRetryHandler, translation.profilePage.loadingIssue]);

	const handlePasswordResetEmail = async () => {
		const { error } = await apiRetryHandler(async () => await sendPasswordResetEmail(translation.locale, email));
		if (error) {
			setPasswordError(getErrorString(translation, error?.response?.data?.error));
		} else {
			setShowCheckEmailModal(true);
		}
	};

	return (
		<>
			<ConfirmationBoxWrapper>
				{!!confirmationText && (
					<ConfirmationBox className="text-center" data-testid="deleteConfirmation">
						{confirmationText}
					</ConfirmationBox>
				)}
				{!!settingsError && <ErrorMessage error={settingsError} setError={setSettingsError} displayCloseIcon={true} />}
			</ConfirmationBoxWrapper>
			<PageBody>
				<PageRow className="mb-4">
					<HeadingText data-testid="editVideoPage">{translation.profilePage.pageTitle}</HeadingText>
					<MainButton type="button" data-testid="updateButton" disabled={generateButton} onClick={updateVideo}>
						{translation.profilePage.saveChanges}
						{loading ? <Spinner as="span" size="sm" role="status" aria-hidden="true" style={{ marginLeft: "1rem" }} /> : ""}
					</MainButton>
				</PageRow>

				{refetch ? (
					<Row pl="0" pr="0">
						<Col sm="12" md="4" className="mb-4">
							<Skeleton count={10} height={50} />
						</Col>
						<Col sm="12" md="4" className="mb-4">
							<Skeleton height={535} />
						</Col>
						<Col sm="12" md="4" className="mb-4">
							<Skeleton height={535} />
						</Col>
					</Row>
				) : (
					<Row pl="0" pr="0">
						<Col sm="12" md="4" className="mb-4">
							<VideoPageTitleText className="mb-3" style={{ paddingLeft: 0 }}>
								{translation.profilePage.personalDetails}
							</VideoPageTitleText>
							<Form.Group className="mb-3">
								<CustomInput style={{ maxWidth: "500px" }} className="form-control" type="email" disabled placeholder={translation.profilePage.email} id="email" data-testid="email" value={email} />
							</Form.Group>
							<Form.Group className="mb-3">
								<CustomInput
									style={{ maxWidth: "500px" }}
									className="form-control"
									type="text"
									required
									placeholder={translation.profilePage.firstName}
									id="firstName"
									data-testid="firstName"
									onChange={(value) => {
										setFirstName(value.target.value);
										setSettingsDataCopy({
											...settingsDataCopy,
											firstName: value.target.value
										});
									}}
									value={firstName}
								/>
							</Form.Group>
							<Form.Group className="mb-3">
								<CustomInput
									style={{ maxWidth: "500px" }}
									className="form-control"
									type="text"
									required
									placeholder={translation.profilePage.lastName}
									id="lastName"
									data-testid="lastName"
									onChange={(value) => {
										setLastName(value.target.value);
										setSettingsDataCopy({
											...settingsDataCopy,
											lastName: value.target.value
										});
									}}
									value={lastName}
								/>
							</Form.Group>
						</Col>
						<Col sm="12" md="4" className="mb-4">
							<VideoPageTitleText className="mb-3" style={{ paddingLeft: 0 }}>
								{translation.profilePage.yourCompanies}
							</VideoPageTitleText>
							<SelectCompany profileView={true} />
						</Col>
						<Col sm="12" md="4" className="mb-4">
							{isPasswordSet ? (
								<>
									<VideoPageTitleText className="mb-3" style={{ paddingLeft: 0 }}>
										{translation.profilePage.password}
									</VideoPageTitleText>
									<Form.Group className="mb-3">
										<CustomInput style={{ maxWidth: "500px" }} className="form-control" type="password" disabled id="password" data-testid="password" value="***************" />
									</Form.Group>
									<BodyText>{translation.profilePage.forgotPasswordText}</BodyText>
									<LinkButton style={{ textDecoration: "underline", padding: 0 }} data-testid="setPasswordLink" onClick={handlePasswordResetEmail}>
										{translation.profilePage.resetPasswordText}
									</LinkButton>
								</>
							) : (
								<>
									<VideoPageTitleText className="mb-3" style={{ paddingLeft: 0 }}>
										{translation.profilePage.password}
									</VideoPageTitleText>
									<BodyText>{translation.profilePage.noPasswordSetText}</BodyText>
									<LinkButton style={{ textDecoration: "underline", padding: 0 }} data-testid="setPasswordLink" onClick={handlePasswordResetEmail}>
										{translation.profilePage.setPasswordText}
									</LinkButton>
								</>
							)}
							{!!passwordError && (
								<div className="mt-4">
									<ErrorMessage error={passwordError} setError={setPasswordError} displayCloseIcon={true} />
								</div>
							)}
						</Col>
					</Row>
				)}
			</PageBody>
			<CheckEmailModal visible={showCheckEmailModal} onCancel={() => setShowCheckEmailModal(false)} />
		</>
	);
};

export default ProfileSettings;
