import React, { useEffect, useState } from "react";
import { ProIconHeader, ProLinkButton } from "@src/styles/components";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "../hooks/translations";
import { Account } from "@src/types/account";
import { StarIcon } from "@src/assets";
import { ProductTypeEnum } from "../../types/product";
import { HeaderPlanState } from "../../types/header";
import { daysBetweenTimestampAndNow } from "@src/utils/time";

interface HeaderPlanProps {
	trialActive: boolean | undefined;
	accountData: Account | undefined;
}

export const HeaderPlan: React.FC<HeaderPlanProps> = (props: HeaderPlanProps) => {
	const navigate = useNavigate();
	const translation = useTranslation();
	const [planState, setPlanState] = useState<string>("");
	const showPlansPage = process.env.SHOW_PLANS_PAGE === "true";

	useEffect(() => {
		if (props.accountData) {
			const subscription = props.accountData.subscription;
			if (subscription?.trialActive) {
				setPlanState(HeaderPlanState.TRIAL);
			} else if (subscription?.type === ProductTypeEnum.ENTERPRISE) {
				setPlanState(HeaderPlanState.ENTERPRISE);
			} else if (subscription?.type === ProductTypeEnum.PRO) {
				setPlanState(HeaderPlanState.PRO);
			} else {
				setPlanState(HeaderPlanState.BASIC);
			}
		}
	}, [props.trialActive, props.accountData]);

	const renderPlanLabel = () => {
		switch (planState) {
			case HeaderPlanState.ENTERPRISE:
				return translation.general.enterprise;
			case HeaderPlanState.TRIAL:
				return `${
					translation.general.trialDays +
					" " +
					(props.accountData?.subscription?.trialEndDate &&
						daysBetweenTimestampAndNow(
							props.accountData.subscription.trialEndDate,
							props.accountData.subscription?.clockTime
						) +
							"/" +
							props.accountData?.subscription?.trialDaysTotal)
				}`;
			case HeaderPlanState.PRO:
				return translation.general.pro;
			case HeaderPlanState.BASIC:
			default:
				return showPlansPage ? translation.general.unlockProPlan : translation.general.basic;
		}
	};

	if (!props.accountData) {
		return null;
	}

	return (
		<ProLinkButton
			basic={planState === HeaderPlanState.BASIC}
			clickable={showPlansPage}
			onClick={showPlansPage ? () => navigate("/plans-pricing") : undefined}
		>
			<ProIconHeader data-testid="StarIcon" src={StarIcon} />
			{renderPlanLabel()}
		</ProLinkButton>
	);
};
