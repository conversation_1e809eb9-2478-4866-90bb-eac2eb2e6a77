import React, { useState } from "react";
import { Flex, MainButton, StripeFieldContainer } from "@src/styles/components";
import { useStripe, useElements, CardNumberElement, CardExpiryElement, CardCvcElement } from "@stripe/react-stripe-js";
import { CreatePaymentMethodData, PaymentMethod, Stripe, StripeElements } from "@stripe/stripe-js";
import { useTranslation } from "../hooks/translations";
import attachPaymentMethod from "../utils/attachPaymentMethod";
import { validatePostalCode } from "@src/utils/addresses";
import { getNames, getCodes } from "country-list";

interface StripePaymentFormProps {
	onFormFailure: (error: unknown) => void;
	onFormSuccess: (paymentMethod: PaymentMethod) => void;
	enableDoLaterOption?: boolean;
	doLaterAction?: () => void;
	disableButton: boolean;
	setDisableButton: (value: boolean) => void;
}

// eslint-disable-next-line react/prop-types
export const StripePaymentForm: React.FC<StripePaymentFormProps> = ({
	onFormFailure,
	onFormSuccess,
	enableDoLaterOption = false,
	doLaterAction,
	disableButton,
	setDisableButton
}) => {
	const translation = useTranslation();
	const stripe = useStripe();
	const elements = useElements();
	const [postalCode, setPostalCode] = useState("");
	const [postalCodeError, setPostalCodeError] = useState("");
	const [country, setCountry] = useState("CA");
	const countryNames = getNames();
	const countryCodes = getCodes();

	const handleSubmit = async (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
		e.preventDefault();
		setPostalCodeError("");

		if (!validatePostalCode(postalCode, country)) {
			setPostalCodeError(translation.modals.stripePostalCodeError);
			return;
		}

		try {
			setDisableButton(true);
			if (!stripe || !elements) {
				return;
			}
			const paymentMethod = await addPaymentMethod(stripe, elements, postalCode, country);
			onFormSuccess(paymentMethod);
		} catch (error) {
			onFormFailure(error);
			setDisableButton(false);
		}
	};

	const addPaymentMethod = async (stripe: Stripe, elements: StripeElements, postalCode: string, country: string) => {
		const cardNumberElement = elements.getElement(CardNumberElement);
		const cardExpiryElement = elements.getElement(CardExpiryElement);
		const cardCvcElement = elements.getElement(CardCvcElement);

		if (!cardNumberElement || !cardExpiryElement || !cardCvcElement) {
			throw new Error("Card element not found");
		}

		// Stripe implicitly uses all card details (number, expiration date, CVC) that are part of the same Elements provider
		// even if the code explicitly references only the CardNumberElement
		const createPaymentMethodData: CreatePaymentMethodData = {
			type: "card",
			card: cardNumberElement,
			billing_details: {
				address: {
					postal_code: postalCode,
					country: country
				}
			}
		};

		const { error, paymentMethod } = await stripe.createPaymentMethod(createPaymentMethodData);
		if (error) {
			throw new Error(error.message);
		}

		const { error: attachError } = await attachPaymentMethod(paymentMethod.id);
		if (attachError) {
			throw new Error(attachError);
		}

		return paymentMethod;
	};

	return (
		<>
			<label style={{ display: "block", marginBottom: "1rem" }}>
				<div style={{ marginBottom: "0.5rem" }}>{translation.modals.stripeCardNumber}</div>
				<StripeFieldContainer>
					<CardNumberElement />
				</StripeFieldContainer>
			</label>
			<Flex style={{ marginBottom: "1rem" }}>
				<label style={{ width: "50%", marginRight: "0.5rem" }}>
					<div style={{ marginBottom: "0.5rem" }}>{translation.modals.stripeExpiration}</div>
					<StripeFieldContainer>
						<CardExpiryElement />
					</StripeFieldContainer>
				</label>
				<label style={{ width: "50%", marginLeft: "0.5rem" }}>
					<div style={{ marginBottom: "0.5rem" }}>{translation.modals.stripeCvc}</div>
					<StripeFieldContainer>
						<CardCvcElement />
					</StripeFieldContainer>
				</label>
			</Flex>
			<Flex style={{ marginBottom: "1rem" }}>
				<label style={{ marginBottom: "1rem", width: "50%", marginRight: "0.5rem" }}>
					<div style={{ marginBottom: "0.5rem" }}>{translation.modals.stripeCountry}</div>
					<StripeFieldContainer>
						<select
							id="country"
							value={country}
							onChange={(e) => {
								setCountry(e.target.value);
								setPostalCodeError("");
							}}
							style={{ border: "none", outline: "none", backgroundColor: "transparent", width: "100%" }}
						>
							{countryNames.map((name, index) => (
								<option key={countryCodes[index]} value={countryCodes[index]}>
									{name}
								</option>
							))}
						</select>
					</StripeFieldContainer>
				</label>
				<label style={{ marginBottom: "1rem", width: "50%", marginLeft: "0.5rem" }}>
					<div style={{ marginBottom: "0.5rem" }}>
						{country === "CA" ? translation.modals.stripePostalCode : translation.modals.stripeZipCode}
					</div>
					<StripeFieldContainer>
						<input
							type="text"
							value={postalCode}
							onChange={(e) => setPostalCode(e.target.value)}
							placeholder={
								country === "CA"
									? translation.modals.stripePostalCodePlaceholder
									: translation.modals.stripeZipCodePlaceholder
							}
							style={{ border: "none", fontSize: "0.9rem", outline: "none", backgroundColor: "transparent" }}
						/>
					</StripeFieldContainer>
					{postalCodeError && <div style={{ color: "red", fontSize: "0.9rem" }}>{postalCodeError}</div>}
				</label>
			</Flex>
			<MainButton
				style={{
					width: "100%",
					marginBottom: "2rem"
				}}
				disabled={disableButton}
				onClick={(e) => handleSubmit(e)}
			>
				{translation.modals.apProSavePaymentDetails}
			</MainButton>
			{enableDoLaterOption && (
				<div
					onClick={doLaterAction}
					style={{
						fontWeight: "bold",
						marginBottom: "1rem",
						cursor: "pointer",
						textAlign: "center"
					}}
				>
					{translation.modals.doLater}
				</div>
			)}
		</>
	);
};
