import React, { useEffect, useState } from "react";
import {
	Flex,
	PageSectionBlock,
	PageSection,
	PageSubsection,
	MetricValue,
	MetricIncrease,
	MetricDecrease,
	RegularText,
	StarIcon,
	MetricDisplayContainer,
	PerformanceHeader,
	TopVideos,
	ScoreCircle,
	MainButton,
	SortBox,
	BlurredDiv,
	CoverImageBox,
	UploadImage,
	MetricIcon,
	InlineMetricIcon
} from "@src/styles/components";
import { HeadingTextH2 } from "@src/styles/forms";
import { useTranslation } from "../hooks/translations";
import { Clicks, Plays, OpenLockIcon, ArrowsInactive, ArrowsUp, ArrowsDown } from "@src/assets";
import { secondsToFormattedTime } from "@src/utils/time";
import Skeleton from "react-loading-skeleton";
import { Metrics, MetricsDisplay, MetricsDisplayVideo } from "@src/types/metrics";
import DateRangePicker from "../inputs/DateRangePicker";
import { useTokenCheck } from "../hooks/useTokenCheck";
import getMetrics from "../utils/getMetrics";
import { useMediaQuery } from "react-responsive";
import Scorebar from "../metrics/Scorebar";
import getTopVideos from "../utils/getTopVideos";
import getTopCollections from "../utils/getTopCollections";
import { shoppableVideo } from "@src/types/videos";
import { useNavigate } from "react-router-dom";
import getConversionMetrics from "../utils/getConversionMetrics";
import { registerEvent } from "@src/components/events/service";
import { EventNameEnum } from "@src/types/events";
import { Account } from "../../types/account";
import { DisplayFormatOptions } from "@src/types/snippetOptions";
import SVG from "../svg/SVG";
import { faEye } from "@fortawesome/free-solid-svg-icons";

interface Props {
	accountData: Account | undefined;
	trialAvailable: boolean;
	hideCreateVideo: boolean;
	setVideoModalStatus: (value: boolean) => void;
}

// eslint-disable-next-line max-lines-per-function
const PerformanceMetrics: React.FC<Props> = ({ accountData, trialAvailable, hideCreateVideo, setVideoModalStatus }) => {
	const todayAtEndOfDay = new Date();
	todayAtEndOfDay.setHours(23, 59, 59, 999);
	todayAtEndOfDay.setDate(todayAtEndOfDay.getDate());
	const sevenDaysAgoAtMidnight = new Date();
	sevenDaysAgoAtMidnight.setHours(0, 0, 0, 0);
	sevenDaysAgoAtMidnight.setDate(sevenDaysAgoAtMidnight.getDate() - 7);
	const [startDate, setStartDate] = useState<Date>(sevenDaysAgoAtMidnight);
	const [endDate, setEndDate] = useState<Date>(todayAtEndOfDay);
	const [selectedStartDate, setSelectedStartDate] = useState<Date>(sevenDaysAgoAtMidnight);
	const [selectedEndDate, setSelectedEndDate] = useState<Date>(todayAtEndOfDay);
	const [metrics, setMetrics] = useState<MetricsDisplay>({});
	const [shopifyStoreConnected, setShopifyStoreConnected] = useState(false);
	const translation = useTranslation();
	const navigate = useNavigate();
	const { apiRetryHandler } = useTokenCheck();
	const isScreenSmall = useMediaQuery({ query: "(max-width: 930px)" });
	const [refetch, setRefetch] = useState(true);
	const [refetchVideo, setRefetchVideo] = useState(true);
	const [activeSort, setActiveSort] = useState(4);
	const [sortOrder1, setSortOrder1] = useState(true);
	// const [sortOrder2, setSortOrder2] = useState(true); // sort by clicks count
	const [sortOrder3, setSortOrder3] = useState(true);
	const [sortOrder4, setSortOrder4] = useState(false);
	const showPlansPage = process.env.SHOW_PLANS_PAGE === "true";

	useEffect(() => {
		const savedStartDate = sessionStorage.getItem("startDate");
		const savedEndDate = sessionStorage.getItem("endDate");

		if (savedStartDate && savedEndDate) {
			setStartDate(new Date(savedStartDate));
			setSelectedStartDate(new Date(savedStartDate));
			setEndDate(new Date(savedEndDate));
			setSelectedEndDate(new Date(savedEndDate));
		}
	}, []);

	useEffect(() => {
		sessionStorage.setItem("startDate", startDate.toISOString());
		sessionStorage.setItem("endDate", endDate.toISOString());
	}, [startDate, endDate]);

	useEffect(() => {
		(async () => {
			let newMetrics: MetricsDisplay = {};
			setRefetch(false);

			const startDateUTC = new Date(Date.UTC(
				startDate.getFullYear(),
				startDate.getMonth(),
				startDate.getDate(),
				0,
				0,
				0,
				0
			));

			const endDateUTC = new Date(Date.UTC(
				endDate.getFullYear(),
				endDate.getMonth(),
				endDate.getDate(),
				23,
				59,
				59,
				999
			));

			const { data: metricsData }: {data: Metrics} = await apiRetryHandler(
				async () => await getMetrics(startDateUTC, endDateUTC)
			);

			if (metricsData) {
				newMetrics.impressions = metricsData.currentMetrics.totalImpressions;
				newMetrics.plays = metricsData.currentMetrics.totalPlays;
				newMetrics.clicks = metricsData.currentMetrics.totalClicks;
				newMetrics.playtime = Math.round(metricsData.currentMetrics.totalPlayTimeSeconds);
				newMetrics.impressionsPrev = metricsData.previousMetrics.totalImpressions;
				newMetrics.playsPrev = metricsData.previousMetrics.totalPlays;
				newMetrics.clicksPrev = metricsData.previousMetrics.totalClicks;
				newMetrics.playtimePrev = Math.round(metricsData.previousMetrics.totalPlayTimeSeconds);
				newMetrics.impressionsDiff =
					metricsData.currentMetrics.totalImpressions - metricsData.previousMetrics.totalImpressions;
				newMetrics.playsDiff = metricsData.currentMetrics.totalPlays - metricsData.previousMetrics.totalPlays;
				newMetrics.clicksDiff = metricsData.currentMetrics.totalClicks - metricsData.previousMetrics.totalClicks;
				newMetrics.playtimeDiff = Math.round(
					metricsData.currentMetrics.totalPlayTimeSeconds - metricsData.previousMetrics.totalPlayTimeSeconds
				);
			}

			const { data: topVideos }: {data: shoppableVideo[]} = await apiRetryHandler(
				async () => await getTopVideos("dsc", "videoScore")
			);
			if (topVideos.length) {
				const videos: MetricsDisplayVideo[] = [];
				topVideos.forEach((video) => {
					const clicks = video.linkClicks?.reduce((count, link) => count + link.clickCount, 0);
					videos.push({
						_id: video._id,
						title: video.title,
						plays: video.videoPlayCount ? Math.round(video.videoPlayCount) : null,
						clicks: clicks ? Math.round(clicks) : null,
						playtime: video.videoPlayDurationSeconds ? Math.round(video.videoPlayDurationSeconds) : null,
						score: video.videoScore ? Math.round(video.videoScore) : null,
						videoPosterURL: video.videoPosterURL,
						videoDisplayMode: video.videoDisplayMode
					});
				});
				newMetrics.topVideos = videos;
			}

			if (accountData?.subscription?.enableConversionMetrics) {
				const { data: topCollections } = await apiRetryHandler(async () => await getTopCollections());
				newMetrics.topCollections = topCollections;

				if (topCollections[0]?.orderCount) {
					setShopifyStoreConnected(true);
				}
				const { data: conversionData } = await apiRetryHandler(
					async () => await getConversionMetrics(startDateUTC, endDateUTC)
				);
				newMetrics = { ...newMetrics, ...conversionData };
			}

			setMetrics(newMetrics);
			setRefetch(true);
		})();
	}, [apiRetryHandler, accountData, startDate, endDate]);

	const sortVideos = async (SortByFlag: boolean, SortKey: string) => {
		setRefetchVideo(false);
		let SortBy = "dsc";
		if (SortByFlag) SortBy = "asc";
		const { data: topVideos }: {data: shoppableVideo[]} = await apiRetryHandler(
			async () => await getTopVideos(SortBy, SortKey)
		);
		if (topVideos.length) {
			const videos: MetricsDisplayVideo[] = [];
			topVideos.forEach((video) => {
				const clicks = video.linkClicks?.reduce((count, link) => count + link.clickCount, 0);
				videos.push({
					_id: video._id,
					title: video.title,
					plays: video.videoPlayCount ? Math.round(video.videoPlayCount) : null,
					clicks: clicks ? Math.round(clicks) : null,
					playtime: video.videoPlayDurationSeconds ? Math.round(video.videoPlayDurationSeconds) : null,
					score: video.videoScore ? Math.round(video.videoScore) : null,
					videoPosterURL: video.videoPosterURL,
					videoDisplayMode: video.videoDisplayMode
				});
			});
			metrics.topVideos = videos;
		}
		setRefetchVideo(true);
	};

	const handleSortClick = (sortKey: string, setSortOrder: React.Dispatch<React.SetStateAction<boolean>>) => {
		setSortOrder((prevOrder: boolean) => {
			const newOrder = !prevOrder;
			sortVideos(newOrder, sortKey);
			return newOrder;
		});
	};

	return (
		<>
			<PageSection>
				<PerformanceHeader>
					<HeadingTextH2 style={{ fontSize: "1.4rem" }}>{translation.performancePage.pageTitle}</HeadingTextH2>
					<Flex>
						<DateRangePicker
							compact={isScreenSmall}
							startDate={selectedStartDate}
							setStartDate={setSelectedStartDate}
							endDate={selectedEndDate}
							setEndDate={setSelectedEndDate}
							setNewStartDate={setStartDate}
							setNewEndDate={setEndDate}
						/>
					</Flex>
				</PerformanceHeader>

				{/* Performance Data */}

				{refetch ? (
					<>
						<MetricDisplayContainer>
							<PageSubsection>
								<MetricIcon>
									<SVG src={faEye} />
								</MetricIcon>
								<HeadingTextH2 style={{ fontSize: "1rem", margin: "0.5rem 0" }}>
									{translation.performancePage.impressionsHeading}
								</HeadingTextH2>
								<MetricValue>
									{metrics.impressions?.toLocaleString() !== "0" ? metrics.impressions?.toLocaleString() : "-"}
								</MetricValue>
								{metrics.impressionsDiff && metrics.impressionsDiff > 0 ? (
									<MetricIncrease>&uarr; {metrics.impressionsDiff?.toLocaleString()}</MetricIncrease>
								) : metrics.impressionsDiff && metrics.impressionsDiff < 0 ? (
									<MetricDecrease>
										&darr; {metrics.impressionsDiff && Math.abs(metrics.impressionsDiff).toLocaleString()}
									</MetricDecrease>
								) : (
									<></>
								)}
							</PageSubsection>
							<PageSubsection>
								<MetricIcon>
									<SVG src={Plays} />
								</MetricIcon>
								<HeadingTextH2 style={{ fontSize: "1rem", margin: "0.5rem 0" }}>
									{translation.performancePage.playsHeading}
								</HeadingTextH2>
								<MetricValue>
									{metrics.plays?.toLocaleString() !== "0" ? metrics.plays?.toLocaleString() : "-"}
								</MetricValue>
								{metrics.playsDiff && metrics.playsDiff > 0 ? (
									<MetricIncrease>&uarr; {metrics.playsDiff?.toLocaleString()}</MetricIncrease>
								) : metrics.playsDiff && metrics.playsDiff < 0 ? (
									<MetricDecrease>
										&darr; {metrics.playsDiff && Math.abs(metrics.playsDiff).toLocaleString()}
									</MetricDecrease>
								) : (
									<></>
								)}
							</PageSubsection>
							<PageSubsection>
								<MetricIcon>
									<SVG src={Clicks} />
								</MetricIcon>
								<HeadingTextH2 style={{ fontSize: "1rem", margin: "0.5rem 0" }}>
									{translation.performancePage.clicksHeading}
								</HeadingTextH2>
								<MetricValue>
									{metrics.clicks?.toLocaleString() !== "0" ? metrics.clicks?.toLocaleString() : "-"}
								</MetricValue>
								{metrics.clicksDiff && metrics.clicksDiff > 0 ? (
									<MetricIncrease>&uarr; {metrics.clicksDiff?.toLocaleString()}</MetricIncrease>
								) : metrics.clicksDiff && metrics.clicksDiff < 0 ? (
									<MetricDecrease>
										&darr; {metrics.clicksDiff && Math.abs(metrics.clicksDiff).toLocaleString()}
									</MetricDecrease>
								) : (
									<></>
								)}
							</PageSubsection>
							<PageSubsection>
								<HeadingTextH2 style={{ fontSize: "1rem", margin: "0.5rem 0" }}>
									{translation.performancePage.playtimeHeading}
								</HeadingTextH2>
								<MetricValue>{metrics.playtime ? secondsToFormattedTime(metrics.playtime) : "-"}</MetricValue>
								{metrics.playtimeDiff && metrics.playtimeDiff > 0 ? (
									<MetricIncrease>
										&uarr; {metrics.playtimeDiff && secondsToFormattedTime(metrics.playtimeDiff)}
									</MetricIncrease>
								) : metrics.playtimeDiff && metrics.playtimeDiff < 0 ? (
									<MetricDecrease>
										&darr; {metrics.playtimeDiff && secondsToFormattedTime(Math.abs(metrics.playtimeDiff))}
									</MetricDecrease>
								) : (
									<></>
								)}
							</PageSubsection>
						</MetricDisplayContainer>

						{/* Pro Performance Data */}

						<MetricDisplayContainer>
							<PageSubsection
								style={{ cursor: (accountData?.subscription.enableConversionMetrics || !showPlansPage) ? "" : "pointer" }}
								onClick={() => (!accountData?.subscription.enableConversionMetrics && showPlansPage) && navigate("/plans-pricing")}
							>
								{!accountData?.subscription.enableConversionMetrics && (
									<MetricIcon>
										<SVG src={OpenLockIcon} />
									</MetricIcon>
								)}
								<HeadingTextH2 style={{ fontSize: "1rem", margin: "0.5rem 0" }}>
									{translation.performancePage.engagedSessionsHeading}
								</HeadingTextH2>
								{accountData?.subscription.enableConversionMetrics ? (
									<>
										<MetricValue>
											{metrics.engagedSessions && metrics.engagedSessions?.toLocaleString() !== "0"
												? metrics.engagedSessions?.toLocaleString()
												: "-"}
										</MetricValue>
										{metrics.engagedSessionsDiff && metrics.engagedSessionsDiff > 0 ? (
											<MetricIncrease>&uarr; {metrics.engagedSessionsDiff?.toLocaleString()}</MetricIncrease>
										) : metrics.engagedSessionsDiff && metrics.engagedSessionsDiff < 0 ? (
											<MetricDecrease>
												&darr; {metrics.engagedSessionsDiff && Math.abs(metrics.engagedSessionsDiff).toLocaleString()}
											</MetricDecrease>
										) : (
											<></>
										)}
									</>
								) : (
									<BlurredDiv />
								)}
							</PageSubsection>
							<PageSubsection
								style={{ cursor: (accountData?.subscription.enableConversionMetrics || !showPlansPage) ? "" : "pointer" }}
								onClick={() => (!accountData?.subscription.enableConversionMetrics && showPlansPage) && navigate("/plans-pricing")}
							>
								{!accountData?.subscription.enableConversionMetrics && (
									<MetricIcon>
										<SVG src={OpenLockIcon} />
									</MetricIcon>
								)}
								<HeadingTextH2 style={{ fontSize: "1rem", margin: "0.5rem 0" }}>
									{translation.performancePage.totalOrdersHeading}
								</HeadingTextH2>
								{accountData?.subscription.enableConversionMetrics ? (
									<>
										<MetricValue>
											{metrics.totalOrders && metrics.totalOrders?.toLocaleString() !== "0"
												? metrics.totalOrders?.toLocaleString()
												: "-"}
										</MetricValue>
										{metrics.totalOrdersDiff && metrics.totalOrdersDiff > 0 ? (
											<MetricIncrease>&uarr; {metrics.totalOrdersDiff?.toLocaleString()}</MetricIncrease>
										) : metrics.totalOrdersDiff && metrics.totalOrdersDiff < 0 ? (
											<MetricDecrease>
												&darr; {metrics.totalOrdersDiff && Math.abs(metrics.totalOrdersDiff).toLocaleString()}
											</MetricDecrease>
										) : (
											<></>
										)}
									</>
								) : (
									<BlurredDiv />
								)}
							</PageSubsection>
							<PageSubsection
								style={{ cursor: (accountData?.subscription.enableConversionMetrics || !showPlansPage) ? "" : "pointer" }}
								onClick={() => (!accountData?.subscription.enableConversionMetrics && showPlansPage) && navigate("/plans-pricing")}
							>
								{!accountData?.subscription.enableConversionMetrics && (
									<MetricIcon>
										<SVG src={OpenLockIcon} />
									</MetricIcon>
								)}
								<HeadingTextH2 style={{ fontSize: "1rem", margin: "0.5rem 0" }}>
									{translation.performancePage.averageBasketSizeHeading}
								</HeadingTextH2>
								{accountData?.subscription.enableConversionMetrics ? (
									<>
										<MetricValue>
											{metrics.avgBasketSize && metrics.avgBasketSize?.toLocaleString() !== "0.0"
												? metrics.avgBasketSize?.toLocaleString()
												: "-"}
										</MetricValue>
										{metrics.avgBasketSizeDiff && metrics.avgBasketSizeDiff > 0 ? (
											<MetricIncrease>&uarr; {metrics.avgBasketSizeDiff?.toLocaleString()}</MetricIncrease>
										) : metrics.avgBasketSizeDiff && metrics.avgBasketSizeDiff < 0 ? (
											<MetricDecrease>
												&darr; {metrics.avgBasketSizeDiff && Math.abs(metrics.avgBasketSizeDiff).toLocaleString()}
											</MetricDecrease>
										) : (
											<></>
										)}
									</>
								) : (
									<BlurredDiv />
								)}
							</PageSubsection>
							<PageSubsection
								style={{ cursor: (accountData?.subscription.enableConversionMetrics || !showPlansPage) ? "" : "pointer" }}
								onClick={() => (!accountData?.subscription.enableConversionMetrics && showPlansPage) && navigate("/plans-pricing")}
							>
								{!accountData?.subscription.enableConversionMetrics && (
									<MetricIcon>
										<SVG src={OpenLockIcon} />
									</MetricIcon>
								)}
								<HeadingTextH2 style={{ fontSize: "1rem", margin: "0.5rem 0" }}>
									{translation.performancePage.converstionRateHeading}
								</HeadingTextH2>
								{accountData?.subscription.enableConversionMetrics ? (
									<>
										<MetricValue>
											{metrics.conversionRate && metrics.conversionRate?.toLocaleString() !== "0.00"
												? metrics.conversionRate + "%"
												: "-"}
										</MetricValue>
										{metrics.conversionRateDiff && metrics.conversionRateDiff > 0 ? (
											<MetricIncrease>&uarr; {metrics.conversionRateDiff}%</MetricIncrease>
										) : metrics.conversionRateDiff && metrics.conversionRateDiff < 0 ? (
											<MetricDecrease>
												&darr; {metrics.conversionRateDiff && Math.abs(metrics.conversionRateDiff)}%
											</MetricDecrease>
										) : (
											<></>
										)}
									</>
								) : (
									<BlurredDiv />
								)}
							</PageSubsection>
						</MetricDisplayContainer>
					</>
				) : (
					<div style={{ marginTop: "3rem" }}>
						<Skeleton className="mt-2" count={6} height={50} />
					</div>
				)}
			</PageSection>

			{/* Top Performing Videos */}

			<PageSection style={{ margin: "1rem 0" }}>
				<div style={{ display: isScreenSmall ? "block" : "flex" }}>
					<div style={{ width: isScreenSmall ? "100%" : "40%" }}>
						<HeadingTextH2
							style={{
								fontSize: "1.4rem",
								marginBottom: isScreenSmall ? "1rem" : "",
								textAlign: isScreenSmall ? "center" : "initial"
							}}
						>
							{translation.performancePage.secondSectionHeading}
						</HeadingTextH2>
					</div>
					<div style={{ width: isScreenSmall ? "25%" : "15%", display: isScreenSmall ? "inline-block" : "" }}>
						<SortBox
							onClick={() => {
								setActiveSort(1);
								handleSortClick("videoPlayCount", setSortOrder1);
							}}
						>
							<span>{translation.performancePage.playsHeading}</span>
							<InlineMetricIcon>
								<SVG src={(
									activeSort === 1 ? (
										sortOrder1 ? ArrowsUp : ArrowsDown
									) : ArrowsInactive
								)} width="0.7em" />
							</InlineMetricIcon>
						</SortBox>
					</div>
					<div style={{ width: isScreenSmall ? "25%" : "15%", display: isScreenSmall ? "inline-block" : "" }}>
						<SortBox style={{ cursor: "default" }}>
							<span>{translation.performancePage.clicksHeading}</span>
						</SortBox>
					</div>
					<div style={{ width: isScreenSmall ? "25%" : "15%", display: isScreenSmall ? "inline-block" : "" }}>
						<SortBox
							onClick={() => {
								setActiveSort(3);
								handleSortClick("videoPlayDurationSeconds", setSortOrder3);
							}}
						>
							<span>{translation.performancePage.playtimeHeading}</span>
							<InlineMetricIcon>
								<SVG src={(
									activeSort === 3 ? (
										sortOrder3 ? ArrowsUp : ArrowsDown
									) : ArrowsInactive
								)} width="0.7em" />
							</InlineMetricIcon>
						</SortBox>
					</div>
					<div style={{ width: isScreenSmall ? "25%" : "15%", display: isScreenSmall ? "inline-block" : "" }}>
						{accountData?.subscription.enableEngagementMetrics ? (
							<SortBox
								onClick={() => {
									setActiveSort(4);
									handleSortClick("videoScore", setSortOrder4);
								}}
								style={{ float: isScreenSmall ? "initial" : "right" }}
							>
								<span>{translation.performancePage.scoreHeading}</span>
								<InlineMetricIcon>
									<SVG src={(
										activeSort === 4 ? (
											sortOrder4 ? ArrowsUp : ArrowsDown
										) : ArrowsInactive
									)} width="0.7em" />
								</InlineMetricIcon>
							</SortBox>
						) : (
							<SortBox onClick={showPlansPage ? () => navigate("/plans-pricing") : undefined} style={{ float: isScreenSmall ? "initial" : "right", cursor: showPlansPage ? "" : "not-allowed" }}>
								<span style={{ opacity: 0.5 }}>{translation.performancePage.scoreHeading}</span>
								<InlineMetricIcon>
									<SVG src={OpenLockIcon} />
								</InlineMetricIcon>
							</SortBox>
						)}
					</div>
				</div>
				{refetch && refetchVideo ? (
					metrics.topVideos?.length ? (
						metrics.topVideos.map((video, index) => (
							<PageSubsection
								onClick={() => navigate("/edit-video/" + video._id)}
								style={{ padding: "1.5rem 2rem", cursor: "pointer" }}
								key={index}
							>
								<TopVideos>
									<Flex style={{ alignItems: "center", width: isScreenSmall ? "100%" : "40%" }}>
										<HeadingTextH2 style={{ fontSize: "1.4rem" }}>{index + 1}</HeadingTextH2>
										<div style={{ width: "80px" }}>
											<CoverImageBox landscape={video.videoDisplayMode === DisplayFormatOptions.LANDSCAPE}>
												<UploadImage src={video.videoPosterURL} />
											</CoverImageBox>
										</div>
										<HeadingTextH2 style={{ fontSize: "1.2em" }}>{video.title}</HeadingTextH2>
									</Flex>
									{!isScreenSmall ? (
										<>
											<Flex style={{ width: "15%", flexDirection: "row", alignItems: "center" }}>
												{video.plays && (
													<>
														<InlineMetricIcon>
															<SVG src={Plays} useFastLoading/>
														</ InlineMetricIcon>
														&nbsp; {video.plays?.toLocaleString()}
													</>
												)}
											</Flex>
											<Flex style={{ width: "15%", flexDirection: "row", alignItems: "center" }}>
												{video.clicks && (
													<>
														<InlineMetricIcon>
															<SVG src={Clicks} useFastLoading/>
														</ InlineMetricIcon>
														&nbsp; {video.clicks?.toLocaleString()}
													</>
												)}
											</Flex>
											<div style={{ width: "15%" }}>{video.playtime && secondsToFormattedTime(video.playtime)}</div>
											{accountData?.subscription.enableEngagementMetrics && video.score !== null ? (
												<div style={{ width: "15%" }}>
													<ScoreCircle style={{ width: "30%", float: "right" }} score={video.score ?? 0}>
														{video.score}
													</ScoreCircle>
												</div>
											) : (
												<div style={{ width: "15%" }}>&nbsp;</div>
											)}
										</>
									) : (
										<>
											&nbsp;
											<Flex style={{ width: "100%", justifyContent: "space-between" }}>
												<Flex style={{ flexDirection: "row", alignItems: "center" }}>
													{video.plays && (
														<>
															<InlineMetricIcon>
																<SVG src={Plays} />
															</ InlineMetricIcon>
															&nbsp; {video.plays?.toLocaleString()}
														</>
													)}
												</Flex>
												<Flex style={{ flexDirection: "row", alignItems: "center" }}>
													{video.clicks && (
														<>
															<InlineMetricIcon>
																<SVG src={Clicks} />
															</ InlineMetricIcon>
															&nbsp; {video.clicks?.toLocaleString()}
														</>
													)}
												</Flex>
												<div>{video.playtime && secondsToFormattedTime(video.playtime)}</div>
											</Flex>
											&nbsp;
											{accountData?.subscription.enableEngagementMetrics && video.score !== null && (
												<Scorebar score={video.score ?? 0} />
											)}
										</>
									)}
								</TopVideos>
							</PageSubsection>
						))
					) : (
						<PageSectionBlock style={{ marginTop: "1rem", textAlign: "center" }}>
							<HeadingTextH2 style={{ fontSize: "1.4rem" }}>{translation.dashboardPage.noVideoPerformance}</HeadingTextH2>
							<RegularText>{translation.dashboardPage.addVideo}</RegularText>
							<MainButton
								className="mt-2 mb-2"
								type="button"
								onClick={() => {
									if (hideCreateVideo) {
										setVideoModalStatus(true);
									} else {
										registerEvent({
											eventName: EventNameEnum.CREATE_VIDEO_PRESS
										});
										navigate("/create-video");
									}
								}}
							>
								{translation.dashboardPage.createVideo}
							</MainButton>
						</PageSectionBlock>
					)
				) : (
					<div style={{ marginTop: "3rem" }}>
						<Skeleton className="mt-2" count={8} height={50} />
					</div>
				)}
			</PageSection>

			{/* Top Performing Collections */}

			{showPlansPage &&
				(accountData?.subscription.enableConversionMetrics ? (
					shopifyStoreConnected ? (
						<PageSection>
							<HeadingTextH2 style={{ fontSize: "1.4rem", textAlign: isScreenSmall ? "center" : "initial" }}>
								{translation.performancePage.topCollectionsHeading}
							</HeadingTextH2>

							{refetch ? (
								metrics.topCollections?.map((collection, index) => (
									<PageSubsection
										onClick={() => navigate("/edit-collection/" + collection._id)}
										style={{ marginLeft: 0, padding: "1.5rem 2rem" }}
										key={index}
									>
										<TopVideos>
											<Flex style={{ alignItems: "center", width: isScreenSmall ? "100%" : "50%" }}>
												<HeadingTextH2 style={{ fontSize: "1.4rem", marginRight: "2rem" }}>{index + 1}</HeadingTextH2>
												<HeadingTextH2 style={{ fontSize: "1.2em" }}>{collection.title}</HeadingTextH2>
											</Flex>
											{!isScreenSmall ? (
												<>
													<Flex style={{ width: "50%", justifyContent: "flex-end" }}>
														{collection.orderCount
															? collection.orderCount === 1
																? collection.orderCount?.toLocaleString() +
																	" " +
																	translation.performancePage.orderTitleLabel
																: collection.orderCount?.toLocaleString() +
																	" " +
																	translation.performancePage.ordersTitleLabel
															: ""}
													</Flex>
												</>
											) : (
												<>
													<Flex style={{ width: "100%", justifyContent: "space-between" }}>
														<Flex>
															{collection.orderCount
																? collection.orderCount === 1
																	? collection.orderCount?.toLocaleString() +
																		" " +
																		translation.performancePage.orderTitleLabel
																	: collection.orderCount?.toLocaleString() +
																		" " +
																		translation.performancePage.ordersTitleLabel
																: ""}
														</Flex>
													</Flex>
												</>
											)}
										</TopVideos>
									</PageSubsection>
								))
							) : (
								<div style={{ marginTop: "3rem" }}>
									<Skeleton className="mt-2" count={8} height={50} />
								</div>
							)}
						</PageSection>
					) : (
						<PageSection>
							<HeadingTextH2 style={{ fontSize: "1.4rem", textAlign: isScreenSmall ? "center" : "initial" }}>
								{translation.performancePage.topCollectionsHeading}
							</HeadingTextH2>
							<PageSectionBlock style={{ marginTop: "1rem", textAlign: "center" }}>
								<HeadingTextH2 style={{ fontSize: "1.4rem" }}>{translation.dashboardPage.noConversion}</HeadingTextH2>
								<RegularText style={{ textAlign: "center" }}>
									{translation.dashboardPage.ensurEscriptSdded} <br />
									{translation.dashboardPage.oneConversion}
								</RegularText>
								<MainButton
									onClick={() => {
										window.scrollTo(0, 0);
										navigate("/account-settings");
									}}
								>
									{translation.general.accountSettings}
								</MainButton>
							</PageSectionBlock>
						</PageSection>
					)
				) : (
					<PageSection>
						<HeadingTextH2 style={{ fontSize: "1.4rem", textAlign: isScreenSmall ? "center" : "initial" }}>
							{translation.performancePage.topCollectionsHeading}
						</HeadingTextH2>
						<PageSectionBlock style={{ marginTop: "1rem", textAlign: "center" }}>
							<HeadingTextH2 style={{ fontSize: "1.4rem" }}>{translation.dashboardPage.upgradeToPro}</HeadingTextH2>
							<RegularText>{translation.dashboardPage.onlyShopify}</RegularText>

							<MainButton
								className="mt-2 mb-2"
								type="button"
								onClick={() => navigate("/plans-pricing")}
							>
								<StarIcon /> &nbsp;
								{trialAvailable ? translation.modals.apProSelectPro : translation.general.unlockProPlan}
							</MainButton>
						</PageSectionBlock>
					</PageSection>
				))
			}
		</>
	);
};

export default PerformanceMetrics;
