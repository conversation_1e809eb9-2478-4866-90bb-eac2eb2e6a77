import "@testing-library/jest-dom";
import React from "react";
import { render, fireEvent } from "@testing-library/react";
import TeleprompterComponent from "../TeleprompterComponent";
import { ThemeProvider } from "styled-components";
import apTheme from "@src/config/theme";

const renderWithTheme = (ui: React.ReactElement) =>
	render(<ThemeProvider theme={apTheme}>{ui}</ThemeProvider>);

describe("TeleprompterComponent", () => {
	it("should update teleprompter text on input", () => {
		const setTeleprompterText = jest.fn();
		const setVideoFontSize = jest.fn();
		const setScriptSpeed = jest.fn();
		const setDisplayTeleprompter = jest.fn();
		const { getByPlaceholderText } = renderWithTheme(
			<TeleprompterComponent
				teleprompterText="hello"
				setTeleprompterText={setTeleprompterText}
				videoFontSize={18}
				setVideoFontSize={setVideoFontSize}
				scriptSpeed={2}
				setScriptSpeed={setScriptSpeed}
				displayTeleprompter={false}
				setDisplayTeleprompter={setDisplayTeleprompter}
				disabled={false}
			/>
		);
		const textarea = getByPlaceholderText(/Enter script here/i);
		fireEvent.change(textarea, { target: { value: "new text" } });
		expect(setTeleprompterText).toHaveBeenCalledWith("new text");
	});

	it("should call setVideoFontSize and setScriptSpeed on slider change", () => {
		const setTeleprompterText = jest.fn();
		const setVideoFontSize = jest.fn();
		const setScriptSpeed = jest.fn();
		const setDisplayTeleprompter = jest.fn();
		const { getAllByRole } = renderWithTheme(
			<TeleprompterComponent
				teleprompterText=""
				setTeleprompterText={setTeleprompterText}
				videoFontSize={18}
				setVideoFontSize={setVideoFontSize}
				scriptSpeed={2}
				setScriptSpeed={setScriptSpeed}
				displayTeleprompter={false}
				setDisplayTeleprompter={setDisplayTeleprompter}
				disabled={false}
			/>
		);
		const sliders = getAllByRole("slider");
		fireEvent.change(sliders[0], { target: { value: "20" } });
		expect(setVideoFontSize).toHaveBeenCalledWith(20);
		fireEvent.change(sliders[1], { target: { value: "5" } });
		expect(setScriptSpeed).toHaveBeenCalledWith(5);
	});
});
