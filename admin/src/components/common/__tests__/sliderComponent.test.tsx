import "@testing-library/jest-dom";
import React from "react";
import { render, fireEvent } from "@testing-library/react";
import SliderComponent from "../SliderComponent";
import { ThemeProvider } from "styled-components";
import apTheme from "@src/config/theme";

const renderWithTheme = (ui: React.ReactElement) =>
	render(<ThemeProvider theme={apTheme}>{ui}</ThemeProvider>);
const noop = jest.fn();

describe("SliderComponent", () => {
	it("renders with correct title and value", () => {
		const { getByText, getByRole } = renderWithTheme(
			<SliderComponent
				title="Font Size"
				minRange={10}
				maxRange={22}
				label="Size"
				units="px"
				value={18}
				onChange={noop}
			/>
		);
		expect(getByText("Font Size")).toBeInTheDocument();
		expect(getByRole("slider")).toHaveValue("18");
	});

	it("calls onChange when slider value changes", () => {
		const handleChange = jest.fn();
		const { getByRole } = renderWithTheme(
			<SliderComponent
				title="Script Speed"
				minRange={1}
				maxRange={8}
				label="Speed"
				units="x"
				value={2}
				onChange={handleChange}
			/>
		);
		const slider = getByRole("slider");
		fireEvent.change(slider, { target: { value: "4" } });
		expect(handleChange).toHaveBeenCalledWith(4);
	});
});
