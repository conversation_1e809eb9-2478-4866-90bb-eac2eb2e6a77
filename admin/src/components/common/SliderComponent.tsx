import React from "react";
import { Form, Row } from "react-bootstrap";
import { SliderContainer } from "@src/styles/components";
import { SliderProps } from "@src/types/slider";

const SliderComponent: React.FC<SliderProps> = ({ title, minRange, maxRange, label, units, value, onChange }) => {
	return (
		<>
			<Row className="mb-2">
				<b>{title}</b>
			</Row>
			<SliderContainer>
				<>
					<Form.Range
						value={value}
						onChange={e => onChange(Number(e.target.value))}
						min={minRange}
						max={maxRange}
						step="1"
					/>
				</>
				<>
					{label + ": " + value + units}
				</>
			</SliderContainer>
		</>
	);
};

export default SliderComponent;
