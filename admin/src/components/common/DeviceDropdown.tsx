import React from "react";
import { SmallSelect } from "@src/styles/components";

interface Props {
	id: string;
	value?: string;
	setValue: (val: string | undefined) => void;
	devices: MediaDeviceInfo[];
	placeholder?: string;
	onClick?: () => void;
	disabled?: boolean;
}

const DeviceDropdown: React.FC<Props> = ({ id, value, setValue, devices, placeholder = "-", onClick, disabled = false }) => {

	const setFieldValue = (val: string) => {
		let valueToSet: string | undefined = val;
		if (val === placeholder) {
			valueToSet = undefined;
		}
		setValue(valueToSet);
	};

	return (
		<SmallSelect
			id={id}
			value={(devices.length <= 0) ? placeholder : (value ?? placeholder)}
			onChange={e => setFieldValue(e.target.value)}
			onClick={onClick}
			disabled={disabled}
		>
			<option value={placeholder} disabled hidden>
				{placeholder}
			</option>
			{devices.map((d) => (
				<option key={d.deviceId} value={d.deviceId}>{d.label}</option>
			))}
		</SmallSelect>
	);
};

export default DeviceDropdown;
