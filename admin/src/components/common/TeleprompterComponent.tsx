import React from "react";
import { Col, Row } from "react-bootstrap";
import {
	TeleprompterBox,
	CustomSwitch,
	TeleprompterTextArea,
	FlexSpace
} from "@src/styles/components";
import { useTranslation } from "../hooks/translations";
import { TeleprompterProps } from "@src/types/teleprompter";
import SliderComponent from "./SliderComponent";


const TeleprompterComponent: React.FC<TeleprompterProps> = ({
	teleprompterText,
	setTeleprompterText,
	videoFontSize,
	setVideoFontSize,
	scriptSpeed,
	setScriptSpeed,
	displayTeleprompter,
	setDisplayTeleprompter,
	disabled
}) => {
	const translation = useTranslation();

	return (
		<>
			<Row pl="0" pr="0">
				<Col>
					<FlexSpace className="mb-2">
						<b>{translation.teleprompterComponent.title}</b>
						<CustomSwitch
							type="switch"
							defaultChecked={displayTeleprompter}
							onChange={(e: React.ChangeEvent<HTMLInputElement>) => setDisplayTeleprompter(e.target.checked)}
							label={translation.teleprompterComponent.switchLabel}
							id="displayTitleSwitch"
						/>
					</FlexSpace>
					<TeleprompterBox>
						<TeleprompterTextArea
							id="TeleprompterTextArea"
							value={teleprompterText}
							onChange={(e) => setTeleprompterText(e.target.value)}
							placeholder={translation.teleprompterComponent.placeholder}
							rows={6}
							disabled={disabled}
						/>
					</TeleprompterBox>
				</Col>
			</Row>
			<Row pl="0" pr="0">
				<Col sm="12" md="6" lg="6" xl="6" className="mt-3">
					<SliderComponent
						title={translation.general.fontSize}
						minRange={10}
						maxRange={22}
						label={translation.general.size}
						units={translation.general.px}
						value={videoFontSize}
						onChange={setVideoFontSize}
					/>
				</Col>
				<Col sm="12" md="6" lg="6" xl="6" className="mt-3">
					<SliderComponent
						title={translation.general.scriptSpeed}
						minRange={1}
						maxRange={8}
						label={translation.general.speed}
						units={translation.general.x}
						value={scriptSpeed}
						onChange={setScriptSpeed}
					/>
				</Col>
			</Row>
		</>
	);
};

export default TeleprompterComponent;
