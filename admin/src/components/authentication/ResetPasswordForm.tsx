import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Form } from "react-bootstrap";
import axios from "axios";
import { useTranslation } from "../hooks/translations";
import { getErrorString } from "../utils/getErrorString";
import { MainButton, LinkButton } from "@src/styles/components";
import { FormBox, HeadingText, BodyText, CustomInput } from "@src/styles/forms";
import { AxiosError } from "@src/types/axios";
import ErrorMessage from "@src/components/utils/ErrorMessage";

const ResetPasswordForm: React.FC = () => {
	const navigate = useNavigate();
	const translation = useTranslation();
	const [validated, setValidated] = useState(false);
	const [formError, setFormError] = useState("");
	const [emailSent, setEmailSent] = useState(false);
	const [loading, setLoading] = useState(false);

	const submitForm = async (event: React.FormEvent<HTMLFormElement>) => {
		setLoading(true);
		event.preventDefault();
		const form = event.currentTarget;
		if (form.checkValidity() === false) {
			event.stopPropagation();
			setValidated(true);
			setLoading(false);
		} else {
			try {
				const API_ENDPOINT = process.env.API_ENDPOINT;
				const CMS_ENDPOINT = process.env.CMS_ENDPOINT;
				const API_VERSION = process.env.API_VERSION;
				const email = (form.elements.namedItem("formEmail") as HTMLInputElement)?.value;

				const requestObject = {
					method: "email/password",
					email: email,
					locale: translation.locale,
					callbackEndpoint: CMS_ENDPOINT
				};

				await axios.request({
					url: `${API_ENDPOINT}/api/auth/forgot-password`,
					method: "POST",
					data: JSON.stringify(requestObject),
					headers: {
						"content-type": "application/json",
						"x-api-version": API_VERSION
					}
				});

				setEmailSent(true);
				setLoading(false);
			} catch (error: unknown) {
				setValidated(false);
				setLoading(false);
				if ((error as AxiosError).response) {
					const axiosError = error as AxiosError;
					setFormError(getErrorString(translation, axiosError?.response?.data?.error));
				}
			}
		}
	};

	return (
		<FormBox>
			{!emailSent ? (
				<>
					<HeadingText className="mb-3" data-testid="passwordResetText">
						{translation.passwordResetPage.resetPassword}
					</HeadingText>
					<BodyText data-testid="passwordResetSubText">{translation.passwordResetPage.subText}</BodyText>
					{!!formError && <ErrorMessage error={formError} setError={setFormError} displayCloseIcon={true} />}

					<Form className="mb-4 mt-4" noValidate onSubmit={submitForm} validated={validated} data-testid="passwordResetSection">
						<Form.Group className="mb-3">
							<CustomInput
								className="form-control"
								type="email"
								required
								autoFocus
								pattern="[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,20}$"
								placeholder={translation.general.email}
								onInput={(e: React.ChangeEvent<HTMLInputElement>) => (e.target.value = ("" + e.target.value).toLowerCase())}
								autoComplete="username"
								id="formEmail"
								data-testid="formEmail"
							/>
						</Form.Group>
						<MainButton type="submit" className="mt-3" data-testid="passwordResetSubmit" disabled={loading}>
							{translation.passwordResetPage.resetPassword}
						</MainButton>
					</Form>

					<BodyText data-testid="passwordResetQuestion">
						{translation.passwordResetPage.question}&nbsp;
						<LinkButton onClick={() => navigate("/sign-in/password")} data-testid="signInLink">
							{translation.general.signIn}.
						</LinkButton>
					</BodyText>
				</>
			) : (
				<>
					<HeadingText className="mb-3" data-testid="checkInboxText">
						{translation.passwordResetPage.checkInbox}
					</HeadingText>
					<BodyText data-testid="checkInboxSubText">{translation.passwordResetPage.checkInboxSubText}</BodyText>
					<MainButton type="button" data-testid="signInButton" className="mt-3" onClick={() => navigate("/sign-in/password")}>
						{translation.passwordResetPage.backToSignIn}
					</MainButton>
				</>
			)}
		</FormBox>
	);
};

export default ResetPasswordForm;
