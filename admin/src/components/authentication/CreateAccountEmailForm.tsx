import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Form } from "react-bootstrap";
import axios from "axios";
import { useTranslation, FormatString } from "../hooks/translations";
import { getErrorString } from "../utils/getErrorString";
import { MainButton, LinkButton } from "@src/styles/components";
import { FormBox, HeadingText, BodyText, CustomInput, CustomLabel } from "@src/styles/forms";
import { EventNameEnum } from "@src/types/events";
import { registerEvent } from "@src/components/events/service";
import { AxiosError } from "@src/types/axios";
import ErrorMessage from "@src/components/utils/ErrorMessage";

const CreateAccountEmailForm: React.FC<{registrationCompleted: boolean; setRegistrationCompleted: React.Dispatch<React.SetStateAction<boolean>>}> = ({ registrationCompleted, setRegistrationCompleted }) => {
	const navigate = useNavigate();
	const translation = useTranslation();
	const [loginError, setLoginError] = useState("");
	const [validated, setValidated] = useState(false);
	const [loading, setLoading] = useState(false);

	const tosLink = "<a href='" + translation.general.tosLink + "' data-testid='signupTOS' target='_blank' rel='noreferrer'>";
	const privacyLink = "<a href='" + translation.general.privacyPolicyLink + "' data-testid='signupPrivacy' target='_blank' rel='noreferrer'>";

	interface SignUpRequest {
		email: string;
		locale: string;
		callbackEndpoint: string | undefined;
	}

	const submitForm = async (event: React.FormEvent<HTMLFormElement>) => {
		setLoading(true);
		event.preventDefault();
		const form = event.currentTarget;
		if (form.checkValidity() === false) {
			event.stopPropagation();
			setValidated(true);
			setLoading(false);
		} else {
			try {
				const API_ENDPOINT = process.env.API_ENDPOINT;
				const CMS_ENDPOINT = process.env.CMS_ENDPOINT;
				const API_VERSION = process.env.API_VERSION;

				const email = (form.elements.namedItem("formEmail") as HTMLInputElement).value;

				const requestObject: SignUpRequest = {
					email: email,
					locale: translation.locale,
					callbackEndpoint: CMS_ENDPOINT
				};

				const returnObject = await axios.request({
					url: `${API_ENDPOINT}/api/auth/sign-up/email`,
					method: "POST",
					data: JSON.stringify(requestObject),
					headers: {
						"content-type": "application/json",
						"x-api-version": API_VERSION
					}
				});

				if (returnObject?.status === 201) {
					setRegistrationCompleted(true);
				}

				registerEvent({
					eventName: EventNameEnum.CREATE_ACCOUNT_PRESS
				});

				setLoading(false);
			} catch (error: unknown) {
				setValidated(false);
				setLoading(false);
				if ((error as AxiosError).response) {
					const axiosError = error as AxiosError;
					setLoginError(getErrorString(translation, axiosError?.response?.data?.error));
				}
			}
		}
	};

	return (
		<FormBox>
			{!registrationCompleted ? (
				<>
					<HeadingText className="mb-3" data-testid="createAccountText">
						{translation.general.signUpForFree}
					</HeadingText>
					<BodyText data-testid="createAccountSubText">{translation.createAccountPage.subText}</BodyText>
					{!!loginError && <ErrorMessage error={loginError} setError={setLoginError} displayCloseIcon={true} />}
					<Form className="mb-4 mt-4" noValidate onSubmit={submitForm} validated={validated} data-testid="CreateAccountSection">
						<Form.Group className="mb-3">
							<CustomInput
								type="email"
								required
								pattern="[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,20}$"
								className="form-control"
								placeholder={translation.createAccountPage.email}
								onInput={(e: React.ChangeEvent<HTMLInputElement>) => (e.target.value = ("" + e.target.value).toLowerCase())}
								autoComplete="username"
								id="formEmail"
								data-testid="formEmail"
							/>
							<Form.Control.Feedback type="invalid" data-testid="emailTextError">
								{translation.errors.emailFormat}
							</Form.Control.Feedback>
						</Form.Group>
						<MainButton type="submit" className="mt-3" data-testid="createAccountSubmit" disabled={loading}>
							{translation.general.createAccount}
						</MainButton>
					</Form>

					<BodyText data-testid="createAccountQuestion">
						{translation.createAccountPage.question}&nbsp;
						<LinkButton onClick={() => navigate("/sign-in/email")} data-testid="signInLink">
							{translation.general.signIn}.
						</LinkButton>
					</BodyText>
					<CustomLabel data-testid="formLegalText" id="formLegalText" htmlFor="formLegalBox" dangerouslySetInnerHTML={{ __html: FormatString(translation.createAccountPage.legalCheckbox, tosLink, "</a>", privacyLink, "</a>") }} />
				</>
			) : (
				<>
					<HeadingText className="mb-3" data-testid="checkInboxText">
						{translation.createAccountPage.emailConfirmedTitle}
					</HeadingText>
					<BodyText data-testid="checkInboxSubText">{translation.createAccountPage.verifyEmailSignUpCopy}</BodyText>
				</>
			)}
		</FormBox>
	);
};

export default CreateAccountEmailForm;
