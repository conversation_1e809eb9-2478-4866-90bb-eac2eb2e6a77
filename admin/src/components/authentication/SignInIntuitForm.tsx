import React, { useState, useEffect, useCallback } from "react";
import axios from "axios";
import { useTranslation } from "../hooks/translations";
import { getErrorString } from "../utils/getErrorString";
import { useSetRecoilState } from "recoil";
import { isLoggedInState } from "./state";
import { MainButton, LogoImageBox, CompanyLogo } from "@src/styles/components";
import { FormBox, HeadingText, BodyText } from "@src/styles/forms";
import { AxiosError } from "@src/types/axios";
import ErrorMessage from "@src/components/utils/ErrorMessage";
import { IntuitLogo } from "@src/assets";
import { removeParamFromURL } from "@src/utils/strings";

const SignInIntuitForm: React.FC = () => {
	const translation = useTranslation();
	const [loginError, setLoginError] = useState("");
	const [loading, setLoading] = useState(false);
	const setIsLoggedIn = useSetRecoilState(isLoggedInState);

	const handleLogin = async () => {
		setLoading(true);
		sessionStorage.removeItem("token");
		const state = window.crypto.randomUUID();
		localStorage.setItem("authState", state);

		const clientId = process.env.OIDC_INTUIT_CLIENT_ID;
		const redirectUri = process.env.OIDC_INTUIT_REDIRECT_URI;
		const authEndpoint = process.env.OIDC_INTUIT_AUTH_URL;
		const prompt = process.env.OIDC_INTUIT_PROMPT;

		const responseType = "code";
		const scope = "openid email profile";
		const maxAge = 3600;
		const acrValues = "";
		const accessType = "offline";
		const nonce = window.crypto.randomUUID();

		const authURL =
			`${authEndpoint}` +
			`?client_id=${clientId}` +
			`&redirect_uri=${redirectUri}` +
			`&response_type=${responseType}` +
			`&prompt=${prompt}` +
			`&scope=${scope}` +
			`&max_age=${maxAge}` +
			`&acr_values=${acrValues}` +
			`&state=${state}` +
			`&nonce=${nonce}` +
			`&access_type=${accessType}`;
		window.location.href = authURL;
	};

	const verifyAuthCode = useCallback(
		async (code: string, inviteToken: string | null) => {
			try {
				const API_ENDPOINT = process.env.API_ENDPOINT;
				const API_VERSION = process.env.API_VERSION;
				const OIDC_INTUIT_CLIENT_ID = process.env.OIDC_INTUIT_CLIENT_ID;

				const requestOidcObject = new FormData();
				requestOidcObject.append("oidcCode", code);
				requestOidcObject.append("oidcClientId", `${OIDC_INTUIT_CLIENT_ID}`);

				const returnOidcObject = await axios.request({
					url: `${API_ENDPOINT}/api/oauth/oidc/token`,
					method: "POST",
					data: requestOidcObject,
					headers: {
						"content-type": "multipart/form-data",
						"x-api-version": API_VERSION
					}
				});

				if (
					returnOidcObject?.data?.oidcAccessToken &&
					returnOidcObject?.data?.oidcIdToken &&
					returnOidcObject?.data?.oidcRefreshToken
				) {
					const requestObject = new FormData();
					requestObject.append("oidcIdToken", returnOidcObject?.data?.oidcIdToken);
					requestObject.append("oidcAccessToken", returnOidcObject?.data?.oidcAccessToken);
					requestObject.append("oidcClientId", `${OIDC_INTUIT_CLIENT_ID}`);

					if (inviteToken) {
						requestObject.append("inviteToken", inviteToken);
					}

					const returnObject = await axios.request({
						url: `${API_ENDPOINT}/api/oauth/oidc`,
						method: "POST",
						data: requestObject,
						headers: {
							"content-type": "multipart/form-data",
							"x-api-version": API_VERSION
						}
					});

					if (returnObject?.data?.accessToken) {
						const returnSelfObject = await axios.request({
							url: `${API_ENDPOINT}/api/accounts/self`,
							method: "GET",
							headers: {
								"Authorization": "Bearer " + returnObject.data.accessToken,
								"content-type": "application/json",
								"x-api-version": API_VERSION
							}
						});
						if (returnSelfObject?.data?.accounts.length === 1) {
							const bodyFormData = new FormData();
							bodyFormData.append("accountId", returnSelfObject?.data?.accounts[0]._id);

							const returnToken = await axios.request({
								url: `${API_ENDPOINT}/api/accounts/token`,
								method: "POST",
								data: bodyFormData,
								headers: {
									"Authorization": "Bearer " + returnObject.data.accessToken,
									"content-type": "multipart/form-data",
									"x-api-version": API_VERSION
								}
							});

							sessionStorage.setItem("token", returnToken?.data?.token);
						}

						localStorage.setItem("accessToken", returnObject.data.accessToken);
						localStorage.setItem("refreshToken", returnOidcObject?.data?.oidcRefreshToken);
						localStorage.setItem("oidcClientId", `${OIDC_INTUIT_CLIENT_ID}`);
						setIsLoggedIn(true);

						return "";
					}

					throw new Error("Failed to read accessToken from response object");
				} else {
					throw new Error("Failed to read oidcAccessToken or oidcIdToken or oidcRefreshToken from response object");
				}
			} catch (error: unknown) {
				localStorage.removeItem("accessToken");
				localStorage.removeItem("refreshToken");
				localStorage.removeItem("oidcClientId");
				localStorage.removeItem("authState");
				sessionStorage.removeItem("token");
				console.error(error);
				let errorMessage = "";

				if ((error as AxiosError).response) {
					const axiosError = error as AxiosError;
					errorMessage = axiosError?.response?.data?.error ?? "";
				}

				return getErrorString(translation, errorMessage);
			}
		},
		[setIsLoggedIn, translation]
	);

	useEffect(() => {
		(async () => {
			const queryParams = new URLSearchParams(location.search);
			const code = queryParams.get("code");
			const state = queryParams.get("state");
			const inviteToken = queryParams.get("invite");
			if (inviteToken) {
				localStorage.setItem("inviteToken", inviteToken);
			}

			const authState = localStorage.getItem("authState");

			if (code && state) {
				removeParamFromURL("code");
				removeParamFromURL("state");

				const inviteTokenValue = localStorage.getItem("inviteToken");
				localStorage.removeItem("authState");
				localStorage.removeItem("inviteToken");

				if (state !== authState) {
					setLoginError(translation.errors.tokenError);
				} else {
					setLoading(true);
					const returnValue = await verifyAuthCode(code, inviteTokenValue);
					setLoginError(returnValue);
					setLoading(false);
				}
			}
		})();
	}, [setLoginError, translation.errors.tokenError, verifyAuthCode]);

	return (
		<FormBox>
			{
				<>
					<LogoImageBox data-testid="LogoBox" className="mb-4">
						<CompanyLogo src={IntuitLogo} />
					</LogoImageBox>
					<HeadingText className="mb-3" data-testid="signInText">
						{translation.general.signIn}
					</HeadingText>
					{!!loginError && (
						<>
							<ErrorMessage error={loginError} setError={setLoginError} displayCloseIcon={true} />
							<br />
						</>
					)}
					<BodyText data-testid="signInSubText">{translation.signInPage.intuitText}</BodyText>

					<MainButton
						onClick={handleLogin}
						className="mt-3"
						data-testid="signInSubmit"
						disabled={loading}
					>
						{translation.general.signInIntuit}
					</MainButton>
				</>
			}
		</FormBox>
	);
};

export default SignInIntuitForm;
