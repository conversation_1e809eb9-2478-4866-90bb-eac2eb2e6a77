import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Form } from "react-bootstrap";
import axios from "axios";
import { useTranslation, FormatString } from "../hooks/translations";
import { getErrorString } from "../utils/getErrorString";
import { MainButton, LinkButton } from "@src/styles/components";
import { FormBox, HeadingText, BodyText, CustomInput, CustomLabel, EyeIcon, EyeSlashIcon } from "@src/styles/forms";
import { EventNameEnum } from "@src/types/events";
import { registerEvent } from "@src/components/events/service";
import { useRecoilState } from "recoil";
import { isLoggedInState } from "./state";
import jwt_decode from "jwt-decode";
import { InviteToken } from "@src/types/invitations";
import { AxiosError } from "@src/types/axios";
import ErrorMessage from "@src/components/utils/ErrorMessage";

const CreateAccountForm: React.FC<{registrationCompleted: boolean; setRegistrationCompleted: React.Dispatch<React.SetStateAction<boolean>>}> = ({ registrationCompleted, setRegistrationCompleted }) => {
	const navigate = useNavigate();
	const translation = useTranslation();
	const [loginError, setLoginError] = useState("");
	const [validated, setValidated] = useState(false);
	const [showPassword, setShowPassword] = useState(false);
	const [formPassword, setFormPassword] = useState("");
	const [loading, setLoading] = useState(false);
	const [inviteToken, setInviteToken] = useState("");
	const [isLoggedIn, setIsLoggedIn] = useRecoilState(isLoggedInState);
	const [formEmail, setFormEmail] = useState("");
	const publicSignUp = process.env.PUBLIC_SIGN_UP;

	const tosLink = "<a href='" + translation.general.tosLink + "' data-testid='signupTOS' target='_blank' rel='noreferrer'>";
	const privacyLink = "<a href='" + translation.general.privacyPolicyLink + "' data-testid='signupPrivacy' target='_blank' rel='noreferrer'>";

	interface SignUpRequest {
		email: string;
		password: string;
		locale: string;
		legalAgreement: boolean;
		callbackEndpoint: string | undefined;
		inviteToken?: string;
	}

	useEffect(() => {
		const url = new URL(window.location.href);
		const token = url.searchParams.get("invite");

		if (token) {
			setInviteToken(token);
			const decoded = jwt_decode(token) as InviteToken;
			if (decoded.userExists) {
				if (isLoggedIn) navigate("/company");
				else {
					localStorage.setItem("goToCompany", "true");
					navigate("/sign-in/password");
				}
			}
			if (decoded.email) {
				setFormEmail(decoded.email);
			}
		} else {
			if (publicSignUp !== "true") {
				navigate("/sign-in/password");
			}
		}
	}, [publicSignUp, isLoggedIn, navigate]);

	const submitForm = async (event: React.FormEvent<HTMLFormElement>) => {
		setLoading(true);
		event.preventDefault();
		const form = event.currentTarget;
		if (form.checkValidity() === false) {
			event.stopPropagation();
			setValidated(true);
			setLoading(false);
		} else {
			try {
				const API_ENDPOINT = process.env.API_ENDPOINT;
				const CMS_ENDPOINT = process.env.CMS_ENDPOINT;
				const API_VERSION = process.env.API_VERSION;

				const email = (form.elements.namedItem("formEmail") as HTMLInputElement).value;
				const password = (form.elements.namedItem("formPassword") as HTMLInputElement).value;

				const requestObject: SignUpRequest = {
					email: email,
					password: password,
					locale: translation.locale,
					legalAgreement: true,
					callbackEndpoint: CMS_ENDPOINT
				};

				if (inviteToken) {
					requestObject.inviteToken = inviteToken;
				}

				const returnObject = await axios.request({
					url: `${API_ENDPOINT}/api/auth/signup`,
					method: "POST",
					data: JSON.stringify(requestObject),
					headers: {
						"content-type": "application/json",
						"x-api-version": API_VERSION
					}
				});

				if (returnObject?.data?.accessToken && returnObject?.data?.refreshToken) {
					localStorage.setItem("accessToken", returnObject.data.accessToken);
					localStorage.setItem("refreshToken", returnObject.data.refreshToken);
					localStorage.setItem("postSignup", "true");
					setIsLoggedIn(true);
				}

				registerEvent({
					eventName: EventNameEnum.CREATE_ACCOUNT_PRESS
				});

				if (!isLoggedIn) setRegistrationCompleted(true);
				setLoading(false);
			} catch (error: unknown) {
				setValidated(false);
				setLoading(false);
				setFormPassword("");
				if ((error as AxiosError).response) {
					const axiosError = error as AxiosError;
					setLoginError(getErrorString(translation, axiosError?.response?.data?.error));
				}
			}
		}
	};

	return (
		<FormBox>
			{!registrationCompleted ? (
				<>
					<HeadingText className="mb-3" data-testid="createAccountText">
						{translation.general.signUpForFree}
					</HeadingText>
					<BodyText data-testid="createAccountSubText">{translation.createAccountPage.subText}</BodyText>
					{!!loginError && <ErrorMessage error={loginError} setError={setLoginError} displayCloseIcon={true} />}
					<Form className="mb-4 mt-4" noValidate onSubmit={submitForm} validated={validated} data-testid="CreateAccountSection">
						<Form.Group className="mb-3">
							<CustomInput
								type="email"
								required
								pattern="[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,20}$"
								className="form-control"
								placeholder={translation.createAccountPage.businessEmail}
								onInput={(e: React.ChangeEvent<HTMLInputElement>) => (e.target.value = ("" + e.target.value).toLowerCase())}
								autoComplete="username"
								id="formEmail"
								data-testid="formEmail"
								defaultValue={formEmail}
								disabled={!!inviteToken}
							/>
							<Form.Control.Feedback type="invalid" data-testid="emailTextError">
								{translation.errors.emailFormat}
							</Form.Control.Feedback>
						</Form.Group>
						<Form.Group className="mb-3">
							<CustomInput
								type={showPassword ? "text" : "password"}
								required
								pattern="^.{8,}$"
								className="form-control"
								style={{ paddingRight: "60px" }}
								placeholder={translation.general.password}
								onChange={(value) => setFormPassword(value.target.value)}
								value={formPassword}
								autoComplete="current-password"
								id="formPassword"
								data-testid="formPassword"
							/>
							{showPassword ? (
								<EyeSlashIcon
									data-testid="EyeSlashIcon"
									onClick={() => {
										setShowPassword(false);
									}}
								/>
							) : (
								<EyeIcon
									data-testid="EyeIcon"
									onClick={() => {
										setShowPassword(true);
									}}
								/>
							)}
							<Form.Control.Feedback type="invalid" data-testid="passwordTextError">
								{translation.errors.passwordFormat}
							</Form.Control.Feedback>
						</Form.Group>
						<MainButton type="submit" className="mt-3" data-testid="createAccountSubmit" disabled={loading}>
							{translation.general.createAccount}
						</MainButton>
					</Form>

					<BodyText data-testid="createAccountQuestion">
						{translation.createAccountPage.question}&nbsp;
						<LinkButton onClick={() => navigate("/sign-in/password")} data-testid="signInLink">
							{translation.general.signIn}.
						</LinkButton>
					</BodyText>
					<CustomLabel data-testid="formLegalText" id="formLegalText" htmlFor="formLegalBox" dangerouslySetInnerHTML={{ __html: FormatString(translation.createAccountPage.legalCheckbox, tosLink, "</a>", privacyLink, "</a>") }} />
				</>
			) : (
				<>
					<HeadingText className="mb-3" data-testid="checkInboxText">
						{translation.createAccountPage.verifyEmail}
					</HeadingText>
					<BodyText data-testid="checkInboxSubText">{translation.createAccountPage.checkInbox}</BodyText>
				</>
			)}
		</FormBox>
	);
};

export default CreateAccountForm;
