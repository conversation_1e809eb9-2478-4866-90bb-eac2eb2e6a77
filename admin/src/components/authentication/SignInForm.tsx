import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Form } from "react-bootstrap";
import axios from "axios";
import { useSetRecoilState } from "recoil";
import { isLoggedInState } from "./state";
import { useTranslation } from "../hooks/translations";
import { getErrorString } from "../utils/getErrorString";
import { MainButton, LinkButton } from "@src/styles/components";
import { FormBox, HeadingText, BodyText, CustomInput, EyeIcon, EyeSlashIcon } from "@src/styles/forms";
import { AxiosError } from "@src/types/axios";
import ErrorMessage from "@src/components/utils/ErrorMessage";

const SignInForm: React.FC = () => {
	const navigate = useNavigate();
	const translation = useTranslation();
	const [validated, setValidated] = useState(false);
	const [loginError, setLoginError] = useState("");
	const [formPassword, setFormPassword] = useState("");
	const setIsLoggedIn = useSetRecoilState(isLoggedInState);
	const [showPassword, setShowPassword] = useState(false);
	const [loading, setLoading] = useState(false);
	const publicSignUp = process.env.PUBLIC_SIGN_UP;

	const resendVerification = async (emailAddress: string) => {
		try {
			const API_ENDPOINT = process.env.API_ENDPOINT;
			const CMS_ENDPOINT = process.env.CMS_ENDPOINT;
			const API_VERSION = process.env.API_VERSION;
			const requestObject = {
				email: emailAddress,
				locale: translation.locale,
				callbackEndpoint: CMS_ENDPOINT
			};

			await axios.request({
				url: `${API_ENDPOINT}/api/auth/resend-verification`,
				method: "POST",
				data: JSON.stringify(requestObject),
				headers: {
					"content-type": "application/json",
					"x-api-version": API_VERSION
				}
			});

			setLoginError(translation.createAccountPage.checkInbox);
		} catch (error: unknown) {
			if ((error as AxiosError).response) {
				const axiosError = error as AxiosError;
				setLoginError(getErrorString(translation, axiosError?.response?.data?.error));
			}
		}
	};

	const submitForm = async (event: React.FormEvent<HTMLFormElement>) => {
		setLoading(true);
		event.preventDefault();
		sessionStorage.removeItem("token");
		const form = event.currentTarget as HTMLFormElement;
		if (form.checkValidity() === false) {
			event.stopPropagation();
			setValidated(true);
			setLoading(false);
		} else {
			try {
				const API_ENDPOINT = process.env.API_ENDPOINT;
				const API_VERSION = process.env.API_VERSION;

				const email = (form.elements.namedItem("formEmail") as HTMLInputElement).value;
				const password = (form.elements.namedItem("formPassword") as HTMLInputElement).value;

				const requestObject = {
					method: "email/password",
					email: email,
					password: password
				};

				const returnObject = await axios.request({
					url: `${API_ENDPOINT}/api/auth/sign-in`,
					method: "POST",
					data: JSON.stringify(requestObject),
					headers: {
						"content-type": "application/json",
						"x-api-version": API_VERSION
					}
				});

				if (returnObject?.data?.accessToken && returnObject?.data?.refreshToken) {
					const returnSelfObject = await axios.request({
						url: `${API_ENDPOINT}/api/accounts/self`,
						method: "GET",
						headers: {
							Authorization: "Bearer " + returnObject.data.accessToken,
							"content-type": "application/json",
							"x-api-version": API_VERSION
						}
					});

					const goToCompany = localStorage.getItem("goToCompany");
					if (returnSelfObject?.data?.accounts.length === 1 && !goToCompany) {
						const bodyFormData = new FormData();
						bodyFormData.append("accountId", returnSelfObject?.data?.accounts[0]._id);

						const returnToken = await axios.request({
							url: `${API_ENDPOINT}/api/accounts/token`,
							method: "POST",
							data: bodyFormData,
							headers: {
								Authorization: "Bearer " + returnObject.data.accessToken,
								"content-type": "multipart/form-data",
								"x-api-version": API_VERSION
							}
						});

						sessionStorage.setItem("token", returnToken?.data?.token);
					}
					localStorage.removeItem("goToCompany");
					localStorage.setItem("postSignup", "true");
					localStorage.setItem("accessToken", returnObject.data.accessToken);
					localStorage.setItem("refreshToken", returnObject.data.refreshToken);
					setIsLoggedIn(true);
				} else {
					setLoginError(getErrorString(translation, ""));
				}
				setLoading(false);
			} catch (error: unknown) {
				setValidated(false);
				setFormPassword("");
				if ((error as AxiosError).response) {
					const axiosError = error as AxiosError;
					if (axiosError?.response?.data?.error === "E_USER_NOT_VERIFIED") {
						const email = (form.elements.namedItem("formEmail") as HTMLInputElement).value;
						resendVerification(email);
					} else {
						setLoginError(getErrorString(translation, axiosError?.response?.data?.error));
					}
				}
				setLoading(false);
			}
		}
	};

	return (
		<FormBox>
			<HeadingText className="mb-3" data-testid="signInText">
				{translation.general.signIn}
			</HeadingText>
			<BodyText data-testid="signInSubText">{translation.signInPage.subText}</BodyText>
			{!!loginError && <ErrorMessage error={loginError} setError={setLoginError} displayCloseIcon={true} />}

			<Form className="mb-4 mt-4" noValidate onSubmit={submitForm} validated={validated} data-testid="signInSection">
				<Form.Group className="mb-3">
					<CustomInput className="form-control" type="email" pattern="[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,20}$" required autoFocus placeholder={translation.general.email} autoComplete="username" id="formEmail" data-testid="formEmail" />
				</Form.Group>
				<Form.Group className="mb-3">
					<CustomInput
						className="form-control"
						style={{ paddingRight: "60px" }}
						type={showPassword ? "text" : "password"}
						required
						placeholder={translation.general.password}
						autoComplete="current-password"
						id="formPassword"
						data-testid="formPassword"
						onChange={(value) => setFormPassword(value.target.value)}
						value={formPassword}
					/>
					{showPassword ? (
						<EyeSlashIcon
							data-testid="EyeSlashIcon"
							onClick={() => {
								setShowPassword(false);
							}}
						/>
					) : (
						<EyeIcon
							data-testid="EyeIcon"
							onClick={() => {
								setShowPassword(true);
							}}
						/>
					)}
				</Form.Group>
				<Form.Group className="mt-3 mb-3">
					<BodyText data-testid="forgotPasswordText">
						<LinkButton data-testid="forgotPasswordLink" onClick={() => navigate("/reset-password")}>
							{translation.general.forgotPassword}
						</LinkButton>
					</BodyText>
				</Form.Group>
				<MainButton type="submit" className="mt-3" data-testid="signInSubmit" disabled={loading}>
					{translation.general.signIn}
				</MainButton>
			</Form>

			{publicSignUp === "true" && (
				<BodyText data-testid="signInQuestion">
					{translation.signInPage.question}&nbsp;
					<LinkButton data-testid="signInLink" onClick={() => navigate("/create-account")}>
						{translation.signInPage.createAccount}
					</LinkButton>
				</BodyText>
			)}
		</FormBox>
	);
};

export default SignInForm;
