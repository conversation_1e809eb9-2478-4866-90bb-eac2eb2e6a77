import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Form } from "react-bootstrap";
import axios from "axios";
import { useTranslation } from "../hooks/translations";
import { getErrorString } from "../utils/getErrorString";
import { MainButton, LinkButton } from "@src/styles/components";
import { FormBox, HeadingText, BodyText, CustomInput } from "@src/styles/forms";
import { AxiosError } from "@src/types/axios";
import ErrorMessage from "@src/components/utils/ErrorMessage";

const SignInEmailForm: React.FC = () => {
	const navigate = useNavigate();
	const translation = useTranslation();
	const [validated, setValidated] = useState(false);
	const [loginError, setLoginError] = useState("");
	const [loading, setLoading] = useState(false);
	const [sentEmail, setSentEmail] = useState(false);
	const publicSignUp = process.env.PUBLIC_SIGN_UP;

	const resendVerification = async (emailAddress: string) => {
		try {
			const API_ENDPOINT = process.env.API_ENDPOINT;
			const CMS_ENDPOINT = process.env.CMS_ENDPOINT;
			const API_VERSION = process.env.API_VERSION;
			const requestObject = {
				email: emailAddress,
				locale: translation.locale,
				callbackEndpoint: CMS_ENDPOINT
			};

			await axios.request({
				url: `${API_ENDPOINT}/api/auth/resend-verification`,
				method: "POST",
				data: JSON.stringify(requestObject),
				headers: {
					"content-type": "application/json",
					"x-api-version": API_VERSION
				}
			});

			setLoginError(translation.createAccountPage.checkInbox);
		} catch (error: unknown) {
			if ((error as AxiosError).response) {
				const axiosError = error as AxiosError;
				setLoginError(getErrorString(translation, axiosError?.response?.data?.error));
			}
		}
	};

	const submitForm = async (event: React.FormEvent<HTMLFormElement>) => {
		setLoading(true);
		event.preventDefault();
		sessionStorage.removeItem("token");
		const form = event.currentTarget as HTMLFormElement;
		if (form.checkValidity() === false) {
			event.stopPropagation();
			setValidated(true);
			setLoading(false);
		} else {
			try {
				const API_ENDPOINT = process.env.API_ENDPOINT;
				const API_VERSION = process.env.API_VERSION;

				const email = (form.elements.namedItem("formEmail") as HTMLInputElement).value;

				const requestObject = {
					email: email,
					locale: translation.locale,
					callbackEndpoint: process.env.CMS_ENDPOINT
				};

				const returnObject = await axios.request({
					url: `${API_ENDPOINT}/api/auth/sign-in/email`,
					method: "POST",
					data: JSON.stringify(requestObject),
					headers: {
						"content-type": "application/json",
						"x-api-version": API_VERSION
					}
				});

				if (returnObject?.status === 200) {
					setSentEmail(true);
				}
				setLoading(false);
			} catch (error: unknown) {
				setValidated(false);
				if ((error as AxiosError).response) {
					const axiosError = error as AxiosError;
					if (axiosError?.response?.data?.error === "E_USER_NOT_VERIFIED") {
						const email = (form.elements.namedItem("formEmail") as HTMLInputElement).value;
						resendVerification(email);
					} else if (axiosError.response.data.error === "E_INVALID_AUTHORIZATION") {
						setLoginError(translation.errors.signInEmailError);
					} else {
						setLoginError(getErrorString(translation, axiosError?.response?.data?.error));
					}
				}
				setLoading(false);
			}
		}
	};

	return (
		<FormBox>
			{sentEmail ? (
				<>
					<HeadingText className="mb-3" data-testid="signInText">
						{translation.signInPage.emailSent}
					</HeadingText>
					<BodyText data-testid="emailSent">{translation.signInPage.checkInbox}</BodyText>
				</>
			) : (
				<>
					<HeadingText className="mb-3" data-testid="signInText">
						{translation.general.signIn}
					</HeadingText>
					<BodyText data-testid="signInSubText">{translation.signInPage.subText}</BodyText>
					{!!loginError && <ErrorMessage error={loginError} setError={setLoginError} displayCloseIcon={true} />}

					<Form className="mb-4 mt-4" noValidate onSubmit={submitForm} validated={validated} data-testid="signInSection">
						<Form.Group className="mb-3">
							<CustomInput className="form-control" type="email" pattern="[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,20}$" required autoFocus placeholder={translation.general.email} autoComplete="username" id="formEmail" data-testid="formEmail" />
						</Form.Group>
						<BodyText data-testid="manualSignIn">
							{translation.signInPage.manualSignInText1}
							<br />
							{translation.signInPage.manualSignInText2}&nbsp;
							<LinkButton style={{ textDecoration: "underline" }} data-testid="signInLink" onClick={() => navigate("/sign-in/password")}>
								{translation.signInPage.manualSignInText3}
							</LinkButton>
						</BodyText>
						<MainButton type="submit" className="mt-3" data-testid="signInSubmit" disabled={loading}>
							{translation.general.signInEmail}
						</MainButton>
					</Form>

					{publicSignUp === "true" && (
						<BodyText data-testid="signInQuestion">
							{translation.signInPage.question}&nbsp;
							<LinkButton data-testid="signUpLink" onClick={() => navigate("/create-account")}>
								{translation.signInPage.createAccount}
							</LinkButton>
						</BodyText>
					)}
				</>
			)}
		</FormBox>
	);
};

export default SignInEmailForm;
