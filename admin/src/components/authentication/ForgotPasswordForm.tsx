import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import { useSetRecoilState } from "recoil";
import { Form } from "react-bootstrap";
import { useTranslation } from "../hooks/translations";
import { getErrorString } from "../utils/getErrorString";
import { isLoggedInState } from "./state";
import { MainButton, LinkButton } from "@src/styles/components";
import { FormBox, HeadingText, BodyText, CustomInput, EyeIcon, EyeSlashIcon } from "@src/styles/forms";
import { AxiosError } from "@src/types/axios";
import ErrorMessage from "@src/components/utils/ErrorMessage";

const ForgotPasswordForm: React.FC = () => {
	const navigate = useNavigate();
	const translation = useTranslation();
	const [showError, setShowError] = useState("");
	const split = window.location.search.split("token=");
	const [formPassword, setFormPassword] = useState("");
	const newPasswordRef = useRef<HTMLInputElement>(null);
	const setIsLoggedIn = useSetRecoilState(isLoggedInState);
	const [validated, setValidated] = useState(false);
	const [formError, setFormError] = useState("");
	const [showPassword, setShowPassword] = useState(false);
	const [loading, setLoading] = useState(false);
	const [loggedOutUser, setLoggedOutUser] = useState(false);

	const submitForm = async (event: React.FormEvent<HTMLFormElement>) => {
		setLoading(true);
		event.preventDefault();
		const form = event.currentTarget;
		if (form.checkValidity() === false) {
			event.stopPropagation();
			setValidated(true);
			setLoading(false);
		} else {
			try {
				const API_ENDPOINT = process.env.API_ENDPOINT;
				const API_VERSION = process.env.API_VERSION;
				const password = (form.elements.namedItem("newPassword") as HTMLInputElement).value;
				const requestObject = {
					token: split[1],
					password: password
				};

				const returnObject = await axios.request({
					url: `${API_ENDPOINT}/api/auth/reset-password`,
					method: "POST",
					data: JSON.stringify(requestObject),
					headers: {
						"content-type": "application/json",
						"x-api-version": API_VERSION
					}
				});

				if (returnObject?.data?.accessToken && returnObject?.data?.refreshToken) {
					localStorage.setItem("accessToken", returnObject.data.accessToken);
					localStorage.setItem("refreshToken", returnObject.data.refreshToken);
					setIsLoggedIn(true);
					navigate("/");
				} else {
					setFormError(getErrorString(translation, ""));
				}
				setLoading(false);
			} catch (error: unknown) {
				setValidated(false);
				setLoading(false);
				if ((error as AxiosError).response) {
					const axiosError = error as AxiosError;
					setFormError(getErrorString(translation, axiosError?.response?.data?.error));
				}
			}
		}
	};

	useEffect(() => {
		(async () => {
			if (!split[1]) {
				setShowError(translation.errors.tokenError);
			} else if (!loggedOutUser) {
				localStorage.removeItem("accessToken");
				localStorage.removeItem("refreshToken");
				localStorage.removeItem("oidcClientId");
				localStorage.removeItem("userHash");
				localStorage.removeItem("postSignup");
				sessionStorage.removeItem("token");
				setIsLoggedIn(false);
				setLoggedOutUser(true);
			}
		})();
	}, [split, setShowError, loggedOutUser, setLoggedOutUser, setIsLoggedIn, translation.errors.tokenError]);

	return (
		<FormBox>
			{!showError ? (
				<>
					<HeadingText className="mb-3" data-testid="passwordResetText">
						{translation.passwordResetPage.newPasswordText}
					</HeadingText>
					<BodyText data-testid="passwordResetSubText">{translation.passwordResetPage.newPasswordSubText}</BodyText>
					{!!formError && <ErrorMessage error={formError} setError={setFormError} displayCloseIcon={true} />}
					<Form className="mb-4 mt-4" noValidate onSubmit={submitForm} validated={validated} data-testid="passwordResetSection">
						<Form.Group className="mb-3">
							<CustomInput
								type={showPassword ? "text" : "password"}
								required
								autoFocus
								pattern="^.{8,}$"
								className="form-control"
								style={{ paddingRight: "60px" }}
								placeholder={translation.passwordResetPage.newPassword}
								onChange={(value) => setFormPassword(value.target.value)}
								value={formPassword}
								id="newPassword"
								data-testid="newPassword"
								ref={newPasswordRef}
							/>
							{showPassword ? (
								<EyeSlashIcon
									data-testid="EyeSlashIcon"
									onClick={() => {
										setShowPassword(false);
									}}
								/>
							) : (
								<EyeIcon
									data-testid="EyeIcon"
									onClick={() => {
										setShowPassword(true);
									}}
								/>
							)}
							<Form.Control.Feedback type="invalid" data-testid="passwordTextError">
								{translation.errors.passwordFormat}
							</Form.Control.Feedback>
						</Form.Group>
						<MainButton type="submit" className="mt-3" data-testid="passwordResetSubmit" disabled={loading}>
							{translation.passwordResetPage.setPassword}
						</MainButton>
					</Form>

					<BodyText data-testid="passwordResetQuestion">
						{translation.passwordResetPage.newPasswordQuestion}&nbsp;
						<LinkButton onClick={() => navigate("/sign-in/password")} data-testid="signInLink">
							{translation.general.signIn}.
						</LinkButton>
					</BodyText>
				</>
			) : (
				<>
					<HeadingText className="mb-3" data-testid="generalError">
						{translation.general.error}
					</HeadingText>
					<ErrorMessage error={showError} setError={setShowError} displayCloseIcon={false} />
					<MainButton type="button" data-testid="signInLink" className="mt-3" onClick={() => navigate("/sign-in/password")}>
						{translation.general.signIn}
					</MainButton>
				</>
			)}
		</FormBox>
	);
};

export default ForgotPasswordForm;
