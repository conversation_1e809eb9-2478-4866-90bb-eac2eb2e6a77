import React, { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import { useSetRecoilState } from "recoil";
import { useTranslation } from "../hooks/translations";
import { getErrorString } from "../utils/getErrorString";
import { isLoggedInState } from "./state";
import { MainButton } from "@src/styles/components";
import { FormBox, HeadingText, BodyText } from "@src/styles/forms";
import { EventNameEnum } from "@src/types/events";
import { registerEvent } from "@src/components/events/service";
import { AxiosError } from "@src/types/axios";
import ErrorMessage from "@src/components/utils/ErrorMessage";

const ConfirmEmailSignIn: React.FC = () => {
	const navigate = useNavigate();
	const translation = useTranslation();
	const [showError, setShowError] = useState("");
	const split = window.location.search.split("token=");
	const setIsLoggedIn = useSetRecoilState(isLoggedInState);

	const verifyEmail = useCallback(
		async (token: string) => {
			try {
				const API_ENDPOINT = process.env.API_ENDPOINT;
				const API_VERSION = process.env.API_VERSION;
				const requestObject = {
					token: token
				};

				const returnObject = await axios.request({
					url: `${API_ENDPOINT}/api/auth/verify`,
					method: "POST",
					data: JSON.stringify(requestObject),
					headers: {
						"content-type": "application/json",
						"x-api-version": API_VERSION
					}
				});

				if (returnObject?.data?.accessToken && returnObject?.data?.refreshToken) {
					const returnSelfObject = await axios.request({
						url: `${API_ENDPOINT}/api/accounts/self`,
						method: "GET",
						headers: {
							Authorization: "Bearer " + returnObject.data.accessToken,
							"content-type": "application/json",
							"x-api-version": API_VERSION
						}
					});
					if (returnSelfObject?.data?.accounts.length === 1) {
						const bodyFormData = new FormData();
						bodyFormData.append("accountId", returnSelfObject?.data?.accounts[0]._id);

						const returnToken = await axios.request({
							url: `${API_ENDPOINT}/api/accounts/token`,
							method: "POST",
							data: bodyFormData,
							headers: {
								Authorization: "Bearer " + returnObject.data.accessToken,
								"content-type": "multipart/form-data",
								"x-api-version": API_VERSION
							}
						});

						sessionStorage.setItem("token", returnToken?.data?.token);
					}

					localStorage.setItem("accessToken", returnObject.data.accessToken);
					localStorage.setItem("refreshToken", returnObject.data.refreshToken);

					setIsLoggedIn(true);

					registerEvent({
						eventName: EventNameEnum.VERIFY_ACCOUNT_IMPRESSION
					});

					return "";
				}

				return getErrorString(translation, "");
			} catch (error: unknown) {
				localStorage.removeItem("accessToken");
				localStorage.removeItem("refreshToken");
				localStorage.removeItem("oidcClientId");
				sessionStorage.removeItem("token");
				if ((error as AxiosError).response) {
					const axiosError = error as AxiosError;
					return getErrorString(translation, axiosError?.response?.data?.error);
				}
			}
		},
		[setIsLoggedIn, translation]
	);

	useEffect(() => {
		(async () => {
			if (split[1]) {
				const returnValue = await verifyEmail(split[1]);
				setShowError(returnValue);
			} else {
				setShowError(translation.errors.tokenError);
			}
		})();
	}, [split, setShowError, translation.errors.tokenError, verifyEmail]);

	return (
		<FormBox>
			{!showError ? (
				<>
					<HeadingText className="mb-3" data-testid="emailConfirmed">
						{translation.createAccountPage.verifySignInTitle}
					</HeadingText>
					<BodyText data-testid="verifyEmailSubText">{translation.createAccountPage.verifySignInCopy}</BodyText>
					<MainButton
						type="button"
						data-testid="continueButton"
						className="mt-3"
						onClick={() => {
							localStorage.setItem("postSignup", "true");
							navigate("/");
						}}
					>
						{translation.general.continue}
					</MainButton>
				</>
			) : (
				<>
					<HeadingText className="mb-3" data-testid="generalError">
						{translation.general.error}
					</HeadingText>
					<ErrorMessage error={showError} setError={setShowError} displayCloseIcon={false} />
					<MainButton type="button" data-testid="signInLink" className="mt-3" onClick={() => navigate("/sign-in/password")}>
						{translation.general.signIn}
					</MainButton>
				</>
			)}
		</FormBox>
	);
};

export default ConfirmEmailSignIn;
