import React from "react";
import { useTranslation } from "../hooks/translations";
import { useNavigate } from "react-router-dom";
import {
	VideoManager,
	ArrowIcon,
	HomePageTitle,
	ImpressionsNumbers,
	BlueTextLink,
	ThemedProgressBar
} from "@src/styles/components";
import { BodyText } from "@src/styles/forms";
import { Account } from "@src/types/account";
import ProgressBar from "react-bootstrap/ProgressBar";
import { formatUnixTimestamp } from "@src/utils/time";
import { apTheme } from "@src/config/theme";
import { useTheme } from "styled-components";

type Props = {
	accountData: Account | undefined;
};

const ImpressionsProgressBar: React.FC<Props> = ({ accountData }) => {
	const navigate = useNavigate();
	const translation = useTranslation();
	const currentImpression = accountData?.totalImpressionsCurrentCycle || 0;
	const maxImpression = accountData?.subscription?.maxImpressionsPerCycle || 0;
	const nextBillingDate = accountData?.subscription?.nextBillingDate || 0;
	const theme: apTheme = useTheme() as apTheme;

	return (
		<VideoManager>
			<HomePageTitle className="mt-3 mb-3">
				<ThemedProgressBar>
					<ProgressBar now={currentImpression} max={maxImpression} variant={currentImpression === maxImpression ? "danger" : "bar-color"} style={{ height: "0.6rem", backgroundColor: theme.colors.apGreyButton }} />
				</ThemedProgressBar>
			</HomePageTitle>
			<ImpressionsNumbers data-max={currentImpression === maxImpression}>
				{currentImpression.toLocaleString()} / {maxImpression.toLocaleString()}
			</ImpressionsNumbers>
			<BodyText className="mb-0">{translation.dashboardPage.impressionsUsed}</BodyText>
			<BodyText className="mb-0">
				{translation.dashboardPage.resetAt} {formatUnixTimestamp(nextBillingDate)}
			</BodyText>
			{process.env.SHOW_PLANS_PAGE === "true" &&
				<BlueTextLink className="mt-2 mb-2" type="button" data-testid="helpResources" onClick={() => navigate("/plans-pricing")}>
					{translation.general.upgrade} <ArrowIcon />
				</BlueTextLink>
			}
		</VideoManager>
	);
};

export default ImpressionsProgressBar;
