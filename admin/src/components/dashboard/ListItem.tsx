import React, { useState, useEffect } from "react";
import { TrashIcon } from "@src/styles/forms";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "../hooks/translations";
import { DeleteVideoModal } from "../modals/DeleteVideoModal";
import {
	Flex,
	ListBox,
	ListGridBox,
	ListItemContainer,
	ListRowItem,
	ListRowGridItem,
	ListRowGridItemBox,
	StatsIcon,
	ScoreBarDiv,
	BlurredLock,
	CoverImageBox,
	UploadImage
} from "@src/styles/components";
import { Plays, Clicks, Likes, OpenLockIcon } from "@src/assets";
import { shoppableVideo } from "@src/types/videos";
import { ConfirmChangesModal } from "../modals/ConfirmChangesModal";
import { usePasswordRequired } from "../utils/getPasswordRequired";
import { useTokenCheck } from "../hooks/useTokenCheck";
import deleteVideo from "../utils/deleteVideo";
import { getErrorString } from "../utils/getErrorString";
import moment from "moment";
import { ProductTypeEnum } from "../../types/product";
import { Account } from "../../types/account";
import { DisplayFormatOptions } from "@src/types/snippetOptions";
import SVG from "../svg/SVG";

type Props = {
	videoItem: shoppableVideo;
	refreshList: () => void;
	setIsDropDisabled: (value: boolean) => void;
	showDeleteError: (value: string) => void;
	showConfirmationText: (value: string) => void;
	numberId: number;
	accountData: Account | undefined;
};

const ListItem: React.FC<Props> = ({
	videoItem,
	numberId,
	refreshList,
	setIsDropDisabled,
	showConfirmationText,
	showDeleteError,
	accountData
}) => {
	const navigate = useNavigate();
	const translation = useTranslation();
	const [modalStatus, setModalStatus] = useState(false);
	const [enterPassword, setEnterPassword] = useState(false);
	const passwordRequired = usePasswordRequired();
	const { apiRetryHandler } = useTokenCheck();
	const [loading, setLoading] = useState(false);

	const handleDelete = async () => {
		setLoading(true);
		const { error } = await apiRetryHandler(async () => await deleteVideo(videoItem._id));

		if (error) {
			const errorText = getErrorString(translation, error?.response?.data?.error);
			showDeleteError(errorText);
		} else {
			showConfirmationText(translation.modals.deleteVideoConfirmation);
			refreshList();
		}

		setModalStatus(false);
		setLoading(false);
	};

	const scoreBarLevel = videoItem.videoScore ? Math.round(videoItem.videoScore) : null;
	const playsCount = videoItem.videoPlayCount ? videoItem.videoPlayCount : null;
	const clicksCount =
		videoItem.linkClicks && videoItem.linkClicks.length > 0
			? videoItem.linkClicks.reduce((count, link) => count + link.clickCount, 0)
			: null;
	const likesCount = videoItem.likes ? videoItem.likes : 0;

	const [scoreBarColor, setScoreBarColor] = useState(0);

	useEffect(() => {
		if (scoreBarLevel !== null && scoreBarLevel <= 20) {
			setScoreBarColor(1);
		}
		if (scoreBarLevel !== null && scoreBarLevel > 20 && scoreBarLevel <= 40) {
			setScoreBarColor(2);
		}
		if (scoreBarLevel !== null && scoreBarLevel > 40 && scoreBarLevel <= 60) {
			setScoreBarColor(3);
		}
		if (scoreBarLevel !== null && scoreBarLevel > 60 && scoreBarLevel <= 80) {
			setScoreBarColor(4);
		}
		if (scoreBarLevel !== null && scoreBarLevel > 80) {
			setScoreBarColor(5);
		}
	}, [scoreBarLevel, setScoreBarColor]);

	useEffect(() => {
		if (modalStatus) {
			setIsDropDisabled(true);
		} else {
			setIsDropDisabled(false);
		}
	}, [modalStatus, setIsDropDisabled]);

	return (
		<>
			<ListItemContainer data-testid={`videoListRow${numberId}`}>
				{/* desktop view */}
				<div className="d-none d-lg-flex" style={{ alignItems: "center" }}>
					<ListGridBox onClick={() => navigate("/edit-video/" + videoItem._id)} clickable={true}>
						<ListRowGridItem data-testid={`videoListBoxes${numberId}`}>
							<ListRowGridItemBox />
							<ListRowGridItemBox />
							<ListRowGridItemBox />
							<ListRowGridItemBox />
							<ListRowGridItemBox />
							<ListRowGridItemBox />
						</ListRowGridItem>
					</ListGridBox>
					<ListBox style={{ width: "100px", alignItems: "center" }} onClick={() => navigate("/edit-video/" + videoItem._id)} clickable={true}>
						<CoverImageBox landscape={videoItem.videoDisplayMode === DisplayFormatOptions.LANDSCAPE}>
							<UploadImage src={videoItem.videoPosterURL} />
						</CoverImageBox>
					</ListBox>
					<ListBox style={{ width: "25%", alignItems: "center" }} onClick={() => navigate("/edit-video/" + videoItem._id)} clickable={true}>
						<ListRowItem data-testid={`videoListTitle${numberId}`}>{videoItem.title}</ListRowItem>
					</ListBox>
					<ListBox style={{ width: "10%" }} onClick={() => navigate("/edit-video/" + videoItem._id)} clickable={true}>
						<ListRowItem style={{ paddingLeft: "8px" }} data-testid={`videoListProducts${numberId}`}>
							{videoItem.products?.length}
						</ListRowItem>
					</ListBox>
					<ListBox
						style={{ minWidth: "12.5rem" }}
						onClick={() => navigate("/edit-video/" + videoItem._id)}
						clickable={true}
					>
						<ListRowItem style={{ paddingLeft: "8px" }} data-testid={`videoListDate${numberId}`}>
							{moment(videoItem.createdAt).format("DD/MM/YYYY")}
						</ListRowItem>
					</ListBox>
					<ListBox style={{ width: "10%" }} onClick={() => navigate("/edit-video/" + videoItem._id)} clickable={true}>
						{playsCount !== null && (
							<>
								<StatsIcon>
									<SVG src={Plays} useFastLoading />
								</StatsIcon>
								<ListRowItem>{playsCount}</ListRowItem>
							</>
						)}
					</ListBox>
					<ListBox style={{ width: "10%" }} onClick={() => navigate("/edit-video/" + videoItem._id)} clickable={true}>
						{clicksCount !== null && (
							<>
								<StatsIcon>
									<SVG src={Clicks} useFastLoading/>
								</StatsIcon>
								<ListRowItem>{clicksCount}</ListRowItem>
							</>
						)}
					</ListBox>
					{accountData?.subscription.type === ProductTypeEnum.BASIC ? (
						<ListBox
							style={{ width: "10%" }}
							onClick={() => navigate(
								process.env.SHOW_PLANS_PAGE === "true" ? ("/plans-pricing") : ("/edit-video/" + videoItem._id)
							)}
							clickable={true}
						>
							<BlurredLock>
								<SVG src={OpenLockIcon} useFastLoading />
							</BlurredLock>
						</ListBox>
					) : (
						<ListBox style={{ width: "10%" }} onClick={() => navigate("/edit-video/" + videoItem._id)} clickable={true}>
							<StatsIcon>
								<SVG src={Likes} useFastLoading/>
							</StatsIcon>
							<ListRowItem>{likesCount}</ListRowItem>
						</ListBox>
					)}
					<ListBox
						style={{ width: "10%", display: "flex", alignItems: "center" }}
						onClick={() => navigate("/edit-video/" + videoItem._id)}
						clickable={true}
					>
						{accountData?.subscription.enableEngagementMetrics && scoreBarLevel !== null && (
							<ScoreBarDiv scoreBarLevel={scoreBarColor}>{scoreBarLevel}</ScoreBarDiv>
						)}
					</ListBox>
					<ListBox style={{ width: "3.5rem" }} last={true}>
						<ListRowItem>
							<TrashIcon data-testid={`trashIcon${numberId}`} onClick={() => setModalStatus(true)} />
						</ListRowItem>
					</ListBox>
				</div>

				{/* mobile view */}
				<div className="d-flex d-lg-none">
					<div
						onClick={() => navigate("/edit-video/" + videoItem._id)}
						style={{ width: "100px", marginTop: "30px" }}
					>
						<CoverImageBox landscape={videoItem.videoDisplayMode === DisplayFormatOptions.LANDSCAPE}>
							<UploadImage src={videoItem.videoPosterURL} />
						</CoverImageBox>
					</div>
					<div style={{ width: "70%" }}>
						<ListBox style={{ width: "100%" }} onClick={() => navigate("/edit-video/" + videoItem._id)} clickable={true}>
							<ListRowItem noPadding={true} style={{ fontWeight: "bold" }}>
								{videoItem.title}
							</ListRowItem>
						</ListBox>
						<Flex>
							<ListBox style={{ width: "50%" }} onClick={() => navigate("/edit-video/" + videoItem._id)} clickable={true}>
								<ListRowItem noPadding={true}>
									{translation.dashboardPage.products}: {videoItem.products?.length}
								</ListRowItem>
							</ListBox>
							<ListBox
								style={{ minWidth: "50%" }}
								onClick={() => navigate("/edit-video/" + videoItem._id)}
								clickable={true}
							>
								<ListRowItem noPadding={true}>{moment(videoItem.createdAt).format("DD/MM/YYYY")}</ListRowItem>
							</ListBox>
						</Flex>
						<Flex>
							<ListBox style={{ width: "33%" }} onClick={() => navigate("/edit-video/" + videoItem._id)} clickable={true}>
								{accountData?.subscription.enableEngagementMetrics && playsCount !== null && (
									<>
										<StatsIcon mobile={true}>
											<SVG src={Plays} useFastLoading />
										</StatsIcon>
										<ListRowItem noPadding={true}>{playsCount}</ListRowItem>{" "}
									</>
								)}
							</ListBox>
							<ListBox style={{ width: "33%" }} onClick={() => navigate("/edit-video/" + videoItem._id)} clickable={true}>
								{accountData?.subscription.enableEngagementMetrics && clicksCount !== null && (
									<>
										<StatsIcon mobile={true}>
											<SVG src={Clicks} useFastLoading />
										</StatsIcon>
										<ListRowItem noPadding={true}>{clicksCount}</ListRowItem>{" "}
									</>
								)}
							</ListBox>
							<ListBox style={{ width: "33%" }} onClick={() => navigate("/edit-video/" + videoItem._id)} clickable={true}>
								{accountData?.subscription.type !== ProductTypeEnum.BASIC && (
									<>
										<StatsIcon mobile={true}>
											<SVG src={Likes} useFastLoading />
										</StatsIcon>
										<ListRowItem noPadding={true}>{likesCount}</ListRowItem>{" "}
									</>
								)}
							</ListBox>
						</Flex>
					</div>

					<div style={{ width: "5rem", padding: "10px" }}>
						<ListBox last={true}>
							<ListRowItem noPadding={true}>
								<TrashIcon onClick={() => setModalStatus(true)} />
							</ListRowItem>
						</ListBox>
						<ListBox onClick={() => navigate("/edit-video/" + videoItem._id)} clickable={true}>
							{accountData?.subscription.enableEngagementMetrics && scoreBarLevel !== null && (
								<ScoreBarDiv scoreBarLevel={scoreBarColor}>{scoreBarLevel}</ScoreBarDiv>
							)}
						</ListBox>
					</div>
				</div>
			</ListItemContainer>
			<DeleteVideoModal
				visible={modalStatus}
				setVisible={setModalStatus}
				onCancel={() => setModalStatus(false)}
				setEnterPassword={setEnterPassword}
				handleDelete={handleDelete}
				passwordRequired={passwordRequired ?? false}
				loading={loading}
			/>
			<ConfirmChangesModal visible={enterPassword} onCancel={() => setEnterPassword(false)} onContinue={handleDelete} />
		</>
	);
};

export default ListItem;
