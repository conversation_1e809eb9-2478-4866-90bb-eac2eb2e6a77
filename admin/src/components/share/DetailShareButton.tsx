import React from "react";
import { ButtonIconDiv, MainButton } from "../../styles/components";
import { LockIcon, LinkIcon } from "../../assets";
import { useTranslation } from "../hooks/translations";
import { useNavigate } from "react-router-dom";
import { ShareType } from "@src/types/share";
import SVG from "../svg/SVG";

interface DetailShareButtonProps {
	allowSharing: boolean;
	disabled: boolean;
	setShareModalStatus: (status: boolean) => void;
	shareType?: ShareType;
	saveChanges?: boolean;
	setModalStatus?: (value: boolean) => void;
	setNavigationUrl?: (value: string) => void;
	style?: React.CSSProperties;
}

const DetailShareButton: React.FC<DetailShareButtonProps> = ({
	allowSharing,
	disabled,
	setShareModalStatus,
	shareType,
	saveChanges,
	setModalStatus,
	setNavigationUrl,
	style
}) => {
	const navigate = useNavigate();
	const translation = useTranslation();
	const showPlansPage = process.env.SHOW_PLANS_PAGE === "true";

	const handleClick = () => {
		if (allowSharing || shareType === ShareType.SHARE_VIDEO) {
			setShareModalStatus(true);
		} else if (showPlansPage) {
			if (saveChanges && setNavigationUrl && setModalStatus) {
				setNavigationUrl("/plans-pricing");
				setModalStatus(true);
			} else {
				navigate("/plans-pricing");
			}
		}
	};

	return (
		<MainButton
			type="button"
			data-testid="ShareButton"
			style={{
				...style,
				cursor: (allowSharing || shareType === ShareType.SHARE_VIDEO || showPlansPage ? "pointer" : "not-allowed")
			}}
			onClick={handleClick}
			greyColor={true}
			disabled={disabled}
		>
			<ButtonIconDiv style={{ cursor: "inherit" }}>
				<SVG
					src={allowSharing || shareType === ShareType.SHARE_VIDEO ? LinkIcon : LockIcon}
					alt={translation.modals.copyLink}
				/>
			</ButtonIconDiv>
			{translation.general.share}
		</MainButton>
	);
};

export default DetailShareButton;
