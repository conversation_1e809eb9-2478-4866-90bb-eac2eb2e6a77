import React from "react";
import { ButtonIconDiv, GreyButton } from "../../styles/components";
import { LockIcon, LinkIcon } from "../../assets";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "../hooks/translations";
import SVG from "../svg/SVG";

interface ListShareButtonProps {
	allowSharing: boolean;
	disabled: boolean;
	numberId: string | number;
	setShareModalStatus: (status: boolean) => void;
}

const ListShareButton: React.FC<ListShareButtonProps> = ({ allowSharing, disabled, numberId, setShareModalStatus }) => {
	const navigate = useNavigate();
	const translation = useTranslation();
	const showPlansPage = process.env.SHOW_PLANS_PAGE === "true";

	const handleClick = () => {
		if (allowSharing) {
			setShareModalStatus(true);
		} else if (showPlansPage) {
			navigate("/plans-pricing");
		}
	};

	return (
		<GreyButton
			type="button"
			onClick={handleClick}
			data-testid={`shareButton${numberId}`}
			style={{ height: "45px", cursor: (allowSharing || showPlansPage) ? "pointer" : "not-allowed" }}
			disabled={disabled}
		>
			<ButtonIconDiv style={{ cursor: "inherit" }}>
				<SVG
					src={allowSharing ? LinkIcon : LockIcon}
					alt={translation.modals.copyLink}
				/>
			</ButtonIconDiv>
			{translation.general.share}
		</GreyButton>
	);
};

export default ListShareButton;
