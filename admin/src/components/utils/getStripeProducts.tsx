import axios from "axios";

const getStripeProducts = async () => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const API_VERSION = "3";
	const accessToken = localStorage.getItem("accessToken");

	const queryResp = await axios.request({
		url: `${API_ENDPOINT}/api/products`,
		method: "GET",
		headers: {
			"content-type": "application/json",
			Authorization: "Bearer " + accessToken,
			"x-api-version": API_VERSION
		}
	});

	const data = queryResp?.data;

	return {
		data: data
	};
};

export default getStripeProducts;
