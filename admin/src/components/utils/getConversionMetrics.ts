import axios from "axios";

const getConversionMetrics = async (startDate: Date, endDate: Date) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const API_VERSION = process.env.API_VERSION;
	const accessToken = localStorage.getItem("accessToken");
	const accountToken = sessionStorage.getItem("token");

	const queryResp = await axios.request({
		url: `${API_ENDPOINT}/api/metrics/conversion/?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`,
		method: "GET",
		headers: {
			Authorization: "Bearer " + accessToken,
			"x-api-version": API_VERSION,
			"x-account-token": accountToken
		}
	});

	const currentMetricConversions = queryResp?.data?.currentMetricConversions;
	const previousMetricConversions = queryResp?.data?.previousMetricConversions;

	const data = {
		totalOrders: currentMetricConversions?.totalOrders,
		totalOrdersDiff: currentMetricConversions?.totalOrders - previousMetricConversions?.totalOrders,
		engagedSessions: currentMetricConversions?.userEngagementSessions,
		engagedSessionsDiff: currentMetricConversions?.userEngagementSessions - previousMetricConversions?.userEngagementSessions,
		avgBasketSize: parseFloat(currentMetricConversions?.averageBasketSize).toFixed(1),
		avgBasketSizeDiff: parseFloat((currentMetricConversions?.averageBasketSize - previousMetricConversions?.averageBasketSize).toFixed(1)),
		conversionRate: parseFloat(currentMetricConversions?.userConversionRate).toFixed(2),
		conversionRateDiff: parseFloat((currentMetricConversions?.userConversionRate - previousMetricConversions?.userConversionRate).toFixed(2))
	};

	return { data };
};

export default getConversionMetrics;
