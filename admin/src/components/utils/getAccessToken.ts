import axios from "axios";

const getAccessToken = async (id: string) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const accessToken = localStorage.getItem("accessToken");
	const API_VERSION = process.env.API_VERSION;
	let error = "";
	const bodyFormData = new FormData();
	bodyFormData.append("accountId", id);

	const returnToken = await axios.request({
		url: `${API_ENDPOINT}/api/accounts/token`,
		method: "POST",
		data: bodyFormData,
		headers: {
			Authorization: "Bearer " + accessToken,
			"content-type": "multipart/form-data",
			"x-api-version": API_VERSION
		}
	});

	sessionStorage.setItem("token", returnToken?.data?.token);
	if (!returnToken.data?.token) {
		error = "error";
	}
	return { error };
};

export default getAccessToken;
