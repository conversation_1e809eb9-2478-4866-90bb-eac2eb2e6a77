import axios from "axios";
import { shoppableVideo } from "@src/types/videos";

interface EditCollectionProps {
	id: string;
	collectionTitle: string;
	buttonBackgroundColor: string;
	buttonBackgroundBlur: boolean;
	iconTextColor: string;
	selectedFont: string;
	borderRadius: number;
	centerOnPage: boolean;
	margin: number;
	paddingBetween: number;
	widgetBorderRadius: number;
	widgetPosition: string;
	inlineBorderRadius: number;
	videos: shoppableVideo[];
}

const editCollection = async ({
	id,
	collectionTitle,
	buttonBackgroundColor,
	buttonBackgroundBlur,
	iconTextColor,
	selectedFont,
	borderRadius,
	centerOnPage,
	margin,
	paddingBetween,
	widgetBorderRadius,
	widgetPosition,
	inlineBorderRadius,
	videos
}: EditCollectionProps) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const API_VERSION = process.env.API_VERSION;
	const accessToken = localStorage.getItem("accessToken");
	const accountToken = sessionStorage.getItem("token");
	const videoIds: string[] = [];

	videos?.map((x) => {
		videoIds.push(x._id);
	});

	const requestObject = {
		title: collectionTitle,
		shoppableVideos: videoIds,
		buttonBackgroundColor: buttonBackgroundColor,
		buttonBackgroundBlur: buttonBackgroundBlur,
		iconTextColor: iconTextColor,
		displayFont: selectedFont,
		carouselBorderRadius: borderRadius,
		carouselIsCentered: centerOnPage,
		carouselMargin: margin,
		carouselGap: paddingBetween,
		widgetBorderRadius: widgetBorderRadius,
		widgetPosition: widgetPosition,
		inlineBorderRadius: inlineBorderRadius
	};

	const queryHeaders: Record<string, string | null | undefined> = {
		"content-type": "application/json",
		"Authorization": "Bearer " + accessToken,
		"x-api-version": API_VERSION,
		"x-account-token": accountToken
	};

	const queryResp = await axios.request({
		url: `${API_ENDPOINT}/api/shoppable-collections/${id}`,
		method: "PUT",
		data: JSON.stringify(requestObject),
		headers: queryHeaders
	});

	const data = queryResp?.data;
	return { data };
};

export default editCollection;
