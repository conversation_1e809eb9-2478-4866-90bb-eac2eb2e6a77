import axios from "axios";
import { JobVideoStatus, VideoEncodeResponse } from "@src/types/files";

const uploadVideoFile = async (file: File, setProgressBar?: (value: number) => void) => {
	let data: VideoEncodeResponse;

	if (file.size === 0) {
		throw Error("error");
	}

	// Step 1: Get signed URL for uploading
	const result = await getSignedUrl(file.name);
	const signedUrl = result.signedURL;

	// Step 2: Initiate the resumable upload
	const uploadUrl = await initiateResumableUpload(signedUrl);

	// Step 3: Upload file in chunks
	await uploadFileInChunks(uploadUrl, file, setProgressBar);

	// Step 4: Notify server that the upload is complete
	const encodeResponse = await jobsEvent(result.tempFilename);

	if (encodeResponse.status === JobVideoStatus.CREATED) {
		data = encodeResponse;
	} else {
		throw new Error(`Video processing failed: ${encodeResponse.statusMessage}`);
	}

	return { data };
};

export default uploadVideoFile;

const getSignedUrl = async (fileName: string) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const API_VERSION = process.env.API_VERSION;
	const accountToken = sessionStorage.getItem("token");
	const accessToken = localStorage.getItem("accessToken");

	const requestData = {
		fileName: fileName
	};

	const queryResp = await axios.request({
		url: `${API_ENDPOINT}/api/videos/signed-url`,
		method: "POST",
		data: JSON.stringify(requestData),
		headers: {
			"Authorization": "Bearer " + accessToken,
			"Content-Type": "application/json",
			"x-api-version": API_VERSION,
			"x-account-token": accountToken
		}
	});
	return queryResp?.data;
};

const initiateResumableUpload = async (signedUrl: string) => {
	const response = await fetch(signedUrl, {
		method: "POST",
		headers: {
			"Content-Type": "video/*",
			"x-goog-resumable": "start"
		}
	});

	if (!response.ok) {
		throw new Error(`Failed to initiate upload: ${response.statusText}`);
	}

	const uploadUrl = response.headers.get("Location");
	if (!uploadUrl) {
		throw new Error("Upload URL not found in response headers");
	}

	return uploadUrl;
};

const uploadFileInChunks = async (
	uploadUrl: string,
	file: File,
	setProgressBar?: (value: number) => void
) => {
	let chunkSize = 256 * 1024;
	const minChunkSizeBytes = chunkSize;
	const maxChunkSizeBytes = 4096 * 1024;
	const minChunkTimeMs = 2000;
	const maxChunkTimeMs = 4000;
	let startPointer = 0;

	while (startPointer < file.size) {
		const chunkStartTime = Date.now();
		let endPointer = startPointer + chunkSize;
		if (endPointer > file.size) {
			endPointer = file.size;
		}

		if (setProgressBar) {
			const progress = (startPointer * 100) / file.size;
			setProgressBar(Math.round(progress));
		}

		const chunk = file.slice(startPointer, endPointer);
		const contentRange = `bytes ${startPointer}-${endPointer - 1}/${file.size}`;

		let uploadSuccessful = false;
		let retryCount = 0;
		const maxRetries = 5;

		while (!uploadSuccessful) {
			try {
				const queryResponse = await fetch(uploadUrl, {
					method: "PUT",
					headers: {
						"Content-Range": contentRange,
						"Content-Type": "video/*"
					},
					body: chunk
				});

				if (queryResponse.status !== 308 && queryResponse.status !== 200) {
					throw new Error(`Upload failed with status ${queryResponse.status}`);
				}

				startPointer += chunkSize;
				uploadSuccessful = true;

				const chunkTime = Date.now() - chunkStartTime;
				if (chunkTime < minChunkTimeMs && chunkSize < maxChunkSizeBytes) {
					chunkSize = Math.min(chunkSize * 2, maxChunkSizeBytes);
				} else if (chunkTime > maxChunkTimeMs && chunkSize > minChunkSizeBytes) {
					chunkSize = Math.max(chunkSize / 2, minChunkSizeBytes);
				}

				if (queryResponse.status === 200) {
					return;
				}
			} catch (error: unknown) {
				if (!navigator.onLine) {
					await waitUntilOnline();
				} else {
					retryCount++;
					if (retryCount >= maxRetries) {
						throw new Error(`Failed to upload chunk after ${maxRetries} retries: ${(error as Error).message}`);
					}
					await new Promise((resolve) => setTimeout(resolve, 2000));
				}
			}
		}
	}
};

const waitUntilOnline = () => {
	console.info("Waiting for network connection...");
	return new Promise<void>((resolve) => {
		if (navigator.onLine) {
			resolve();
		} else {
			const handleOnline = () => {
				console.info("Network connection restored.");
				window.removeEventListener("online", handleOnline);
				resolve();
			};
			window.addEventListener("online", handleOnline);
		}
	});
};


const jobsEvent = async (filename: string) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const API_VERSION = process.env.API_VERSION;
	const accountToken = sessionStorage.getItem("token");
	const accessToken = localStorage.getItem("accessToken");
	const requestData = {
		tempFilename: filename
	};

	const maxRetries = 5;
	const initialDelayMs = 5000;
	let retryCount = 0;
	let delay = initialDelayMs;
	while (retryCount < maxRetries) {
		try {
			const response = await axios.request({
				url: `${API_ENDPOINT}/api/videos/events/uploaded`,
				method: "POST",
				data: JSON.stringify(requestData),
				headers: {
					"Content-Type": "application/json",
					"Authorization": "Bearer " + accessToken,
					"x-api-version": API_VERSION,
					"x-account-token": accountToken
				}
			});

			if (response.status >= 200 && response.status < 300) {
				return response.data;
			} else if (response.status >= 500) {
				throw new Error("Failed to notify server for uploaded, retrying...");
			}
		} catch (error: unknown) {
			if (retryCount === maxRetries - 1) {
				throw new Error(`Failed to notify server after ${maxRetries} retries: ${(error as Error).message}`);
			}
			await new Promise((resolve) => setTimeout(resolve, delay));
			delay *= 2;
			retryCount++;
		}
	}

};
