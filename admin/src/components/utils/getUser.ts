import axios from "axios";
import jwt_decode from "jwt-decode";
import { accessToken, companyDetails } from "@src/types/videos";

const getUser = async () => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const accessToken = localStorage.getItem("accessToken");
	const API_VERSION = process.env.API_VERSION;
	const accountToken = sessionStorage.getItem("token");
	const data = {} as companyDetails;
	let error = "";

	if (accessToken && accountToken) {
		const decoded = jwt_decode(accessToken) as accessToken;
		const queryResp = await axios.request({
			url: `${API_ENDPOINT}/api/users/${decoded?.userId}`,
			method: "GET",
			headers: {
				"content-type": "application/json",
				"Authorization": "Bearer " + accessToken,
				"x-api-version": API_VERSION,
				"x-account-token": accountToken
			}
		});

		data.email = queryResp?.data?.user?.email ?? "";
		data.firstName = queryResp?.data?.user?.firstName ?? "";
		data.lastName = queryResp?.data?.user?.lastName ?? "";
		data.isPasswordSet = true;

		if (queryResp.data?.user && "isPasswordSet" in queryResp.data.user) {
			data.isPasswordSet = queryResp.data.user.isPasswordSet;
		}

		return { data, error };
	}

	error = "error";
	return { data, error };
};

export default getUser;
