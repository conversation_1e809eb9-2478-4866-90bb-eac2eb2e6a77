import axios from "axios";
import { VoiceProviderName } from "@src/types/avatar";

const uploadCustomVoice = async (
	audioFile: File,
	name: string
) => {
	try {
		const API_ENDPOINT = process.env.API_ENDPOINT;
		const accessToken = localStorage.getItem("accessToken");
		const accountToken = sessionStorage.getItem("token");

		const formData = new FormData();
		formData.append("audio", audioFile);
		formData.append("name", name);
		formData.append("provider", VoiceProviderName.ELEVENLABS);

		const response = await axios.request({
			url: `${API_ENDPOINT}/api/avatar/voice`,
			method: "POST",
			data: formData,
			headers: {
				"Authorization": "Bearer " + accessToken,
				"content-type": "multipart/form-data",
				"x-api-version": process.env.API_VERSION || "2",
				"x-account-token": accountToken
			}
		});

		if (!response.data) {
			return {
				data: null,
				error: "Failed to upload voice"
			};
		}

		return {
			data: response.data,
			error: null
		};
	} catch (error) {
		return {
			data: null,
			error: error instanceof Error ? error.message : "Unknown error occurred"
		};
	}
};

export default uploadCustomVoice;
