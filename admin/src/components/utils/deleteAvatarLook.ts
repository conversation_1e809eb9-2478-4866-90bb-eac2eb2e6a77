import axios from "axios";

const deleteAvatarLook = async (id: string) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const accessToken = localStorage.getItem("accessToken");
	const API_VERSION = process.env.API_VERSION;
	const accountToken = sessionStorage.getItem("token");
	const error = "";

	const queryHeaders: Record<string, string | null | undefined> = {
		"content-type": "application/json",
		Authorization: "Bearer " + accessToken,
		"x-api-version": API_VERSION,
		"x-account-token": accountToken
	};

	const queryResp = await axios.request({
		url: `${API_ENDPOINT}/api/avatar/look/${id}`,
		method: "DELETE",
		headers: queryHeaders
	});

	const data = queryResp?.data;
	return { data, error };
};

export default deleteAvatarLook;
