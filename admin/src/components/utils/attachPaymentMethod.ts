import axios from "axios";

const attachPaymentMethod = async (paymentMethodId: string) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const accessToken = localStorage.getItem("accessToken");
	const accountToken = sessionStorage.getItem("token");

	if (accessToken && accountToken) {
		const queryHeaders: Record<string, string | null | undefined> = {
			"content-type": "application/json",
			"Authorization": "Bearer " + accessToken,
			"x-api-version": "3",
			"x-account-token": accountToken
		};

		const requestObject = {
			paymentMethodId: paymentMethodId
		};

		const queryResp = await axios.request({
			url: `${API_ENDPOINT}/api/stripe/payment`,
			method: "POST",
			data: JSON.stringify(requestObject),
			headers: queryHeaders
		});

		const data = queryResp?.data;
		return { data };
	}

	const error = "error";
	return { error };
};

export default attachPaymentMethod;
