import axios from "axios";

const getLooks = async () => {
	try {
		const API_ENDPOINT = process.env.API_ENDPOINT;
		const API_VERSION = process.env.API_VERSION;
		const accessToken = localStorage.getItem("accessToken");
		const accountToken = sessionStorage.getItem("token");

		const response = await axios.request({
			url: `${API_ENDPOINT}/api/avatar/look`,
			method: "GET",
			headers: {
				"Authorization": "Bearer " + accessToken,
				"x-api-version": API_VERSION,
				"x-account-token": accountToken,
				"Accept": "application/json"
			}
		});

		return { data: response.data, error: null };
	} catch (error) {
		return { data: null, error: "Error fetching avatars" };
	}
};

export default getLooks;
