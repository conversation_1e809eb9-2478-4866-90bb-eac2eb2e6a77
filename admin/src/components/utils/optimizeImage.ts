import axios from "axios";

const optimizeImage = async (imageURL: string, imageWidthPx?: number, imageHeightPx?: number) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const accessToken = localStorage.getItem("accessToken");
	const API_VERSION = process.env.API_VERSION;
	const accountToken = sessionStorage.getItem("token");

	if (accessToken && accountToken) {
		const jobFormData = new FormData();
		jobFormData.append("sourceURL", imageURL);
		jobFormData.append("jobType", "optimizeImage");
		if (imageWidthPx) jobFormData.append("imageWidthPx", imageWidthPx.toString());
		if (imageHeightPx) jobFormData.append("imageHeightPx", imageHeightPx.toString());

		const queryResp = await axios.request({
			url: `${API_ENDPOINT}/api/jobs`,
			method: "POST",
			data: jobFormData,
			headers: {
				"content-type": "multipart/form-data",
				"Authorization": "Bearer " + accessToken,
				"x-api-version": API_VERSION,
				"x-account-token": accountToken
			}
		});

		const data = queryResp?.data.job.status === "complete" ? queryResp?.data.job.imageURL : null;
		if (!data) throw new Error("E_OPTIMIZE_ERROR");
		return { data };
	}

	const error = "missing accessToken or accountToken";
	return { error };
};

export default optimizeImage;
