import axios from "axios";
import jwt_decode from "jwt-decode";
import { accessToken, companyPage } from "@src/types/videos";

const getCompanies = async () => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const accessToken = localStorage.getItem("accessToken");
	const API_VERSION = process.env.API_VERSION;
	const data = {} as companyPage;
	let error = "";

	if (accessToken) {
		const decoded = jwt_decode(accessToken) as accessToken;

		const queryResp = await axios.request({
			url: `${API_ENDPOINT}/api/users/${decoded?.userId}`,
			method: "GET",
			headers: {
				"content-type": "application/json",
				"Authorization": "Bearer " + accessToken,
				"x-api-version": API_VERSION
			}
		});

		data.email = queryResp?.data?.user?.email ?? "";
		data.firstName = queryResp?.data?.user?.firstName ?? "";
		data.lastName = queryResp?.data?.user?.lastName ?? "";
		data.postSignupCompleted = queryResp?.data?.user?.postSignupCompleted ?? false;
		data.maxCompanies = queryResp?.data?.user?.maxCompanies ?? 1;

		const queryResp2 = await axios.request({
			url: `${API_ENDPOINT}/api/accounts/self`,
			method: "GET",
			headers: {
				"content-type": "application/json",
				"Authorization": "Bearer " + accessToken,
				"x-api-version": API_VERSION
			}
		});

		data.accounts = queryResp2?.data;

		return { data, error };
	}

	error = "error";
	return { data, error };
};

export default getCompanies;
