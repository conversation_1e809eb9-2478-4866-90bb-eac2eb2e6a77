import axios from "axios";

const createLook = async (
	imageFile: File,
	groupId: string | null
) => {
	try {
		const API_ENDPOINT = process.env.API_ENDPOINT;
		const API_VERSION = process.env.API_VERSION;
		const accessToken = localStorage.getItem("accessToken");
		const accountToken = sessionStorage.getItem("token");

		const formData = new FormData();
		formData.append("look", imageFile);
		formData.append("name", imageFile.name);

		if (groupId) {
			formData.append("groupId", groupId);
		}

		const response = await axios.request({
			url: `${API_ENDPOINT}/api/avatar/look`,
			method: "POST",
			data: formData,
			headers: {
				"Authorization": "Bearer " + accessToken,
				"x-api-version": API_VERSION,
				"x-account-token": accountToken,
				"Content-Type": "multipart/form-data"
			}
		});

		return { data: response.data, error: null };
	} catch (error) {
		return { data: null, error: "Error creating custom avatar" };
	}
};

export default createLook;
