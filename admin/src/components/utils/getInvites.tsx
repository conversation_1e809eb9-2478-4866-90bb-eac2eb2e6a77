import { Invitation } from "@src/types/invitations";
import axios from "axios";

const getInvites = async () => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const accessToken = localStorage.getItem("accessToken");
	const API_VERSION = process.env.API_VERSION;
	let error = "";
	let data = {};

	if (accessToken) {
		const queryResp = await axios.request({
			url: `${API_ENDPOINT}/api/invitations/self`,
			method: "GET",
			headers: {
				"content-type": "application/json",
				Authorization: "Bearer " + accessToken,
				"x-api-version": API_VERSION
			}
		});

		data = queryResp.data.invitations.filter((invite: Invitation) => invite.status === "pending");
		return { data, error };
	}

	error = "error";
	return { data, error };
};

export default getInvites;
