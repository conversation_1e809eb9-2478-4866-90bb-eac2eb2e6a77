import axios from "axios";

const removeUser = async (userId: string, isInvite?: boolean) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const API_VERSION = process.env.API_VERSION;
	const accessToken = localStorage.getItem("accessToken");
	const accountToken = sessionStorage.getItem("token");

	let queryResp;

	const queryHeaders: Record<string, string | null | undefined> = {
		"content-type": "application/json",
		"Authorization": "Bearer " + accessToken,
		"x-api-version": API_VERSION,
		"x-account-token": accountToken
	};

	if (isInvite) {
		queryResp = await axios.request({
			url: `${API_ENDPOINT}/api/invitations/${userId}`,
			method: "DELETE",
			headers: queryHeaders
		});
	} else {
		queryResp = await axios.request({
			url: `${API_ENDPOINT}/api/accounts/users/${userId}`,
			method: "DELETE",
			headers: queryHeaders
		});
	}

	let error = "";
	if (queryResp.status === 200) {
		const data = queryResp.data;
		return { data, error };
	}

	error = queryResp.data.message;
	throw new Error(error);
};

export default removeUser;
