import axios from "axios";
import { checkOidcDomain } from "@src/utils/compare";

const inviteUser = async (email: string, locale: string) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const API_VERSION = process.env.API_VERSION;
	const accessToken = localStorage.getItem("accessToken");
	const accountToken = sessionStorage.getItem("token");

	const callbackEndpoint = process.env.CMS_ENDPOINT || "";
	const OIDC_INTUIT_EMAIL_DOMAIN = process.env.OIDC_INTUIT_EMAIL_DOMAIN || "";

	const bodyFormData = new FormData();
	bodyFormData.append("email", email);
	bodyFormData.append("locale", locale);

	if (checkOidcDomain(email, OIDC_INTUIT_EMAIL_DOMAIN)) {
		bodyFormData.append("callbackEndpoint", `${callbackEndpoint}/sign-in/intuit`);
	} else {
		bodyFormData.append("callbackEndpoint", `${callbackEndpoint}/create-account/password`);
	}

	const queryHeaders: Record<string, string | null | undefined> = {
		"content-type": "multipart/form-data",
		"Authorization": "Bearer " + accessToken,
		"x-api-version": API_VERSION,
		"x-account-token": accountToken
	};

	let error = "";
	const queryResp = await axios.request({
		url: `${API_ENDPOINT}/api/invitations`,
		method: "POST",
		data: bodyFormData,
		headers: queryHeaders
	});
	if (queryResp.status === 200) {
		const data = queryResp.data;
		return { data, error };
	}

	error = queryResp.data.message;
	throw new Error(error);
};

export default inviteUser;
