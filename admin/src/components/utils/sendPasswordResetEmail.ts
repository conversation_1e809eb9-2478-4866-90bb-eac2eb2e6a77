import axios from "axios";

const sendPasswordResetEmail = async (locale: string, email: string) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const API_VERSION = process.env.API_VERSION;
	const CMS_ENDPOINT = process.env.CMS_ENDPOINT;

	const requestObject = {
		method: "email/password",
		email: email,
		locale: locale,
		callbackEndpoint: CMS_ENDPOINT
	};

	await axios.request({
		url: `${API_ENDPOINT}/api/auth/forgot-password`,
		method: "POST",
		data: JSON.stringify(requestObject),
		headers: {
			"content-type": "application/json",
			"x-api-version": API_VERSION
		}
	});

	return true;
};

export default sendPasswordResetEmail;
