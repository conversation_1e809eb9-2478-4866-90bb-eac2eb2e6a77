import axios from "axios";

const getVideoJobStatus = async (videoName: string) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const accessToken = localStorage.getItem("accessToken");
	const API_VERSION = process.env.API_VERSION;
	const accountToken = sessionStorage.getItem("token");
	const error = "";

	const queryResp = await axios.request({
		url: `${API_ENDPOINT}/api/videos/job/status/${videoName}`,
		method: "GET",
		headers: {
			"content-type": "application/json",
			Authorization: "Bearer " + accessToken,
			"x-api-version": API_VERSION,
			"x-account-token": accountToken
		}
	});

	const data = queryResp?.data;
	return { data, error };
};

export default getVideoJobStatus;
