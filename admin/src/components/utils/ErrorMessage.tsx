import React from "react";
import { CustomAlert, XmarkIconWhite } from "@src/styles/forms";

interface Props {
	error?: string;
	setError: (value: string) => void;
	displayCloseIcon?: boolean;
	style?: React.CSSProperties;
}

const ErrorMessage: React.FC<Props> = ({ error, setError, displayCloseIcon, style }) => {
	return (
		<CustomAlert data-testid="ErrorMessage" style={style}>
			<XmarkIconWhite onClick={() => setError("")} modedisplay={displayCloseIcon?.toString()} />
			{error}
		</CustomAlert>
	);
};

export default ErrorMessage;
