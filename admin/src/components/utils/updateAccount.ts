import axios from "axios";
import jwt_decode from "jwt-decode";
import { accountToken } from "@src/types/videos";

interface AccountDetailsProps {
	logo: File;
	firstName: string;
	lastName: string;
	companyURL: string;
	companyName?: string;
}

const updateAccount = async ({ logo, firstName, lastName, companyURL, companyName }: AccountDetailsProps) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const accessToken = localStorage.getItem("accessToken");
	const API_VERSION = process.env.API_VERSION;
	const accountToken = sessionStorage.getItem("token");

	if (accessToken && accountToken) {
		const decoded = jwt_decode(accountToken) as accountToken;

		const usersFormData = new FormData();
		if (firstName) {
			usersFormData.append("firstName", firstName);
		}
		if (lastName) {
			usersFormData.append("lastName", lastName);
		}

		await axios.request({
			url: `${API_ENDPOINT}/api/users/${decoded?.userId}`,
			method: "PATCH",
			data: usersFormData,
			headers: {
				"content-type": "multipart/form-data",
				"Authorization": "Bearer " + accessToken,
				"x-api-version": API_VERSION,
				"x-account-token": accountToken
			}
		});

		const accountsFormData = new FormData();
		if (companyURL.indexOf("http://") == 0 || companyURL.indexOf("https://") == 0) {
			accountsFormData.append("companyURL", companyURL);
		} else if (companyURL) {
			accountsFormData.append("companyURL", "https://" + companyURL);
		}

		if (logo) {
			accountsFormData.append("companyLogo", logo);
		}

		if (companyName) {
			accountsFormData.append("companyName", companyName);
		}

		const queryHeaders: Record<string, string | null | undefined> = {
			"content-type": "multipart/form-data",
			"Authorization": "Bearer " + accessToken,
			"x-api-version": API_VERSION,
			"x-account-token": accountToken
		};

		const queryResp = await axios.request({
			url: `${API_ENDPOINT}/api/accounts/${decoded?.account?._id}`,
			method: "PATCH",
			data: accountsFormData,
			headers: queryHeaders
		});

		const data = queryResp?.data;
		return { data };
	}

	const error = "error";
	return { error };
};

export default updateAccount;
