import axios from "axios";

const rejectInvite = async (invitationId: string) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const API_VERSION = process.env.API_VERSION;
	const accessToken = localStorage.getItem("accessToken");

	const inviteFormData = new FormData();
	inviteFormData.append("status", "declined");

	const queryResp = await axios.request({
		url: `${API_ENDPOINT}/api/invitations/${invitationId}`,
		method: "PUT",
		data: inviteFormData,
		headers: {
			"content-type": "multipart/form-data",
			Authorization: "Bearer " + accessToken,
			"x-api-version": API_VERSION
		}
	});

	const data = queryResp?.data;
	return { data };
};

export default rejectInvite;
