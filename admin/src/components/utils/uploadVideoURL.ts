import axios from "axios";

const uploadVideoURL = async (url: string) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const accessToken = localStorage.getItem("accessToken");
	const API_VERSION = process.env.API_VERSION;
	const accountToken = sessionStorage.getItem("token");
	const error = "";

	const requestData = {
		fileUrl: url
	};

	const queryResp = await axios.request({
		url: `${API_ENDPOINT}/api/videos/url`,
		method: "POST",
		data: JSON.stringify(requestData),
		headers: {
			Authorization: "Bearer " + accessToken,
			"Content-Type": "application/json",
			"x-api-version": API_VERSION,
			"x-account-token": accountToken
		}
	});

	const data = queryResp?.data;
	return { data, error };
};

export default uploadVideoURL;
