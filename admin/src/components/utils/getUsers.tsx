import { Invitation, InvitationStatus } from "@src/types/invitations";
import { User, UserWithInvite } from "@src/types/users";
import axios from "axios";

const getUsersWithStatus = async (currentUserId: string) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const API_VERSION = process.env.API_VERSION;
	const accessToken = localStorage.getItem("accessToken");
	const accountToken = sessionStorage.getItem("token");
	const error = "";

	const usersQueryResp = await axios.request({
		url: `${API_ENDPOINT}/api/users`,
		method: "GET",
		headers: {
			"content-type": "application/json",
			Authorization: "Bearer " + accessToken,
			"x-api-version": API_VERSION,
			"x-account-token": accountToken
		}
	});

	const invitationsQueryResp = await axios.request({
		url: `${API_ENDPOINT}/api/invitations`,
		method: "GET",
		headers: {
			"content-type": "application/json",
			Authorization: "Bearer " + accessToken,
			"x-api-version": API_VERSION,
			"x-account-token": accountToken
		}
	});

	const userData: User[] = usersQueryResp?.data?.users;
	const invitationData: Invitation[] = invitationsQueryResp?.data?.invitations;

	const data: UserWithInvite[] = userData.map((user: User) => {
		return {
			...user,
			status: user.isOwner ? InvitationStatus.OWNER : InvitationStatus.ACTIVE,
			isCurrentUser: user._id === currentUserId,
			isInvite: false
		};
	});

	invitationData
		.filter((invitation) => invitation.status === "pending")
		.forEach((invitation: Invitation) => {
			data.push({
				_id: invitation._id,
				createdAt: invitation.createdAt,
				email: invitation.email,
				status: InvitationStatus.PENDING,
				updatedAt: invitation.updatedAt,
				isInvite: true
			});
		});

	return { data, error };
};

export default getUsersWithStatus;
