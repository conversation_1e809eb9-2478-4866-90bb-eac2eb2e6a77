import axios from "axios";

const updateCollection = async (id: string, shoppableVideos: string[]) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const accessToken = localStorage.getItem("accessToken");
	const API_VERSION = process.env.API_VERSION;
	const accountToken = sessionStorage.getItem("token");

	const reqBody = {
		shoppableVideos
	};

	const queryHeaders: Record<string, string | null | undefined> = {
		"content-type": "application/json",
		Authorization: "Bearer " + accessToken,
		"x-api-version": API_VERSION,
		"x-account-token": accountToken
	};

	const queryResp = await axios.request({
		url: `${API_ENDPOINT}/api/shoppable-collections/${id}`,
		method: "PUT",
		data: reqBody,
		headers: queryHeaders
	});

	const data = queryResp?.data;
	return { data };
};

export default updateCollection;
