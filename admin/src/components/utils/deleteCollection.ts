import axios from "axios";

const deleteCollection = async (id: string) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const accessToken = localStorage.getItem("accessToken");
	const API_VERSION = process.env.API_VERSION;
	const accountToken = sessionStorage.getItem("token");
	const error = "";

	const requestObject = {
		videoId: id
	};

	const queryHeaders: Record<string, string | null | undefined> = {
		"content-type": "application/json",
		Authorization: "Bearer " + accessToken,
		"x-api-version": API_VERSION,
		"x-account-token": accountToken
	};

	const queryResp = await axios.request({
		url: `${API_ENDPOINT}/api/shoppable-collections/${id}`,
		method: "DELETE",
		data: JSON.stringify(requestObject),
		headers: queryHeaders
	});

	const data = queryResp?.data;
	return { data, error };
};

export default deleteCollection;
