import axios from "axios";
import jwt_decode from "jwt-decode";
import { accessToken, companyPage, SearchType } from "@src/types/videos";

const getAllCompanies = async (search: string, searchType: SearchType) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const accessToken = localStorage.getItem("accessToken");
	const API_VERSION = process.env.API_VERSION;
	const data = {} as companyPage;
	let error = "";

	if (accessToken) {
		const decoded = jwt_decode(accessToken) as accessToken;

		const queryResp = await axios.request({
			url: `${API_ENDPOINT}/api/users/${decoded?.userId}`,
			method: "GET",
			headers: {
				"content-type": "application/json",
				"Authorization": "Bearer " + accessToken,
				"x-api-version": API_VERSION
			}
		});

		data.email = queryResp?.data?.user?.email ?? "";
		data.firstName = queryResp?.data?.user?.firstName ?? "";
		data.lastName = queryResp?.data?.user?.lastName ?? "";

		const queryResp2 = await axios.request({
			url: `${API_ENDPOINT}/api/accounts/super?searchType=${searchType}&search=` + search,
			method: "GET",
			headers: {
				"content-type": "application/json",
				"Authorization": "Bearer " + accessToken,
				"x-api-version": API_VERSION
			}
		});

		data.accounts = queryResp2?.data.accounts;

		return { data, error };
	}

	error = "error";
	return { data, error };
};

export default getAllCompanies;
