// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const getErrorString = (translation: any, code: string) => {
	switch (code) {
		case "E_INTERNAL_ERROR":
			return translation.errors.internalError;

		case "E_MISSING_EVENT_NAME":
			return translation.errors.missingEventName;

		case "E_MISSING_EVENT_DATA":
			return translation.errors.missingEventData;

		case "E_MISSING_ACCOUNT_ID":
			return translation.errors.missingAccountId;

		case "E_PASSWORD_COMPLEXITY":
			return translation.errors.passwordComplexity;

		case "E_INVALID_TOKEN":
			return translation.errors.invalidToken;

		case "E_ACCOUNT_EXISTS":
			return translation.errors.accountExists;

		case "E_ALREADY_VERIFIED":
			return translation.errors.alreadyVerified;

		case "E_HASH_FAILURE":
			return translation.errors.hashFailure;

		case "E_SERVICE_FAILED":
			return translation.errors.serviceFailed;

		case "E_INVALID_ACCOUNT":
			return translation.errors.invalidAccount;

		case "E_USER_NOT_VERIFIED":
			return translation.errors.userNotVerified;

		case "E_DATABASE_FAILURE":
			return translation.errors.databaseFailure;

		case "E_INVALID_METHOD":
			return translation.errors.invalidMethod;

		case "E_NOT_AUTHENTICATED":
			return translation.errors.notAuthenticated;

		case "E_EMAIL_NOT_FOUND":
			return translation.errors.emailNotFound;

		case "E_MISSING_KEY":
			return translation.errors.missingKey;

		case "E_MISSING_ID":
			return translation.errors.missingId;

		case "E_EMAIL_DELIVERY":
			return translation.errors.emailDelivery;

		case "E_MISSING_TOKEN":
			return translation.errors.missingToken;

		case "E_TOKEN_VERIFICATION":
			return translation.errors.tokenVerification;

		case "E_INVALID_AUTHORIZATION":
			return translation.errors.tokenVerification;

		case "E_MISSING_HASHKEY":
			return translation.errors.missingHashkey;

		case "E_ACCESS_FORBIDDEN":
			return translation.errors.accessForbidden;

		case "E_COLLECTION_NOT_FOUND":
			return translation.errors.collectionNotFound;

		case "E_INVALID_PRODUCT_URL":
			return translation.errors.invalidProductUrl;

		case "E_FILE_WRITE_FAILURE":
			return translation.errors.fileWriteFailure;

		case "E_INVALID_INPUT":
			return translation.errors.invalidInput;

		case "E_OPTIMIZE_ERROR":
			return translation.errors.optimizeImageError;

		case "E_RESOURCE_CONFLICT":
			return translation.errors.videoInUse;

		case "ERR_BAD_REQUEST":
			return translation.errors.igBadRequest;

		case "E_SIGN_UP_EXISTING_EMAIL":
			return translation.errors.signUpExistingEmail;

		case "E_SIGN_UP_INVALID_FORMAT":
			return translation.errors.signUpInvalidFormat;

		case "E_SIGN_UP_GENERIC":
			return translation.errors.signUpGeneric;

		case "E_EMAIL_SIGN_IN_NO_EMAIL":
			return translation.errors.emailSignInNoEmail;

		case "E_EMAIL_SIGN_IN_INVALID_FORMAT":
			return translation.errors.emailSignInInvalidFormat;

		case "E_EMAIL_SIGN_IN_GENERIC":
			return translation.errors.emailSignInGeneric;

		case "E_SIGN_IN_INCORRECT":
			return translation.errors.signInIncorrect;

		case "E_EMAIL_SIGN_INVALID_FORMAT":
			return translation.errors.emailSignInvalidFormat;

		case "E_SIGN_IN_GENERIC":
			return translation.errors.signInGeneric;

		case "E_FORGOT_PASSWORD_NO_EMAIL":
			return translation.errors.forgotPasswordNoEmail;

		case "E_FORGOT_PASSWORD_GENERIC":
			return translation.errors.forgotPasswordGeneric;

		case "E_VIDEO_LIBRARY_FILE_TOO_LARGE":
			return translation.errors.videoLibraryFileTooLarge;

		case "E_VIDEO_LIBRARY_WRONG_FILE_TYPE":
			return translation.errors.videoLibraryWrongFileType;

		case "E_VIDEO_LIBRARY_VIDEO_NOT_PROCESSED":
			return translation.errors.videoLibraryVideoNotProcessed;

		case "E_VIDEO_LIBRARY_CANT_LOAD_VIDEOS":
			return translation.errors.videoLibraryCantLoadVideos;

		case "E_VIDEO_LIBRARY_GENERIC":
			return translation.errors.videoLibraryGeneric;

		case "E_VIDEO_MANAGER_CANT_LOAD_VIDEOS":
			return translation.errors.videoManagerCantLoadVideos;

		case "E_VIDEO_MANAGER_CANT_REORDER":
			return translation.errors.videoManagerCantReorder;

		case "E_VIDEO_MANAGER_GENERIC":
			return translation.errors.videoManagerGeneric;

		case "E_VIDEO_DETAIL_NO_TITLE":
			return translation.errors.videoDetailNoTitle;

		case "E_VIDEO_DETAIL_NO_VIDEO":
			return translation.errors.videoDetailNoVideo;

		case "E_VIDEO_DETAIL_NO_TITLE_LINK":
			return translation.errors.videoDetailNoTitleLink;

		case "E_VIDEO_DETAIL_NO_LINK_IMAGE":
			return translation.errors.videoDetailNoLinkImage;

		case "E_VIDEO_DETAIL_FILE_SIZE":
			return translation.errors.videoDetailFileSize;

		case "E_VIDEO_DETAIL_WRONG_FORMAT":
			return translation.errors.videoDetailWrongFormat;

		case "E_VIDEO_DETAIL_CANT_PROCESS":
			return translation.errors.videoDetailCantProcess;

		case "E_VIDEO_DETAIL_CANT_SAVE":
			return translation.errors.videoDetailCantSave;

		case "E_VIDEO_DETAIL_GENERIC":
			return translation.errors.videoDetailGeneric;

		case "E_COLLECTIONS_CANT_LOAD":
			return translation.errors.collectionsCantLoad;

		case "E_COLLECTIONS_CANT_DELETE":
			return translation.errors.collectionsCantDelete;

		case "E_COLLECTIONS_GENERIC":
			return translation.errors.collectionsGeneric;

		case "E_COLLECTION_DETAIL_CANT_LOAD":
			return translation.errors.collectionDetailCantLoad;

		case "E_COLLECTION_DETAIL_CANT_REORDER":
			return translation.errors.collectionDetailCantReorder;

		case "E_COLLECTION_DETAIL_CANT_SAVE":
			return translation.errors.collectionDetailCantSave;

		case "E_COLLECTION_DETAIL_CANT_ADD_VIDEOS":
			return translation.errors.collectionDetailCantAddVideos;

		case "E_COLLECTION_DETAIL_GENERIC":
			return translation.errors.collectionDetailGeneric;

		case "E_PROFILE_CANT_LOAD":
			return translation.errors.profileCantLoad;

		case "E_PROFILE_CANT_SAVE":
			return translation.errors.profileCantSave;

		case "E_PROFILE_GENERIC":
			return translation.errors.profileGeneric;

		case "E_PROFILE_CANT_SEND_RESET":
			return translation.errors.profileCantSendReset;

		case "E_SETTINGS_CANT_LOAD":
			return translation.errors.settingsCantLoad;

		case "E_SETTINGS_CANT_SAVE":
			return translation.errors.settingsCantSave;

		case "E_SETTINGS_GENERIC":
			return translation.errors.settingsGeneric;

		case "E_SETTINGS_CANT_INVITE_USER":
			return translation.errors.settingsCantInviteUser;

		case "E_SETTINGS_INVALID_INVITE_EMAIL":
			return translation.errors.settingsInvalidInviteEmail;

		case "E_SETTINGS_CANT_DELETE_USER":
			return translation.errors.settingsCantDeleteUser;

		case "E_SETTINGS_WRONG_FILE_TYPE":
			return translation.errors.settingsWrongFileType;

		case "E_PERFORMANCE_CANT_LOAD":
			return translation.errors.performanceCantLoad;

		case "E_PERFORMANCE_CANT_REFRESH":
			return translation.errors.performanceCantRefresh;

		case "E_PERFORMANCE_NO_AVAILABLE_DATA":
			return translation.errors.performanceNoAvailableData;

		case "E_PERFORMANCE_TIMEOUT":
			return translation.errors.performanceTimeout;

		case "E_PERFORMANCE_GENERIC":
			return translation.errors.performanceGeneric;

		case "E_GENERAL_ERROR_CANT_LOAD":
			return translation.errors.generalErrorCantLoad;

		case "E_GENERAL_ERROR_GENERIC":
			return translation.errors.generalErrorGeneric;

		case "E_GENERAL_ERROR_SIGNED_OUT":
			return translation.errors.generalErrorSignedOut;

		case "E_AUTH_METHOD_NOT_SUPPORTED":
			return translation.errors.authMethodNotSupported;

		case "E_AUTH_EXISTS_DUPLICIATE":
			return translation.errors.authDuplication;

		case "E_USER_LIMIT_REACHED":
			return translation.errors.userLimitReached;

		case "E_EMAIL_ALREADY_EXISTS":
			return translation.errors.emailAlreadyExists;

		case "E_EMAIL_ALREADY_INVITED":
			return translation.errors.emailAlreadyInvited;

		default:
			return translation.errors.unexpectedError;
	}
};
