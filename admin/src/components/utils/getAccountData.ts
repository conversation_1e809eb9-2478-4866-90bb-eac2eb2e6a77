import axios from "axios";
import jwt_decode from "jwt-decode";
import { accountToken } from "@src/types/videos";
import { Account } from "@src/types/account";

const getAccountData = async () => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const API_VERSION = process.env.API_VERSION;
	const accessToken = localStorage.getItem("accessToken");
	const accountToken = sessionStorage.getItem("token");
	let accountData: Account | null = null;

	if (accessToken && accountToken) {
		const decoded = jwt_decode(accountToken) as accountToken;
		const queryResp = await axios.request({
			url: `${API_ENDPOINT}/api/accounts/${decoded?.account?._id}`,
			method: "GET",
			headers: {
				"content-type": "application/json",
				Authorization: "Bearer " + accessToken,
				"x-api-version": API_VERSION,
				"x-account-token": accountToken
			}
		});

		accountData = queryResp?.data?.account || null;
	}

	return { data: accountData };
};

export default getAccountData;
