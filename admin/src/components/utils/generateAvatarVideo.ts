import axios from "axios";
import { GenerateAvatarVideoOptions } from "@src/types/avatar";

const generateAvatarVideo = async (options: GenerateAvatarVideoOptions) => {
	try {
		const API_ENDPOINT = process.env.API_ENDPOINT;
		const API_VERSION = process.env.API_VERSION;
		const accessToken = localStorage.getItem("accessToken");
		const accountToken = sessionStorage.getItem("token");

		const requestBody = {
			lookId: options.lookId,
			voiceId: options.voiceId,
			text: options.text
		};

		const response = await axios.request({
			url: `${API_ENDPOINT}/api/avatar`,
			method: "POST",
			data: requestBody,
			headers: {
				"Authorization": "Bearer " + accessToken,
				"x-api-version": API_VERSION,
				"x-account-token": accountToken,
				"Accept": "application/json",
				"Content-Type": "application/json"
			}
		});

		return { data: response.data, error: null };
	} catch (error) {
		return { data: null, error: "Error generating video" };
	}
};

export default generateAvatarVideo;
