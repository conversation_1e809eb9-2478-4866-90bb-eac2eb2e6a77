import axios from "axios";

const getVideos = async () => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const API_VERSION = process.env.API_VERSION;
	const accessToken = localStorage.getItem("accessToken");
	const accountToken = sessionStorage.getItem("token");
	const error = "";

	const queryResp = await axios.request({
		url: `${API_ENDPOINT}/api/shoppable-videos`,
		method: "GET",
		headers: {
			"content-type": "application/json",
			Authorization: "Bearer " + accessToken,
			"x-api-version": API_VERSION,
			"x-account-token": accountToken
		}
	});

	const data = queryResp?.data?.shoppableVideos;
	return { data, error };
};

export default getVideos;
