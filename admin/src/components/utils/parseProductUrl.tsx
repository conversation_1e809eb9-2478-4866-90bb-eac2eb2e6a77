import axios from "axios";

const parseProductUrl = async (productUrl: string) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const accessToken = localStorage.getItem("accessToken");
	const accountToken = sessionStorage.getItem("token");
	const API_VERSION = process.env.API_VERSION;

	let error = "";
	let data = { title: null, imageURL: null };

	if (!accessToken) return { data, error: "No access token" };
	if (!productUrl) return { data, error };

	try {
		const queryResp = await axios.request({
			url: `${API_ENDPOINT}/api/shoppable-videos/fetch-product?url=${productUrl}`,
			method: "GET",
			headers: {
				"content-type": "application/json",
				"Authorization": "Bearer " + accessToken,
				"x-api-version": API_VERSION,
				"x-account-token": accountToken
			}
		});

		if (queryResp.status === 200) {
			data = queryResp.data;
			return { data, error };
		}

		error = queryResp.data.message;
		throw new Error(error);
	} catch (error) {
		return { data, error };
	}
};

export default parseProductUrl;
