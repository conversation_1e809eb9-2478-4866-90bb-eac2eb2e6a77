import axios from "axios";

const getCollectionById = async (id: string) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const accessToken = localStorage.getItem("accessToken");
	const API_VERSION = process.env.API_VERSION;
	const accountToken = sessionStorage.getItem("token");
	const error = "";

	const queryResp = await axios.request({
		url: `${API_ENDPOINT}/api/shoppable-collections/${id}`,
		method: "GET",
		headers: {
			Authorization: "Bearer " + accessToken,
			"content-type": "application/json",
			"x-api-version": API_VERSION,
			"x-account-token": accountToken
		}
	});
	const data = queryResp?.data?.shoppableCollection;
	return { data, error };
};

export default getCollectionById;
