import axios from "axios";

const postSignup = async (firstName: string, companyName: string, teamOption?: string, platformOption?: string) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const accessToken = localStorage.getItem("accessToken");
	const API_VERSION = process.env.API_VERSION;
	const accountToken = sessionStorage.getItem("token");

	if (accessToken && accountToken) {
		const queryHeaders: Record<string, string | null | undefined> = {
			"content-type": "application/json",
			Authorization: "Bearer " + accessToken,
			"x-api-version": API_VERSION,
			"x-account-token": accountToken
		};

		const requestObject = {
			name: firstName,
			companyName: companyName,
			team: teamOption,
			platform: platformOption
		};

		const queryResp = await axios.request({
			url: `${API_ENDPOINT}/api/accounts/users/post-signup`,
			method: "POST",
			data: JSON.stringify(requestObject),
			headers: queryHeaders
		});

		const data = queryResp?.data;
		return { data };
	}

	return { error: "error" };
};

export default postSignup;
