import axios from "axios";
import jwt_decode from "jwt-decode";
import { accessToken, accountToken, companyDetails } from "@src/types/videos";
import { ProductTypeEnum } from "@src/types/product";

const getAccount = async () => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const accessToken = localStorage.getItem("accessToken");
	const API_VERSION = process.env.API_VERSION;
	const accountToken = sessionStorage.getItem("token");
	const data = {} as companyDetails;
	let error = "";

	if (accessToken && accountToken) {
		const decoded = jwt_decode(accessToken) as accessToken;
		const queryResp = await axios.request({
			url: `${API_ENDPOINT}/api/users/${decoded?.userId}`,
			method: "GET",
			headers: {
				"content-type": "application/json",
				"Authorization": "Bearer " + accessToken,
				"x-api-version": API_VERSION,
				"x-account-token": accountToken
			}
		});

		data.email = queryResp?.data?.user?.email ?? "";
		data.firstName = queryResp?.data?.user?.firstName ?? "";
		data.lastName = queryResp?.data?.user?.lastName ?? "";
		data.isPasswordSet = true;

		if (queryResp.data?.user && "isPasswordSet" in queryResp.data.user) {
			data.isPasswordSet = queryResp.data.user.isPasswordSet;
		}

		const decoded2 = jwt_decode(accountToken) as accountToken;
		const queryResp2 = await axios.request({
			url: `${API_ENDPOINT}/api/accounts/${decoded2?.account?._id}`,
			method: "GET",
			headers: {
				"content-type": "application/json",
				"Authorization": "Bearer " + accessToken,
				"x-api-version": API_VERSION,
				"x-account-token": accountToken
			}
		});

		data.companyName = queryResp2?.data?.account?.companyName ?? "";
		data.companyLogo = queryResp2?.data?.account?.companyLogo ?? "";
		data.companyURL = queryResp2?.data?.account?.companyURL ?? "";
		data.vanityBranding = queryResp2?.data?.account?.subscription?.hideVanityBranding ?? false;
		data.type = queryResp2?.data?.account?.subscription?.type ?? ProductTypeEnum.BASIC;
		data.isAccountOwner = queryResp2.data.account.ownerUserId === decoded.userId;

		return { data, error };
	}

	error = "error";
	return { data, error };
};

export default getAccount;
