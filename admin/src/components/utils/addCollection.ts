import axios from "axios";
import { shoppableVideo } from "@src/types/videos";

interface RequestObject {
	title: string;
	shoppableVideos: string[];
	buttonBackgroundColor?: string;
	buttonBackgroundBlur?: boolean;
	iconTextColor?: string;
	displayFont?: string;
	carouselBorderRadius?: number;
	carouselIsCentered?: boolean;
	carouselMargin?: number;
	carouselGap?: number;
	widgetBorderRadius?: number;
	widgetPosition?: string;
	inlineBorderRadius?: number;
}

interface AddCollectionProps {
	collectionTitle: string,
	videos: shoppableVideo[],
	buttonBackgroundColor?: string,
	buttonBackgroundBlur?: boolean,
	iconTextColor?: string,
	selectedFont?: string,
	borderRadius?: number,
	centerOnPage?: boolean,
	margin?: number,
	paddingBetween?: number,
	widgetBorderRadius?: number,
	widgetPosition?: string,
	inlineBorderRadius?: number
}

const addCollection = async ({
	collectionTitle,
	videos,
	buttonBackgroundColor,
	buttonBackgroundBlur,
	iconTextColor,
	selectedFont,
	borderRadius,
	centerOnPage,
	margin,
	paddingBetween,
	widgetBorderRadius,
	widgetPosition,
	inlineBorderRadius
} : AddCollectionProps) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const API_VERSION = process.env.API_VERSION;
	const accessToken = localStorage.getItem("accessToken");
	const accountToken = sessionStorage.getItem("token");
	const videoIds: string[] = [];

	videos?.map((x) => {
		videoIds.push(x._id);
	});

	const requestObject: RequestObject = {
		title: collectionTitle,
		shoppableVideos: videoIds
	};

	if (buttonBackgroundColor) {
		requestObject.buttonBackgroundColor = buttonBackgroundColor;
	}

	if (buttonBackgroundBlur) {
		requestObject.buttonBackgroundBlur = true;
	} else {
		requestObject.buttonBackgroundBlur = false;
	}

	if (iconTextColor) {
		requestObject.iconTextColor = iconTextColor;
	}

	if (selectedFont) {
		requestObject.displayFont = selectedFont;
	}

	if (borderRadius !== undefined) {
		requestObject.carouselBorderRadius = borderRadius;
	}

	if (centerOnPage) {
		requestObject.carouselIsCentered = true;
	} else {
		requestObject.carouselIsCentered = false;
	}

	if (margin !== undefined) {
		requestObject.carouselMargin = margin;
	}

	if (paddingBetween !== undefined) {
		requestObject.carouselGap = paddingBetween;
	}

	if (widgetBorderRadius !== undefined) {
		requestObject.widgetBorderRadius = widgetBorderRadius;
	}

	if (widgetPosition) {
		requestObject.widgetPosition = widgetPosition;
	}

	if (inlineBorderRadius !== undefined) {
		requestObject.inlineBorderRadius = inlineBorderRadius;
	}

	const queryHeaders: Record<string, string | null | undefined> = {
		"content-type": "application/json",
		Authorization: "Bearer " + accessToken,
		"x-api-version": API_VERSION,
		"x-account-token": accountToken
	};

	const queryResp = await axios.request({
		url: `${API_ENDPOINT}/api/shoppable-collections`,
		method: "POST",
		data: JSON.stringify(requestObject),
		headers: queryHeaders
	});

	const data = queryResp?.data;
	return { data };
};

export default addCollection;
