import axios from "axios";

const getTopCollections = async () => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const API_VERSION = process.env.API_VERSION;
	const accessToken = localStorage.getItem("accessToken");
	const accountToken = sessionStorage.getItem("token");

	const queryResp = await axios.request({
		url: `${API_ENDPOINT}/api/shoppable-collections?sortKey=orderCount&SortBy=dsc`,
		method: "GET",
		headers: {
			Authorization: "Bearer " + accessToken,
			"x-api-version": API_VERSION,
			"x-account-token": accountToken
		}
	});

	const data = queryResp?.data;
	return { data: data };
};

export default getTopCollections;
