import axios from "axios";
import { FileInput } from "@src/types/videos";
import { CaptionData } from "@src/types/caption";

interface AddVideoProps {
	videoTitleValue: string;
	videoDescValue: string;
	uploadFileID: string;
	videoButtonTextValue: string;
	displayFormat: string;
	coverImageLink: string;
	gifURL: string;
	videoPosterPlayEmbedURL: string;
	productsInputs?: FileInput[];
	displayTitle?: boolean;
	emailAddress?: string;
	phoneNumber?: string;
	captionData?: CaptionData;
}

const addVideo = async ({
	videoTitleValue,
	videoDescValue,
	uploadFileID,
	videoButtonTextValue,
	displayFormat,
	coverImageLink,
	gifURL,
	videoPosterPlayEmbedURL,
	productsInputs,
	displayTitle,
	emailAddress,
	phoneNumber,
	captionData
}: AddVideoProps) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const API_VERSION = "4";
	const accessToken = localStorage.getItem("accessToken");
	const accountToken = sessionStorage.getItem("token");

	const bodyFormData = new FormData();
	bodyFormData.append("title", videoTitleValue);
	bodyFormData.append("description", videoDescValue);
	bodyFormData.append("videoId", uploadFileID);
	bodyFormData.append("ctaText", videoButtonTextValue);
	bodyFormData.append("showTitle", displayTitle ? displayTitle.toString() : "false");
	bodyFormData.append("videoPosterImageURL", coverImageLink);
	bodyFormData.append("gifURL", gifURL);
	bodyFormData.append("videoPosterPlayEmbedURL", videoPosterPlayEmbedURL);
	bodyFormData.append("videoDisplayMode", displayFormat);

	if (captionData) {
		bodyFormData.append("captionData", JSON.stringify(captionData));
	}

	productsInputs?.map((x, i) => {
		if (x.productTitle != "" && x.productURL != "") {
			bodyFormData.append("productTitle" + i, x.productTitle);
			bodyFormData.append("productURL" + i, x.productURL);
			if (x.subTitle) bodyFormData.append("subTitle" + i, x.subTitle);
			if (x.productImageFile) bodyFormData.append("productImageFile" + i, x.productImageFile as File);
			if (x.imageURL) bodyFormData.append("productImageURL" + i, x.imageURL);
		}
	});

	if (emailAddress) {
		bodyFormData.append("email", emailAddress);
	}

	if (phoneNumber) {
		const cleanedPhoneNumber = phoneNumber.replace(/\D/g, "");
		bodyFormData.append("phone", `+1${cleanedPhoneNumber}`);
	}

	const queryHeaders: Record<string, string | null | undefined> = {
		"content-type": "multipart/form-data",
		"Authorization": "Bearer " + accessToken,
		"x-api-version": API_VERSION,
		"x-account-token": accountToken
	};

	const queryResp = await axios.request({
		url: `${API_ENDPOINT}/api/shoppable-videos`,
		method: "POST",
		data: bodyFormData,
		headers: queryHeaders
	});

	const data = queryResp?.data;
	return { data };
};

export default addVideo;
