import axios from "axios";

const updateSubscription = async (priceId: string, subscriptionId?: string) => {
	const API_ENDPOINT = process.env.API_ENDPOINT;
	const accessToken = localStorage.getItem("accessToken");
	const accountToken = sessionStorage.getItem("token");

	if (accessToken && accountToken && subscriptionId) {
		const queryHeaders: Record<string, string | null | undefined> = {
			"content-type": "application/json",
			"Authorization": "Bearer " + accessToken,
			"x-api-version": "3",
			"x-account-token": accountToken
		};

		const requestObject = {
			priceId: priceId
		};

		const queryResp = await axios.request({
			url: `${API_ENDPOINT}/api/subscriptions/` + subscriptionId,
			method: "PUT",
			data: JSON.stringify(requestObject),
			headers: queryHeaders
		});

		const data = queryResp?.data;
		return { data };
	}

	const error = "error";
	return { error };
};

export default updateSubscription;
