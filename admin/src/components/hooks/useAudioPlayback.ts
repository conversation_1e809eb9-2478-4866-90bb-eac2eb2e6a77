import {
	useState,
	useCallback
} from "react";

export const useAudioPlayback = (audioRef: React.RefObject<HTMLAudioElement>, recordedDuration?: number) => {
	const [isPlaying, setIsPlaying] = useState(false);
	const [playTime, setPlayTime] = useState(0);

	const handlePlay = useCallback(() => {
		if (audioRef.current) {
			audioRef.current.currentTime = 0;
			audioRef.current.play();
			setIsPlaying(true);
		}
	}, [audioRef]);

	const handleStop = useCallback(() => {
		if (audioRef.current) {
			audioRef.current.pause();
			setIsPlaying(false);
		}
	}, [audioRef]);

	const handleAudioTimeUpdate = useCallback(() => {
		if (audioRef.current) {
			setPlayTime(audioRef.current.currentTime);
		}
	}, [audioRef]);

	const handleAudioEnded = useCallback(() => {
		setIsPlaying(false);
		setPlayTime(0);
	}, []);

	const getProgressBarWidth = useCallback(() => {
		if (!recordedDuration || !playTime) return 0;
		const percent = (playTime / recordedDuration) * 100;
		return Math.min(Math.max(percent, 0), 100);
	}, [playTime, recordedDuration]);

	const resetPlaybackState = useCallback(() => {
		setIsPlaying(false);
		setPlayTime(0);
	}, []);

	return {
		isPlaying,
		playTime,
		handlePlay,
		handleStop,
		handleAudioTimeUpdate,
		handleAudioEnded,
		getProgressBarWidth,
		resetPlaybackState,
		setPlayTime
	};
};
