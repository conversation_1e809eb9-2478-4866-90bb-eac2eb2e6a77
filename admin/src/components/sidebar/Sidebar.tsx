import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
	CollectionsIcon,
	AvatarIcon,
	ProfileIcon,
	ShoppableVideosIcon,
	SettingsIcon,
	StarIcon,
	SidebarCircle,
	HomeIcon,
	LibraryIcon,
	HelpIcon
} from "@src/assets";
import {
	ApNavLogo,
	NavIcon,
	SidebarContainer,
	SidebarNotch,
	SidebarCircleContainer,
	StyledNavItem,
	ChevronRight
} from "@src/styles/components";
import { SignOutModal } from "../modals/SignOutModal";
import { useTranslation } from "../hooks/translations";
import { Account } from "../../types/account";
import { ProductTypeEnum } from "../../types/product";
import jwt_decode from "jwt-decode";
import { accessToken } from "@src/types/videos";
import SVG from "../svg/SVG";
import { faChevronRight } from "@fortawesome/free-solid-svg-icons";

interface Props {
	saveChanges?: boolean;
	setNavigationUrl?: (value: string) => void;
	setModalStatus?: (value: boolean) => void;
	accountData: Account | undefined;
}

const Sidebar: React.FC<Props> = ({ saveChanges, setNavigationUrl, setModalStatus, accountData }) => {
	const translation = useTranslation();
	const [isExpanded, setIsExpanded] = useState(false);
	const [arrowClicked, setArrowClicked] = useState(false);
	const [signOutModalStatus, setSignOutModalStatus] = useState(false);
	const navigate = useNavigate();
	let isSuperAdmin = false;

	const accessToken = localStorage.getItem("accessToken");
	if (accessToken) {
		const decoded = jwt_decode(accessToken) as accessToken;
		if (decoded.super) {
			isSuperAdmin = true;
		}
	}

	const handleNavigation = (newUrl: string) => {
		if (setModalStatus && setNavigationUrl && saveChanges) {
			setNavigationUrl(newUrl);
			setModalStatus(true);
		} else {
			navigate(newUrl);
			setIsExpanded(false);
			setArrowClicked(false);
		}
	};

	return (
		<>
			<span
				onMouseEnter={() => {
					setIsExpanded(true);
				}}
				onMouseLeave={() => {
					setIsExpanded(false);
				}}
			>
				<SidebarCircleContainer
					isExpanded={isExpanded}
					arrowClicked={arrowClicked}
					onClick={() => {
						setIsExpanded(true);
						setArrowClicked(true);
					}}
				>
					<SidebarNotch>
						<SVG src={SidebarCircle} />
					</SidebarNotch>
					<ChevronRight>
						<SVG src={faChevronRight} />
					</ChevronRight>
				</SidebarCircleContainer>
				<SidebarContainer isExpanded={isExpanded} arrowClicked={arrowClicked}>
					<a className="nav-item">
						<StyledNavItem
							isExpanded={isExpanded}
							arrowClicked={arrowClicked}
							noHover={true}
							style={{ paddingLeft: "13px", paddingBottom: "56px", paddingTop: "56px" }}
						>
							<ApNavLogo isExpanded={isExpanded} arrowClicked={arrowClicked} />
						</StyledNavItem>
					</a>
					<a
						className="nav-close"
						onClick={() => {
							setIsExpanded(false);
							setArrowClicked(false);
						}}
					>
						&times;
					</a>
					<a
						className="nav-item"
						data-testid="homeButton"
						onClick={() => {
							handleNavigation("/");
						}}
					>
						<StyledNavItem isExpanded={isExpanded} arrowClicked={arrowClicked}>
							<NavIcon>
								<SVG src={HomeIcon} alt={translation.general.home} />
							</NavIcon>
							<span className="nav-text">{translation.general.home}</span>
						</StyledNavItem>
					</a>
					<a
						className="nav-item"
						data-testid="shoppableVideosButton"
						onClick={() => {
							handleNavigation("/videos");
						}}
					>
						<StyledNavItem isExpanded={isExpanded} arrowClicked={arrowClicked}>
							<NavIcon>
								<SVG src={ShoppableVideosIcon} alt={translation.general.videos} />
							</NavIcon>
							<span className="nav-text">{translation.general.videos}</span>
						</StyledNavItem>
					</a>
					<a
						className="nav-item"
						data-testid="collectionsButton"
						onClick={() => {
							handleNavigation("/collections");
						}}
					>
						<StyledNavItem isExpanded={isExpanded} arrowClicked={arrowClicked}>
							<NavIcon>
								<SVG src={CollectionsIcon} alt={translation.general.collections} />
							</NavIcon>
							<span className="nav-text">{translation.general.collections}</span>
						</StyledNavItem>
					</a>
					<a
						className="nav-item"
						data-testid="videoLibraryButton"
						onClick={() => {
							handleNavigation("/video-library");
						}}
					>
						<StyledNavItem isExpanded={isExpanded} arrowClicked={arrowClicked}>
							<NavIcon>
								<SVG src={LibraryIcon} alt={translation.general.library} />
							</NavIcon>
							<span className="nav-text">{translation.general.library}</span>
						</StyledNavItem>
					</a>
					{process.env.SHOW_PLANS_PAGE === "true" &&
						(accountData?.subscription?.type === ProductTypeEnum.BASIC && (
							<a
								className="nav-item"
								data-testid="unlockProButton"
								onClick={() => {
									navigate("/plans-pricing");
								}}
							>
								<StyledNavItem isExpanded={isExpanded} arrowClicked={arrowClicked}>
									<NavIcon>
										<SVG src={StarIcon} alt={translation.general.unlockPro} />
									</NavIcon>
									<span className="nav-text">{translation.general.unlockPro}</span>
								</StyledNavItem>
							</a>
						))
					}
					{isSuperAdmin && (
						<a
							className="nav-item"
							onClick={() => {
								handleNavigation("/add-account");
							}}
						>
							<StyledNavItem isExpanded={isExpanded} arrowClicked={arrowClicked}>
								<NavIcon>
									<SVG src={LibraryIcon} alt={translation.general.createAccount} />
								</NavIcon>
								<span className="nav-text">{translation.general.createAccount}</span>
							</StyledNavItem>
						</a>
					)}
					{accountData?.subscription?.allowRecordVideo && (
						<a
							className="nav-item"
							data-testid="recordVideoButton"
							onClick={() => {
								handleNavigation("/record-video");
							}}
						>
							<StyledNavItem isExpanded={isExpanded} arrowClicked={arrowClicked}>
								<NavIcon>
									<SVG src={HelpIcon} alt={translation.general.recordVideo} />
								</NavIcon>
								<span className="nav-text">{translation.general.recordVideo}</span>
							</StyledNavItem>
						</a>
					)}
					{accountData?.subscription?.allowAvatars && (
						<a
							className="nav-item"
							data-testid="avatarsButton"
							onClick={() => {
								handleNavigation("/video-avatars");
							}}
						>
							<StyledNavItem isExpanded={isExpanded} arrowClicked={arrowClicked}>
								<NavIcon>
									<SVG src={AvatarIcon} alt={translation.general.avatars} />
								</NavIcon>
								<span className="nav-text">{translation.general.avatars}</span>
							</StyledNavItem>
						</a>
					)}
					<div style={{ position: "absolute", bottom: "5%", width: "100%" }}>
						<a
							className="nav-item"
							data-testid="profileButton"
							onClick={() => {
								handleNavigation("/profile");
							}}
						>
							<StyledNavItem isExpanded={isExpanded} arrowClicked={arrowClicked}>
								<NavIcon>
									<SVG src={ProfileIcon} alt={translation.general.profile} />
								</NavIcon>
								<span className="nav-text">{translation.general.profile}</span>
							</StyledNavItem>
						</a>
						<a
							className="nav-item"
							data-testid="settingsButton"
							onClick={() => {
								handleNavigation("/account-settings");
							}}
						>
							<StyledNavItem isExpanded={isExpanded} arrowClicked={arrowClicked}>
								<NavIcon>
									<SVG src={SettingsIcon} alt={translation.general.settings} />
								</NavIcon>
								<span className="nav-text">{translation.general.settings}</span>
							</StyledNavItem>
						</a>
					</div>
				</SidebarContainer>
			</span>
			<SignOutModal visible={signOutModalStatus} onCancel={() => setSignOutModalStatus(false)} />
		</>
	);
};

export default Sidebar;
