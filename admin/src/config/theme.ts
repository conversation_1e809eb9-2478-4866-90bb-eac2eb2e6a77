import Color from "color";
import { isValidColor } from "@src/utils/colors";
import { AccountTheme } from "@src/types/account";

export interface apTheme {
	fonts: {
		family: string;
	};
	vendorName: string;
	colors: {
		apWhite: string;
		apBackgroundColor: string;
		apOffBlack: string;
		apLowMedGrey: string;
		apFormText: string;
		apSpanColor: string;
		apSuccess: string;
		apError: string;
		apMaxImpressions: string;
		apButton: string;
		apButtonColor: string;
		apButtonHover: string;
		apButtonActive: string;
		logoBackground: string;
		apButtonShadow: string;
		apThirdButton: string;
		apThirdButtonColor: string;
		apThirdButtonHover: string;
		apThirdButtonActive: string;
		apGreyButton: string;
		apGreyButtonColor: string;
		apGreyButtonHover: string;
		apGreyButtonActive: string;
		apInputColor: string;
		apTextColor: string;
		apSecondaryTextColor: string;
		disabledTextColor: string;
		apSectionBackground: string;
		apInputBackground: string;
		apModalBackground: string;
		snippetBackground: string;
		snippetBorder: string;
		snippetModalTextColor: string;
		snippetModalBackground: string;
		apCopyIcon: string;
		apCopyIconHover: string;
		apCopyIconActive: string;
		apInputBorderColorDark: string;
		apFooterTextColor: string;
		disabledInput: string;
		modalBorderColor: string;
		inputBorderColor: string;
		inputBorderBoxShadow: string;
		scoreBarLevel1: string;
		scoreBarLevel2: string;
		scoreBarLevel3: string;
		scoreBarLevel4: string;
		scoreBarLevel5: string;
		apMedGrey: string;
		apYellow: string;
		apBlue: string;
		apGreen: string;
		apBlack: string;
		trialColor: string;
		trialBackground: string;
		labelBackground: string;
		labelBorder: string;
		proLabelBackground: string;
		proLabelBorder: string;
		apButtonRed: string;
		loadingBarColor: string;
		loadingBarBackgroundColor: string;
		apProgressBarBGColor: string;
		stepActive: string;
		stepInactive: string;
	};
}

const getTheme = (accountTheme: AccountTheme | null):apTheme => {
	const primarySiteColor = accountTheme && isValidColor(accountTheme.primarySiteColor) ? accountTheme.primarySiteColor : "#000000";
	const backgroundColor = accountTheme && isValidColor(accountTheme.backgroundColor) ? accountTheme.backgroundColor : "#FFFFFF";
	const primaryTextColor = accountTheme && isValidColor(accountTheme.primaryTextColor) ? accountTheme.primaryTextColor : "#343434";
	const secondaryTextColor = accountTheme && isValidColor(accountTheme.secondaryTextColor) ? accountTheme.secondaryTextColor : "#969595";
	const buttonTextColor = accountTheme && isValidColor(accountTheme.buttonTextColor) ? accountTheme.buttonTextColor : "#FFFFFF";
	const disabledTextColor = accountTheme && isValidColor(accountTheme.disabledTextColor) ? accountTheme.disabledTextColor : "#9F9F9F";
	const contentBoxColor = accountTheme && isValidColor(accountTheme.contentBoxColor) ? accountTheme.contentBoxColor : "#F8F8F8";
	const isBackgroundDarker = Color(backgroundColor).luminosity() < Color(contentBoxColor).luminosity();
	const greyButtonBase = isBackgroundDarker ? Color(contentBoxColor).lighten(0.25).hex() : Color(contentBoxColor).darken(0.25).hex();

	const theme:apTheme = {
		fonts: {
			family: "'Readex Pro', sans-serif"
		},
		vendorName: "Retail",
		colors: {
			apWhite: "#FFFFFF",
			apBackgroundColor: backgroundColor,
			apOffBlack: "#A1A1A1",
			apLowMedGrey: "#DEDDDD",
			apFormText: primaryTextColor,
			apSpanColor: primarySiteColor,
			apSuccess: "#6DCE49",
			apError: "#E01111",
			apMaxImpressions: "#DC3545",
			apButton: primarySiteColor,
			apButtonColor: buttonTextColor,
			apButtonHover: primarySiteColor !== "#000000" ? Color(primarySiteColor).darken(0.25).hex() : Color(primarySiteColor).lightness(25).hex(),
			apButtonActive: primarySiteColor !== "#000000" ? Color(primarySiteColor).darken(0.25).hex() : Color(primarySiteColor).lightness(25).hex(),
			logoBackground: primarySiteColor,
			apButtonShadow: "#00000029",
			apThirdButton: backgroundColor,
			apThirdButtonColor: primaryTextColor,
			apThirdButtonHover: Color(primaryTextColor).darken(0.5).hex(),
			apThirdButtonActive: secondaryTextColor,
			apGreyButton: greyButtonBase,
			apGreyButtonColor: primaryTextColor,
			apGreyButtonHover: isBackgroundDarker ? Color(greyButtonBase).lighten(0.3).hex() : Color(greyButtonBase).darken(0.3).hex(),
			apGreyButtonActive: greyButtonBase,
			apInputColor: primaryTextColor,
			apTextColor: primaryTextColor,
			apSecondaryTextColor: secondaryTextColor,
			disabledTextColor: disabledTextColor,
			apSectionBackground: contentBoxColor,
			apInputBackground: contentBoxColor,
			apModalBackground: "#00000060",
			snippetBackground: "#343434",
			snippetBorder: "#707070",
			snippetModalTextColor: secondaryTextColor,
			snippetModalBackground: backgroundColor,
			apCopyIcon: greyButtonBase,
			apCopyIconHover: Color(greyButtonBase).darken(0.5).hex(),
			apCopyIconActive: greyButtonBase,
			apInputBorderColorDark: "#343434",
			apFooterTextColor: secondaryTextColor,
			disabledInput: disabledTextColor,
			modalBorderColor: "#000000",
			inputBorderColor: "#86b7fe",
			inputBorderBoxShadow: "#0d6efd40",
			scoreBarLevel1: "#DC3545",
			scoreBarLevel2: "#F88F00",
			scoreBarLevel3: "#FFBB00",
			scoreBarLevel4: "#98C16F",
			scoreBarLevel5: "#6DCE49",
			apMedGrey: "#D1D1D1",
			apYellow: "#FFBE2B",
			apBlue: "#00AAF8",
			apGreen: "#6DCE49",
			apBlack: "#000000",
			trialColor: "#0A6C28",
			trialBackground: "#C9FFD6",
			labelBackground: "#C9E0FF",
			labelBorder: primarySiteColor,
			proLabelBackground: "#C9FFD6",
			proLabelBorder: "#0A6C28",
			apButtonRed: "#db3837",
			loadingBarColor: "#4caf50",
			loadingBarBackgroundColor: greyButtonBase,
			apProgressBarBGColor: "#ffffff4d",
			stepActive: "#302FC1",
			stepInactive: greyButtonBase
		}
	};

	return theme;
};

export type ThemeColors = keyof (apTheme)["colors"];
export default getTheme;
