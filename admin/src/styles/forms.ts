import styled, { css } from "styled-components";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
	faEye,
	faEyeSlash,
	faTrashCan,
	faUser,
	faPenToSquare,
	faCaretLeft,
	faClone,
	faImage,
	faCircleXmark,
	faXmark,
	faSpinner,
	faCircleQuestion,
	faAngleRight,
	faGear,
	faCircleMinus,
	faCheckCircle
} from "@fortawesome/free-solid-svg-icons";

const FormBox = styled.div`
	color: ${(props) => props.theme.colors.apFormText};
	font-family: ${(props) => props.theme.fonts.family};
	margin-left: 2rem;
	margin-right: 2rem;
	position: relative;
	text-align: center;
	font-family: ${(props) => props.theme.fonts.family};

	@media only screen and (max-width: 767px) {
		position: initial;
		top: initial;
		-webkit-transform: initial;
		-ms-transform: initial;
		transform: initial;
		margin-top: 2rem;
		margin-bottom: 2rem;
	}
`;

const HeadingText = styled.h1`
	font-size: 2.2rem;
	font-weight: bold;
	margin-bottom: 0rem;
	color: ${(props) => props.theme.colors.apFormText};
	font-family: ${(props) => props.theme.fonts.family};
`;

const HeadingTextH2 = styled.h2`
	font-size: 1.6rem;
	font-weight: bold;
	margin-bottom: 0rem;
	color: ${(props) => props.theme.colors.apFormText};
	font-family: ${(props) => props.theme.fonts.family};
`;

const SubheadingText = styled.div`
	color: ${(props) => props.theme.colors.apFormText};
	font-family: ${(props) => props.theme.fonts.family};
`;

const BodyText = styled.div<{disabled?: boolean; resize?: boolean}>`
	font-size: 1rem;
	margin-bottom: 1rem;
	color: ${(props) => props.theme.colors.apFormText};
	font-family: ${(props) => props.theme.fonts.family};

	${(props: {disabled?: boolean}) =>
		props.disabled &&
		css`
			color: ${(props) => props.theme.colors.apThirdButtonActive};
		`}

	@media only screen and (max-width: 1099px) and (min-width: 768px) {
		${(props) => props.resize && "font-size: 0.9rem;"}
	}
`;

const FooterText = styled.div`
	width: fit-content;
	margin-right: auto;
	margin-left: auto;
	margin-bottom: 1rem;
	text-align: center;
	left: 0;
	right: 0;
	font-size: 1rem;
	color: ${(props) => props.theme.colors.apFooterTextColor};
	font-family: ${(props) => props.theme.fonts.family};
	padding: 0 3.5rem;

	${(props: {auth?: boolean}) =>
		props.auth &&
		css`
			margin-right: 3rem;
			position: absolute;
			bottom: 1rem;
		`}

	.link {
		margin-left: 1rem;

		a {
			color: ${(props) => props.theme.colors.apFooterTextColor};
			text-decoration: none;
		}
	}

	@media only screen and (max-width: 767px) {
		position: initial;

		${(props: {auth?: boolean}) =>
			props.auth &&
			css`
				margin-right: auto;
				position: absolute;
			`}
	}
`;

const CustomInput = styled.input`
	border: 1px solid ${(props) => props.theme.colors.apInputBackground};
	background-color: ${(props) => props.theme.colors.apInputBackground};
	color: ${(props) => props.theme.colors.apInputColor};
	width: 100%;
	border-radius: 10px;
	padding: 1rem;

	&:disabled {
		background-color: ${(props) => props.theme.colors.apInputBackground};
		color: ${(props) => props.theme.colors.disabledInput};
	}

	&:focus {
		background-color: ${(props) => props.theme.colors.apBackgroundColor};
		color: ${(props) => props.theme.colors.apInputColor};
	}
`;

const CustomTextarea = styled.textarea`
	border: 1px solid ${(props) => props.theme.colors.apInputBackground};
	background-color: ${(props) => props.theme.colors.apInputBackground};
	color: ${(props) => props.theme.colors.apInputColor};
	width: 100%;
	border-radius: 10px;
	padding: 1rem;
	resize: none;
	height: 100px;

	&:disabled {
		background-color: ${(props) => props.theme.colors.apInputBackground};
		color: ${(props) => props.theme.colors.disabledInput};
	}

	&:focus {
		background-color: ${(props) => props.theme.colors.apBackgroundColor};
		color: ${(props) => props.theme.colors.apInputColor};
	}
`;

const ReadOnlyInput = styled.input`
	border: 1px solid ${(props) => props.theme.colors.apBackgroundColor};
	background-color: ${(props) => props.theme.colors.apBackgroundColor};
	color: ${(props) => props.theme.colors.apInputColor};
	width: 60px;
	text-align: center;
	border-radius: 10px;
	padding: 0.5rem;
	cursor: default;
`;

const CustomLabel = styled.label`
	text-align: center;

	a {
		color: ${(props) => props.theme.colors.apThirdButtonColor};
		text-decoration: none;
		font-weight: bold;
	}
`;

const CustomWarningLabel = styled(CustomLabel)`
	color: ${(props) => props.theme.colors.apError};

	a {
		color: ${(props) => props.theme.colors.apError};
	}
`;

const ConfirmationBoxWrapper = styled.div`
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	z-index: 1;
`;

const ConfirmationBox = styled.div`
	font-family: ${(props) => props.theme.fonts.family};
	border: 1px solid ${(props) => props.theme.colors.apSuccess};
	background-color: ${(props) => props.theme.colors.apSuccess};
	color: ${(props) => props.theme.colors.apWhite};
	border-radius: 10px;
	padding: 1rem 1.5rem;
	font-size: 0.9rem;
`;

const CustomAlert = styled.div`
	font-family: ${(props) => props.theme.fonts.family};
	border: 1px solid ${(props) => props.theme.colors.apError};
	background-color: ${(props) => props.theme.colors.apError};
	color: ${(props) => props.theme.colors.apWhite};
	border-radius: 10px;
	padding: 1rem 1.5rem;
	font-size: 0.9rem;
`;

const EyeIcon = styled(FontAwesomeIcon).attrs({ icon: faEye })`
	font-size: 0.9rem;
	cursor: pointer;
	color: ${(props) => props.theme.colors.apFormText};
	float: right;
	margin-right: 2.25rem;
	margin-top: -2.25rem;
`;

const UserIcon = styled(FontAwesomeIcon).attrs({ icon: faUser })`
	font-size: 1rem;
	margin-right: 5px;
`;

const GearIcon = styled(FontAwesomeIcon).attrs({ icon: faGear })`
	font-size: 1rem;
	margin-right: 5px;
`;

const HelpIcon = styled(FontAwesomeIcon).attrs({ icon: faCircleQuestion })`
	font-size: 1.2rem;
	vertical-align: sub;
	margin-right: 5px;
`;

const CaretLeft = styled(FontAwesomeIcon).attrs({ icon: faCaretLeft })`
	font-size: 1rem;
`;

const AngleRight = styled(FontAwesomeIcon).attrs({ icon: faAngleRight })`
	font-size: 1.2rem;
	cursor: pointer;
`;

const AcceptInvite = styled(FontAwesomeIcon).attrs({ icon: faCheckCircle })`
	font-size: 1.75rem;
	cursor: pointer;
	color: ${(props) => props.theme.colors.apSuccess};
	margin-right: 0.75rem;
`;

const RejectInvite = styled(FontAwesomeIcon).attrs({ icon: faCircleXmark })`
	font-size: 1.75rem;
	cursor: pointer;
	color: ${(props) => props.theme.colors.apError};
`;

const TrashIcon = styled(FontAwesomeIcon).attrs({ icon: faTrashCan })`
	font-size: 1.2rem;
	cursor: pointer;
`;

const DeleteIcon = styled(FontAwesomeIcon).attrs({ icon: faTrashCan })`
	font-size: 1.2rem;
	cursor: pointer;
	position: absolute;
	height: 18px;
	background: ${(props) => props.theme.colors.apWhite};
	color: ${(props) => props.theme.colors.apBlack};
	padding: 3px;
	border-radius: 5px;
	right: 1rem;
	bottom: 1rem;
`;

const TrashIconGrey = styled(FontAwesomeIcon).attrs({ icon: faTrashCan })`
	font-size: 1.2rem;
	opacity: 0.3;
`;

const RemoveButton = styled(FontAwesomeIcon).attrs({ icon: faCircleMinus })`
	font-size: 1.2rem;
	cursor: pointer;
	color: ${(props) => props.theme.colors.apError};
`;

const CloneIconDiv = styled.div`
	position: absolute;
	width: 100%;
	padding-top: 10px;
	padding-right: 12px;
`;

const CloneIcon = styled(FontAwesomeIcon).attrs({ icon: faClone })`
	font-size: 1.2rem;
	cursor: pointer;
	color: ${(props) => props.theme.colors.apCopyIcon};

	&:hover {
		color: ${(props) => props.theme.colors.apCopyIconHover};
	}
	&:active {
		color: ${(props) => props.theme.colors.apCopyIconActive};
	}
`;

const ImageIcon = styled(FontAwesomeIcon).attrs({ icon: faImage })`
	font-size: 1rem;
	color: ${(props) => props.theme.colors.apThirdButtonActive};
	margin: 15px 5px 5px 5px;

	${(props: {landscape?: boolean}) =>
		props.landscape &&
		css`
			margin: 5px;
		`}
`;

const CircleXmarkIcon = styled(FontAwesomeIcon).attrs({ icon: faCircleXmark })`
	font-size: 1.2rem;
	cursor: pointer;
	color: ${(props) => props.theme.colors.apFormText};
	float: right;
	margin-right: -1.2rem;
	margin-top: -1.2rem;
`;

const XmarkIcon = styled(FontAwesomeIcon).attrs({ icon: faXmark })`
	font-size: 1.5rem;
	cursor: pointer;
	color: ${(props) => props.theme.colors.apFormText};
	right: 25px;
	margin-top: -40px;
	position: absolute;
`;

const XmarkIconWhite = styled(FontAwesomeIcon).attrs({ icon: faXmark })<{modedisplay?: string}>`
	font-size: 1.4rem;
	cursor: pointer;
	color: ${(props) => props.theme.colors.apWhite};
	margin-left: 10px;
	vertical-align: bottom;
	display: ${(props) => (props.modedisplay ? "block" : "none")};
	float: right;
`;

const SpinnerIcon = styled(FontAwesomeIcon).attrs({ icon: faSpinner })`
	font-size: 1.2rem;
	margin-top: 10px;
	color: ${(props) => props.theme.colors.apFormText};
`;

const PenIcon = styled(FontAwesomeIcon).attrs({ icon: faPenToSquare })`
	font-size: 1.2rem;
	cursor: pointer;
`;

const EyeSlashIcon = styled(FontAwesomeIcon).attrs({ icon: faEyeSlash })`
	font-size: 0.9rem;
	cursor: pointer;
	color: ${(props) => props.theme.colors.apFormText};
	float: right;
	margin-right: 2.25rem;
	margin-top: -2.25rem;
`;

export {
	FormBox,
	HeadingText,
	HeadingTextH2,
	SubheadingText,
	BodyText,
	FooterText,
	CustomInput,
	CustomTextarea,
	CustomLabel,
	ReadOnlyInput,
	CustomWarningLabel,
	ConfirmationBoxWrapper,
	ConfirmationBox,
	CustomAlert,
	CaretLeft,
	AngleRight,
	EyeIcon,
	EyeSlashIcon,
	UserIcon,
	GearIcon,
	HelpIcon,
	TrashIcon,
	TrashIconGrey,
	DeleteIcon,
	RemoveButton,
	PenIcon,
	CloneIconDiv,
	CloneIcon,
	ImageIcon,
	CircleXmarkIcon,
	XmarkIcon,
	XmarkIconWhite,
	SpinnerIcon,
	AcceptInvite,
	RejectInvite
};
