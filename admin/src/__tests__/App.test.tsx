import React from "react";
import { App } from "../App";
import { render, cleanup, act } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import { RecoilRoot } from "recoil";

afterEach(cleanup);

jest.mock("logrocket", () => ({
	default: {
		init: jest.fn()
	}
}));

it("renders without crashing", async () => {
	await act(async () => {
		// Use the async variant of act()

		render(
			<RecoilRoot>
				<App />
			</RecoilRoot>
		);
	});
});
