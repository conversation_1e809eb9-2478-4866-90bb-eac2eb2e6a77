export function arraysAreEqualIgnoreOrder(a: string[], b: string[]): boolean {
	if (a.length !== b.length) {
		return false;
	}

	const sortedA = a.slice().sort();
	const sortedB = b.slice().sort();

	for (let i = 0; i < sortedA.length; i++) {
		if (sortedA[i] !== sortedB[i]) {
			return false;
		}
	}

	return true;
}

export const getImageSize = (videoWidthPx?: number, videoHeightPx?: number) => {
	let imageWidthPx = 500;
	let imageHeightPx = 750;

	if (videoWidthPx && videoHeightPx) {
		const aspectRatio = videoWidthPx / videoHeightPx;
		if (aspectRatio > 1) {
			imageWidthPx = 750;
			imageHeightPx = 500;
		} else if (aspectRatio === 1) {
			imageHeightPx = 500;
		}
	}

	return { imageWidthPx, imageHeightPx };
};

export const compareImpressions = (totalImpressionsCurrentCycle?: number, maxImpressionsPerCycle?: number) => {
	let reachedLimit = false;

	if (
		totalImpressionsCurrentCycle &&
		maxImpressionsPerCycle &&
		totalImpressionsCurrentCycle >= maxImpressionsPerCycle
	) {
		reachedLimit = true;
	}

	return reachedLimit;
};

export const checkOidcDomain = (email: string, domainsString: string) => {
	const domains = domainsString.split(",");
	return domains.some((domain) => email.endsWith(domain));
};
