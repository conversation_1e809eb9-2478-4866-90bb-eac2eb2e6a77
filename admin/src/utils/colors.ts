import Color from "color";

export function isValidColor(color: string | undefined | null): color is string {
	if (!color) return false;

	try {
		Color(color);
		return true;
	} catch {
		return false;
	}
}

export async function handleEyeDropPick(): Promise<string | undefined> {
	if (!window.EyeDropper) return;

	const eyeDropper = new window.EyeDropper();

	try {
		const result = await eyeDropper.open();
		return result.sRGBHex as string;
	} catch (err) {
		//Most likely user clicked esc
	}
}
