import React from "react";
import { createRoot } from "react-dom/client";
import { App } from "./App";
import { LanguageContextProvider } from "./components/contexts/LanguageContext";
import TagManager from "react-gtm-module";
import { validateEnvVariables } from "./utils/envValidator";
import { RecoilRoot } from "recoil";

// Importing CSS
import "bootstrap/dist/css/bootstrap.min.css";
import "react-loading-skeleton/dist/skeleton.css";
import "highlight.js/styles/stackoverflow-dark.css";

validateEnvVariables();

// Log the release tag on application load
console.info("RELEASE_TAG:", process.env.RELEASE_TAG);

const gtmId = process.env.GTM_ID;

if (gtmId && gtmId.trim() !== "") {
	const tagManagerArgs = {
		gtmId
	};
	TagManager.initialize(tagManagerArgs);
}

const apContainer = document.getElementById("root");

if (!apContainer) {
	throw new Error("Failed to locate DOM element with id 'root'");
}

const root = createRoot(apContainer);
root.render(
	<LanguageContextProvider>
		<RecoilRoot>
			<App />
		</RecoilRoot>
	</LanguageContextProvider>
);
